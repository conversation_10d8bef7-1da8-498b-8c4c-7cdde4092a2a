"use server";

import { auth } from "@clerk/nextjs/server";
import { and, eq, inArray } from "drizzle-orm";
import { revalidatePath } from "next/cache";

import db from "@/db/drizzle";
import { getUserProgress, getUserSubscription } from "@/db/queries";
import {
  challengeProgress,
  challenges,
  userProgress,
  challengeOptions as challengeOptionsSchema,

  // NEW: speed-round tables
  challengeSessions,
  challengeAttempts,
} from "@/db/schema";
import { validateMatch } from "./validate-match"; // Import the new function

export const upsertChallengeProgress = async (
  challengeId: number,
  option1Id?: number | string,
  option2Id?: number
) => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });

  if (!challenge) {
    throw new Error("Challenge not found");
  }

  const lessonId = challenge.lessonId;

  const existingChallengeProgress = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  const isPractice = !!existingChallengeProgress;

  // 🔧 Hearts gate: ignore for non-graded/explainer items
  if (
    currentUserProgress.hearts === 0 &&
    !isPractice &&
    !userSubscription?.isActive &&
    // @ts-ignore — columns exist on table
    !(challenge as any).isNonGraded &&
    challenge.type !== "EXPLAINER"
  ) {
    console.log(
      "upsertChallengeProgress: No hearts left and not practice mode (graded item)."
    );
    return { error: "hearts" };
  }

  // ✅ Handle non-graded explainer-type explicitly (no points/hearts changes)
  // This covers both the explicit EXPLAINER type and any future items flagged non-graded.
  // @ts-ignore — columns exist on table
  const isNonGraded = (challenge as any).isNonGraded === true;
  if (challenge.type === "EXPLAINER" || isNonGraded) {
    console.log(
      "Non-graded / EXPLAINER challenge detected — mark complete, no scoring."
    );
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({ completed: true })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");

    console.log("Explainer progress recorded (no hearts/points changed).");
    return; // ⬅️ Exit early to avoid scoring logic
  }

  // Logic for MATCHING challenge type
  if (challenge.type === "MATCHING") {
    console.log("Matching challenge detected");
    console.log("option1Id:", option1Id, "option2Id:", option2Id);

    if (typeof option1Id !== "number" || typeof option2Id !== "number") {
      throw new Error(
        `Matching options are required and must be numbers. Received option1Id: ${option1Id}, option2Id: ${option2Id}`
      );
    }

    const result = await validateMatch(option1Id, option2Id);
    console.log("validateMatch result:", result);

    if (result.error) {
      return result;
    }

    // Update challenge progress after successful validation
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({ completed: true })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points and hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  } else if (challenge.type === "TAP_WHAT_YOU_HEAR") {
    console.log("Tap What You Hear challenge detected");
    console.log("option1Id:", option1Id);

    if (typeof option1Id !== "number") {
      throw new Error(
        `An option is required for Tap What You Hear and must be a number. Received option1Id: ${option1Id}`
      );
    }

    const correctOption = await db.query.challengeOptions.findFirst({
      where: and(
        eq(challengeOptionsSchema.challengeId, challengeId),
        eq(challengeOptionsSchema.correct, true)
      ),
    });

    if (!correctOption || correctOption.id !== option1Id) {
      return { error: "incorrect" };
    }

    // Update challenge progress
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({
          completed: true,
        })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points and hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  } else if (challenge.type === "IMAGE_AUDIO_SELECT") {
    console.log("Image and Audio Select challenge detected");
    console.log("option1Id:", option1Id);

    if (typeof option1Id !== "number") {
      throw new Error(
        `An option is required for Image and Audio Select and must be a number. Received option1Id: ${option1Id}`
      );
    }

    const correctOption = await db.query.challengeOptions.findFirst({
      where: and(
        eq(challengeOptionsSchema.challengeId, challengeId),
        eq(challengeOptionsSchema.correct, true)
      ),
    });

    if (!correctOption || correctOption.id !== option1Id) {
      return { error: "incorrect" };
    }

    // Update challenge progress
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({
          completed: true,
        })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points and hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  } else if (challenge.type === "DRAG_AND_DROP") {
    console.log("Drag and Drop challenge detected");
    console.log("option1Id:", option1Id);

    if (typeof option1Id !== "string") {
      throw new Error(
        `An option is required for Drag and Drop and must be a string. Received option1Id: ${option1Id}`
      );
    }

    const selectedOptions: number[] = (option1Id as string)
      .split(",")
      .map(Number);

    console.log("Selected options from frontend:", selectedOptions);

    const challengeOptions = await db.query.challengeOptions.findMany({
      where: eq(challengeOptionsSchema.challengeId, challengeId),
      orderBy: (challengeOptionsSchema, { asc }) => [
        asc(challengeOptionsSchema.sequence),
      ],
    });

    const correctOrder = challengeOptions.map((option) => option.id);
    const challengeZoneSequences = challengeOptions
      .filter((option) => !selectedOptions.includes(option.id))
      .map((option) => option.sequence);

    console.log("Correct order from database:", correctOrder);
    console.log("Challenge zone sequences:", challengeZoneSequences);

    const isCorrectOrder = selectedOptions.every(
      (id, index) => id === correctOrder[index]
    );

    const hasIncorrectOption = challengeZoneSequences.some((seq) => seq !== 99);

    if (!isCorrectOrder || hasIncorrectOption) {
      return { error: "incorrect" };
    }

    // Update challenge progress
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({
          completed: true,
        })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points and hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  } else if (challenge.type === "FILL_IN_THE_BLANK") {
    console.log("Fill in the Blank challenge detected");
    console.log("option1Id:", option1Id);

    if (typeof option1Id !== "number") {
      throw new Error(
        `An option is required for Fill in the Blank and must be a number. Received option1Id: ${option1Id}`
      );
    }

    const correctOption = await db.query.challengeOptions.findFirst({
      where: and(
        eq(challengeOptionsSchema.challengeId, challengeId),
        eq(challengeOptionsSchema.correct, true)
      ),
    });

    if (!correctOption || correctOption.id !== option1Id) {
      return { error: "incorrect" };
    }

    // Update challenge progress
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({
          completed: true,
        })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points and hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  }
  // ADDED: "SPEAK_THIS" logic (to mirror client-side handling)
  else if (challenge.type === "SPEAK_THIS") {
    console.log("Speak This challenge detected");

    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({ completed: true })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  }
  // ADDED: "SPEAK_THIS_ADVANCED" logic
  else if (challenge.type === "SPEAK_THIS_ADVANCED") {
    console.log("Speak This Advanced challenge detected");

    // If there's no "option" needed, we simply mark it as completed.
    // If you had to check correctness, you'd do that here.

    if (existingChallengeProgress) {
      // Already attempted => practice or repeated run
      await db
        .update(challengeProgress)
        .set({ completed: true })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      // First time completing this challenge
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points, maybe hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  }
  // ADDED: "SPEAK_SEQUENCE" logic (new challenge type)
  else if (challenge.type === "SPEAK_SEQUENCE") {
    console.log("Speak Sequence challenge detected");
    // Server expects to be called only when the WHOLE sequence is done,
    // but we support three flexible payloads to align with possible client flows:
    //  A) option1Id === 'complete' | 'done' | 'all_matched'  -> mark complete
    //  B) option1Id is a comma-separated list of option IDs   -> verify membership & count, then complete
    //  C) option1Id is a single number (one item)             -> treat as partial; no completion

    const normalizeFlag = (v: unknown) =>
      typeof v === "string" ? v.trim().toLowerCase() : "";

    const flag = normalizeFlag(option1Id);

    const finalize = async () => {
      if (existingChallengeProgress) {
        await db
          .update(challengeProgress)
          .set({ completed: true })
          .where(eq(challengeProgress.id, existingChallengeProgress.id));
      } else {
        await db.insert(challengeProgress).values({
          challengeId,
          userId,
          completed: true,
        });
      }
      await db
        .update(userProgress)
        .set({
          hearts: Math.min(currentUserProgress.hearts + 1, 5),
          points: currentUserProgress.points + 10,
        })
        .where(eq(userProgress.userId, userId));

      revalidatePath(`/lesson/${lessonId}`);
      revalidatePath("/learn");
      revalidatePath("/quests");
      revalidatePath("/leaderboard");
    };

    // Case A: explicit completion flag
    if (flag === "complete" || flag === "done" || flag === "all_matched") {
      console.log("[SPEAK_SEQUENCE] explicit completion flag received.");
      await finalize();
    }
    // Case B: comma-separated list of option IDs to verify
    else if (typeof option1Id === "string" && option1Id.includes(",")) {
      const ids = option1Id
        .split(",")
        .map((s) => s.trim())
        .filter(Boolean)
        .map((s) => Number(s))
        .filter((n) => !Number.isNaN(n));

      if (ids.length === 0) {
        return { error: "invalid_payload" };
      }

      // Fetch expected options for this challenge
      const expectedOpts = await db.query.challengeOptions.findMany({
        where: eq(challengeOptionsSchema.challengeId, challengeId),
        columns: { id: true },
        orderBy: (co, { asc }) => [asc(co.sequence), asc(co.id)],
      });

      const expectedCount =
        // @ts-ignore — columns exist on table
        (challenge as any).speakItemCount ??
        // @ts-ignore (snake case safety)
        (challenge as any).speak_item_count ??
        10;

      // Validate: all provided IDs belong to this challenge
      const expectedIds = new Set(expectedOpts.map((o) => o.id));
      const allBelong = ids.every((id) => expectedIds.has(id));

      if (!allBelong) {
        console.warn(
          "[SPEAK_SEQUENCE] Provided IDs include items not in this challenge."
        );
        return { error: "incorrect" };
      }

      // Validate count matches expected target
      if (ids.length !== expectedCount) {
        console.warn(
          `[SPEAK_SEQUENCE] Provided ${ids.length} ids but expected ${expectedCount}.`
        );
        return { error: "incorrect" };
      }

      // Passed sanity checks → mark completed
      console.log("[SPEAK_SEQUENCE] IDs verified; marking completed.");
      await finalize();
    }
    // Case C: single number (partial progress — do nothing server-side)
    else if (typeof option1Id === "number") {
      console.log(
        "[SPEAK_SEQUENCE] single item event received; treating as partial (no completion). option1Id=",
        option1Id
      );
      // No DB changes here; client controls step-to-step gating.
      return { status: "pending" as const };
    } else {
      // If nothing recognizable, assume client will retry
      console.log("[SPEAK_SEQUENCE] Unrecognized payload for completion.");
      return { error: "invalid_payload" };
    }
  }
  // NEW: SPEED ROUND logic (SPEED_SPOT_MISTAKE & SPEED_MATCH_AUDIO)
  else if (
    challenge.type === "SPEED_SPOT_MISTAKE" ||
    challenge.type === "SPEED_MATCH_AUDIO"
  ) {
    console.log(`[SPEED] challenge detected: ${challenge.type}`);
    // We accept either a compact colon-encoded string or JSON payload.
    // Compact examples:
    //  "start" or "start:mobile"
    //  "attempt:<sessionId>:<speedItemId>:<chosen>:<isCorrect>:<latencyMs?>"
    //    chosen = "match" | "mismatch" | "option_a" | "option_b" | "option_c" | "option_d"
    //    isCorrect = "1" | "0" | "true" | "false"
    //  "end:<sessionId>"
    //
    // JSON example:
    //  {"action":"start","deviceType":"mobile"}
    //  {"action":"attempt","sessionId":12,"speedItemId":34,"chosen":"match","isCorrect":true,"latencyMs":280}
    //  {"action":"end","sessionId":12}

    type SpeedCmd =
      | { action: "start"; deviceType?: string }
      | {
          action: "attempt";
          sessionId: number;
          speedItemId: number;
          chosen: string;
          isCorrect: boolean;
          latencyMs?: number | null;
        }
      | { action: "end"; sessionId: number };

    const parseSpeed = (raw: unknown): SpeedCmd | null => {
      if (typeof raw !== "string") return null;

      // JSON payload?
      if (raw.trim().startsWith("{")) {
        try {
          const j = JSON.parse(raw.trim());
          if (!j || typeof j !== "object") return null;
          if (j.action === "start") {
            return { action: "start", deviceType: j.deviceType ?? undefined };
          }
          if (j.action === "attempt") {
            const sid = Number(j.sessionId);
            const iid = Number(j.speedItemId);
            if (!sid || !iid) return null;
            return {
              action: "attempt",
              sessionId: sid,
              speedItemId: iid,
              chosen: String(j.chosen ?? ""),
              isCorrect:
                j.isCorrect === true ||
                j.isCorrect === "true" ||
                j.isCorrect === 1 ||
                j.isCorrect === "1",
              latencyMs:
                j.latencyMs === undefined || j.latencyMs === null
                  ? null
                  : Number(j.latencyMs),
            };
          }
          if (j.action === "end") {
            const sid = Number(j.sessionId);
            if (!sid) return null;
            return { action: "end", sessionId: sid };
          }
          return null;
        } catch {
          return null;
        }
      }

      // Colon format
      const parts = raw.split(":").map((s) => s.trim());
      if (parts.length >= 1) {
        if (parts[0].toLowerCase() === "start") {
          return { action: "start", deviceType: parts[1] ?? undefined };
        }
        if (parts[0].toLowerCase() === "attempt" && parts.length >= 5) {
          const sessionId = Number(parts[1]);
          const speedItemId = Number(parts[2]);
          const chosen = parts[3];
          const isCorrect =
            parts[4] === "1" ||
            parts[4] === "true" ||
            parts[4] === "TRUE" ||
            parts[4] === "True";
          const latencyMs =
            parts.length >= 6 && parts[5] !== "" ? Number(parts[5]) : undefined;
          if (!sessionId || !speedItemId) return null;
          return {
            action: "attempt",
            sessionId,
            speedItemId,
            chosen,
            isCorrect,
            latencyMs: Number.isFinite(latencyMs!) ? latencyMs : null,
          };
        }
        if (parts[0].toLowerCase() === "end" && parts.length >= 2) {
          const sessionId = Number(parts[1]);
          if (!sessionId) return null;
          return { action: "end", sessionId };
        }
      }
      return null;
    };

    const cmd = parseSpeed(option1Id);
    if (!cmd) {
      console.warn("[SPEED] Invalid speed payload:", option1Id);
      return { error: "invalid_payload" };
    }

    // START
    if (cmd.action === "start") {
      console.log("[SPEED] start session", { deviceType: cmd.deviceType });
      const inserted = await db
        .insert(challengeSessions)
        .values({
          challengeId,
          userId,
          deviceType: cmd.deviceType ?? null,
          startedAt: new Date(),
        } as any)
        .returning();
      const session = inserted[0];

      // Do NOT mark the challenge completed on start.
      revalidatePath(`/lesson/${lessonId}`);
      return { sessionId: session.id };
    }

    // ATTEMPT
    if (cmd.action === "attempt") {
      console.log("[SPEED] attempt", cmd);

      // ensure session belongs to same challenge+user
      const sess = await db.query.challengeSessions.findFirst({
        where: eq(challengeSessions.id, cmd.sessionId),
        columns: {
          id: true,
          userId: true,
          challengeId: true,
        },
      });
      if (!sess || sess.userId !== userId || sess.challengeId !== challengeId) {
        console.warn("[SPEED] attempt rejected: session mismatch");
        return { error: "invalid_session" };
      }

      const row = await db
        .insert(challengeAttempts)
        .values({
          sessionId: cmd.sessionId,
          speedItemId: cmd.speedItemId,
          chosen: cmd.chosen,
          isCorrect: cmd.isCorrect,
          latencyMs: cmd.latencyMs ?? null,
          startedAt: new Date(),
          answeredAt: new Date(),
        } as any)
        .returning();

      // No completion yet
      return { attemptId: row[0].id, ok: true };
    }

    // END
    if (cmd.action === "end") {
      console.log("[SPEED] end session", cmd.sessionId);
      const sess = await db.query.challengeSessions.findFirst({
        where: eq(challengeSessions.id, cmd.sessionId),
        columns: {
          id: true,
          userId: true,
          challengeId: true,
          startedAt: true,
        },
      });
      if (!sess || sess.userId !== userId || sess.challengeId !== challengeId) {
        console.warn("[SPEED] end rejected: session mismatch");
        return { error: "invalid_session" };
      }

      // pull attempts
      const attempts = await db.query.challengeAttempts.findMany({
        where: eq(challengeAttempts.sessionId, cmd.sessionId),
        orderBy: (ca, { asc }) => [asc(ca.id)],
        columns: {
          id: true,
          isCorrect: true,
          latencyMs: true,
          startedAt: true,
          answeredAt: true,
        },
      });

      const itemsSeen = attempts.length;
      const itemsCorrect = attempts.filter((a) => a.isCorrect).length;
      const score = itemsCorrect; // simple: 1 per correct (tweak later)

      // compute max streak
      let streak = 0;
      let maxStreak = 0;
      for (const a of attempts) {
        streak = a.isCorrect ? streak + 1 : 0;
        if (streak > maxStreak) maxStreak = streak;
      }

      // ✅ Type-safe duration using milliseconds (avoid new Date(null))
      const firstMs =
        attempts[0]?.startedAt instanceof Date
          ? attempts[0].startedAt.getTime()
          : sess.startedAt instanceof Date
          ? sess.startedAt.getTime()
          : Date.now();

      const lastMs =
        attempts.length > 0 &&
        attempts[attempts.length - 1]?.answeredAt instanceof Date
          ? attempts[attempts.length - 1].answeredAt!.getTime()
          : Date.now();

      const durationMs = Math.max(0, lastMs - firstMs);

      const updated = await db
        .update(challengeSessions)
        .set({
          endedAt: new Date(),
          durationMs,
          score,
          itemsSeen,
          itemsCorrect,
          streakMax: maxStreak,
        } as any)
        .where(eq(challengeSessions.id, cmd.sessionId))
        .returning();

      // Mark challenge completed on first run (or again in practice)
      if (existingChallengeProgress) {
        await db
          .update(challengeProgress)
          .set({ completed: true })
          .where(eq(challengeProgress.id, existingChallengeProgress.id));
      } else {
        await db.insert(challengeProgress).values({
          challengeId,
          userId,
          completed: true,
        });
      }

      // Award points & hearts (simple policy; adjust if you want)
      const pointsToAdd = Math.max(10, itemsCorrect * 2); // at least 10, or 2 per correct
      await db
        .update(userProgress)
        .set({
          hearts: Math.min(currentUserProgress.hearts + 1, 5),
          points: currentUserProgress.points + pointsToAdd,
        })
        .where(eq(userProgress.userId, userId));

      revalidatePath(`/lesson/${lessonId}`);
      revalidatePath("/learn");
      revalidatePath("/quests");
      revalidatePath("/leaderboard");

      return {
        session: updated[0],
        itemsSeen,
        itemsCorrect,
        score,
        streakMax: maxStreak,
        pointsAwarded: pointsToAdd,
      };
    }

    // Should not reach here
    return { error: "invalid_payload" };
  }
  // End of special challenge types
  else {
    if (isPractice) {
      console.log("upsertChallengeProgress: Practice mode detected.");
      await db
        .update(challengeProgress)
        .set({
          completed: true,
        })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));

      await db
        .update(userProgress)
        .set({
          hearts: Math.min(currentUserProgress.hearts + 1, 5),
          points: currentUserProgress.points + 10,
        })
        .where(eq(userProgress.userId, userId));

      revalidatePath("/learn");
      revalidatePath("/lesson");
      revalidatePath("/quests");
      revalidatePath("/leaderboard");
      revalidatePath(`/lesson/${lessonId}`);
      return;
    }

    console.log("upsertChallengeProgress: Inserting new challenge progress.");
    await db.insert(challengeProgress).values({
      challengeId,
      userId,
      completed: true,
    });

    await db
      .update(userProgress)
      .set({
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath("/learn");
    revalidatePath("/lesson");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
    revalidatePath(`/lesson/${lessonId}`);
  }

  console.log("Challenge progress upserted successfully");

  revalidatePath("/learn");
  revalidatePath("/lesson");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");
  revalidatePath(`/lesson/${lessonId}`);
};

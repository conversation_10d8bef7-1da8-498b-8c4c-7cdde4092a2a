"use server";

import { and, eq } from "drizzle-orm";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import { auth, currentUser } from "@clerk/nextjs/server";

import db from "@/db/drizzle";
import { POINTS_TO_REFILL } from "@/constants";
import {
  getCourseById,
  getUserProgress,
  getUserSubscription,
} from "@/db/queries";
import {
  challengeProgress,
  challenges,
  userProgress,
  challengeOptions as challengeOptionsSchema,
} from "@/db/schema";

export const upsertUserProgress = async (courseId: number) => {
  const { userId } = await auth();
  const user = await currentUser();

  if (!userId || !user) {
    throw new Error("Unauthorized");
  }

  const course = await getCourseById(courseId);

  if (!course) {
    throw new Error("Course not found");
  }

  if (!course.units.length || !course.units[0].lessons.length) {
    throw new Error("Course is empty");
  }

  const existingUserProgress = await getUserProgress();

  if (existingUserProgress) {
    await db.update(userProgress).set({
      activeCourseId: courseId,
      userName: user.firstName || "User",
      userImageSrc: user.imageUrl || "/mascot.svg",
    });

    revalidatePath("/courses");
    revalidatePath("/learn");
    redirect("/learn");
  }

  await db.insert(userProgress).values({
    userId,
    activeCourseId: courseId,
    userName: user.firstName || "User",
    userImageSrc: user.imageUrl || "/mascot.svg",
  });

  revalidatePath("/courses");
  revalidatePath("/learn");
  redirect("/learn");
};

export const reduceHearts = async (challengeId: number) => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });

  if (!challenge) {
    throw new Error("Challenge not found");
  }

  // 🚫 Never deduct hearts for non-graded/EXPLAINER items
  // @ts-ignore
  const isNonGraded = (challenge as any).isNonGraded === true;
  if (challenge.type === "EXPLAINER" || isNonGraded) {
    console.log("reduceHearts: Non-graded/EXPLAINER — skip heart deduction.");
    return { error: "non_graded" };
  }

  const lessonId = challenge.lessonId;

  const existingChallengeProgress = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  const isPractice = !!existingChallengeProgress;

  if (isPractice) {
    console.log("reduceHearts: Practice mode detected. No hearts deducted.");
    return { error: "practice" };
  }

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  if (userSubscription?.isActive) {
    console.log("reduceHearts: User subscription active. No hearts deducted.");
    return { error: "subscription" };
  }

  if (currentUserProgress.hearts === 0) {
    console.log("reduceHearts: No hearts left to deduct.");
    return { error: "hearts" };
  }

  await db
    .update(userProgress)
    .set({
      hearts: Math.max(currentUserProgress.hearts - 1, 0),
    })
    .where(eq(userProgress.userId, userId));

  revalidatePath("/shop");
  revalidatePath("/learn");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");
  revalidatePath(`/lesson/${lessonId}`);

  console.log("reduceHearts: Heart deducted successfully.");
  return { success: true };
};

export const refillHearts = async () => {
  const currentUserProgress = await getUserProgress();

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  if (currentUserProgress.hearts === 5) {
    throw new Error("Hearts are already full");
  }

  if (currentUserProgress.points < POINTS_TO_REFILL) {
    throw new Error("Not enough points");
  }

  await db
    .update(userProgress)
    .set({
      hearts: 5,
      points: currentUserProgress.points - POINTS_TO_REFILL,
    })
    .where(eq(userProgress.userId, currentUserProgress.userId));

  revalidatePath("/shop");
  revalidatePath("/learn");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");
};

export const updateMatchingProgress = async (
  challengeId: number,
  selectedOptionId: number
) => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });

  if (!challenge) {
    throw new Error("Challenge not found");
  }

  // Bypass hearts gate for non-graded/EXPLAINER
  // @ts-ignore
  const isNonGraded = (challenge as any).isNonGraded === true;

  const lessonId = challenge.lessonId;

  const existingChallengeProgress = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  if (
    currentUserProgress.hearts === 0 &&
    !existingChallengeProgress &&
    !userSubscription?.isActive &&
    challenge.type !== "EXPLAINER" &&
    !isNonGraded
  ) {
    return { error: "hearts" };
  }

  const correctOption = await db.query.challengeOptions.findFirst({
    where: and(
      eq(challengeOptionsSchema.challengeId, challengeId),
      eq(challengeOptionsSchema.correct, true)
    ),
  });

  const isMatchCorrect = correctOption?.id === selectedOptionId;

  if (!isMatchCorrect) {
    // No heart deductions for non-graded/EXPLAINER
    if (challenge.type === "EXPLAINER" || isNonGraded) {
      revalidatePath(`/lesson/${lessonId}`);
      return { error: "non_graded" };
    }

    await db
      .update(userProgress)
      .set({
        hearts: Math.max(currentUserProgress.hearts - 1, 0),
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    return { error: "incorrect" };
  }

  if (existingChallengeProgress) {
    await db
      .update(challengeProgress)
      .set({
        completed: true,
      })
      .where(eq(challengeProgress.id, existingChallengeProgress.id));
  } else {
    await db.insert(challengeProgress).values({
      challengeId,
      userId,
      completed: true,
    });
  }

  // No scoring for non-graded/EXPLAINER
  if (challenge.type !== "EXPLAINER" && !isNonGraded) {
    await db
      .update(userProgress)
      .set({
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));
  }

  revalidatePath("/learn");
  revalidatePath("/lesson");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");
  revalidatePath(`/lesson/${lessonId}`);

  return { success: true };
};

export const updateTapWhatYouHearProgress = async (
  challengeId: number,
  selectedOptionId: number
) => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });

  if (!challenge) {
    throw new Error("Challenge not found");
  }

  // Bypass hearts gate for non-graded/EXPLAINER
  // @ts-ignore
  const isNonGraded = (challenge as any).isNonGraded === true;

  const lessonId = challenge.lessonId;

  const existingChallengeProgress = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  if (
    currentUserProgress.hearts === 0 &&
    !existingChallengeProgress &&
    !userSubscription?.isActive &&
    challenge.type !== "EXPLAINER" &&
    !isNonGraded
  ) {
    return { error: "hearts" };
  }

  const correctOption = await db.query.challengeOptions.findFirst({
    where: and(
      eq(challengeOptionsSchema.challengeId, challengeId),
      eq(challengeOptionsSchema.correct, true)
    ),
  });

  const isCorrect = correctOption?.id === selectedOptionId;

  if (!isCorrect) {
    if (challenge.type === "EXPLAINER" || isNonGraded) {
      revalidatePath(`/lesson/${lessonId}`);
      return { error: "non_graded" };
    }

    await db
      .update(userProgress)
      .set({
        hearts: Math.max(currentUserProgress.hearts - 1, 0),
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    return { error: "incorrect" };
  }

  if (existingChallengeProgress) {
    await db
      .update(challengeProgress)
      .set({
        completed: true,
      })
      .where(eq(challengeProgress.id, existingChallengeProgress.id));
  } else {
    await db.insert(challengeProgress).values({
      challengeId,
      userId,
      completed: true,
    });
  }

  if (challenge.type !== "EXPLAINER" && !isNonGraded) {
    await db
      .update(userProgress)
      .set({
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));
  }

  revalidatePath("/learn");
  revalidatePath("/lesson");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");
  revalidatePath(`/lesson/${lessonId}`);

  return { success: true };
};

export const updateImageAudioSelectProgress = async (
  challengeId: number,
  selectedOptionId: number
) => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });

  if (!challenge) {
    throw new Error("Challenge not found");
  }

  // Bypass hearts gate for non-graded/EXPLAINER
  // @ts-ignore
  const isNonGraded = (challenge as any).isNonGraded === true;

  const lessonId = challenge.lessonId;

  const existingChallengeProgress = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  if (
    currentUserProgress.hearts === 0 &&
    !existingChallengeProgress &&
    !userSubscription?.isActive &&
    challenge.type !== "EXPLAINER" &&
    !isNonGraded
  ) {
    return { error: "hearts" };
  }

  const correctOption = await db.query.challengeOptions.findFirst({
    where: and(
      eq(challengeOptionsSchema.challengeId, challengeId),
      eq(challengeOptionsSchema.correct, true)
    ),
  });

  const isCorrect = correctOption?.id === selectedOptionId;

  if (!isCorrect) {
    if (challenge.type === "EXPLAINER" || isNonGraded) {
      revalidatePath(`/lesson/${lessonId}`);
      return { error: "non_graded" };
    }

    await db
      .update(userProgress)
      .set({
        hearts: Math.max(currentUserProgress.hearts - 1, 0),
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    return { error: "incorrect" };
  }

  if (existingChallengeProgress) {
    await db
      .update(challengeProgress)
      .set({
        completed: true,
      })
      .where(eq(challengeProgress.id, existingChallengeProgress.id));
  } else {
    await db.insert(challengeProgress).values({
      challengeId,
      userId,
      completed: true,
    });
  }

  if (challenge.type !== "EXPLAINER" && !isNonGraded) {
    await db
      .update(userProgress)
      .set({
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));
  }

  revalidatePath("/learn");
  revalidatePath("/lesson");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");
  revalidatePath(`/lesson/${lessonId}`);

  return { success: true };
};

export const updateDragAndDropProgress = async (
  challengeId: number,
  selectedOptionIds: string
) => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });

  if (!challenge) {
    throw new Error("Challenge not found");
  }

  // Bypass hearts gate for non-graded/EXPLAINER
  // @ts-ignore
  const isNonGraded = (challenge as any).isNonGraded === true;

  const lessonId = challenge.lessonId;

  const existingChallengeProgress = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  if (
    currentUserProgress.hearts === 0 &&
    !existingChallengeProgress &&
    !userSubscription?.isActive &&
    challenge.type !== "EXPLAINER" &&
    !isNonGraded
  ) {
    return { error: "hearts" };
  }

  const challengeOptions = await db.query.challengeOptions.findMany({
    where: eq(challengeOptionsSchema.challengeId, challengeId),
    orderBy: (challengeOptionsSchema, { asc }) => [
      asc(challengeOptionsSchema.sequence),
    ],
  });

  const selectedOptions: number[] = selectedOptionIds.split(",").map(Number);
  const correctOrder = challengeOptions.map((option) => option.id);
  const challengeZoneSequences = challengeOptions
    .filter((option) => !selectedOptions.includes(option.id))
    .map((option) => option.sequence);

  const isCorrect = selectedOptions.every(
    (id, index) => id === correctOrder[index]
  );
  const hasIncorrectOption = challengeZoneSequences.some((seq) => seq !== 99);

  if (!isCorrect || hasIncorrectOption) {
    if (challenge.type === "EXPLAINER" || isNonGraded) {
      revalidatePath(`/lesson/${lessonId}`);
      return { error: "non_graded" };
    }

    await db
      .update(userProgress)
      .set({
        hearts: Math.max(currentUserProgress.hearts - 1, 0),
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    return { error: "incorrect" };
  }

  if (existingChallengeProgress) {
    await db
      .update(challengeProgress)
      .set({
        completed: true,
      })
      .where(eq(challengeProgress.id, existingChallengeProgress.id));
  } else {
    await db.insert(challengeProgress).values({
      challengeId,
      userId,
      completed: true,
    });
  }

  if (challenge.type !== "EXPLAINER" && !isNonGraded) {
    await db
      .update(userProgress)
      .set({
        points: currentUserProgress.points + 10,
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
      })
      .where(eq(userProgress.userId, userId));
  }

  revalidatePath("/learn");
  revalidatePath("/lesson");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");
  revalidatePath(`/lesson/${lessonId}`);

  return { success: true };
};

export const updateFillInTheBlankProgress = async (
  challengeId: number,
  selectedOptionId: number
) => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });

  if (!challenge) {
    throw new Error("Challenge not found");
  }

  // Bypass hearts gate for non-graded/EXPLAINER
  // @ts-ignore
  const isNonGraded = (challenge as any).isNonGraded === true;

  const lessonId = challenge.lessonId;

  const existingChallengeProgress = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  if (
    currentUserProgress.hearts === 0 &&
    !existingChallengeProgress &&
    !userSubscription?.isActive &&
    challenge.type !== "EXPLAINER" &&
    !isNonGraded
  ) {
    return { error: "hearts" };
  }

  const correctOption = await db.query.challengeOptions.findFirst({
    where: and(
      eq(challengeOptionsSchema.challengeId, challengeId),
      eq(challengeOptionsSchema.correct, true)
    ),
  });

  const isCorrect = correctOption?.id === selectedOptionId;

  if (!isCorrect) {
    if (challenge.type === "EXPLAINER" || isNonGraded) {
      revalidatePath(`/lesson/${lessonId}`);
      return { error: "non_graded" };
    }

    await db
      .update(userProgress)
      .set({
        hearts: Math.max(currentUserProgress.hearts - 1, 0),
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    return { error: "incorrect" };
  }

  if (existingChallengeProgress) {
    await db
      .update(challengeProgress)
      .set({
        completed: true,
      })
      .where(eq(challengeProgress.id, existingChallengeProgress.id));
  } else {
    await db.insert(challengeProgress).values({
      challengeId,
      userId,
      completed: true,
    });
  }

  if (challenge.type !== "EXPLAINER" && !isNonGraded) {
    await db
      .update(userProgress)
      .set({
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));
  }

  revalidatePath("/learn");
  revalidatePath("/lesson");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");
  revalidatePath(`/lesson/${lessonId}`);

  return { success: true };
};

export const updateSpeakThisAdvancedProgress = async (challengeId: number) => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });

  if (!challenge) {
    throw new Error("Challenge not found");
  }

  // Bypass hearts gate for non-graded/EXPLAINER
  // @ts-ignore
  const isNonGraded = (challenge as any).isNonGraded === true;

  const lessonId = challenge.lessonId;

  const existingChallengeProgress = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  if (
    currentUserProgress.hearts === 0 &&
    !existingChallengeProgress &&
    !userSubscription?.isActive &&
    challenge.type !== "EXPLAINER" &&
    !isNonGraded
  ) {
    return { error: "hearts" };
  }

  if (existingChallengeProgress) {
    await db
      .update(challengeProgress)
      .set({
        completed: true,
      })
      .where(eq(challengeProgress.id, existingChallengeProgress.id));
  } else {
    await db.insert(challengeProgress).values({
      challengeId,
      userId,
      completed: true,
    });
  }

  if (challenge.type !== "EXPLAINER" && !isNonGraded) {
    await db
      .update(userProgress)
      .set({
        points: currentUserProgress.points + 10,
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
      })
      .where(eq(userProgress.userId, userId));
  }

  revalidatePath("/learn");
  revalidatePath("/lesson");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");
  revalidatePath(`/lesson/${lessonId}`);

  return { success: true };
};

// NEW: Mark EXPLAINER / non-graded items as complete without hearts/points changes
export const updateExplainerProgress = async (challengeId: number) => {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });
  if (!challenge) throw new Error("Challenge not found");

  // @ts-ignore
  const isNonGraded = (challenge as any).isNonGraded === true;

  if (challenge.type !== "EXPLAINER" && !isNonGraded) {
    console.log(
      "updateExplainerProgress: Challenge is not EXPLAINER/non-graded; skipping."
    );
    return { error: "not_explainer" };
  }

  const existing = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  if (existing) {
    await db
      .update(challengeProgress)
      .set({ completed: true })
      .where(eq(challengeProgress.id, existing.id));
  } else {
    await db.insert(challengeProgress).values({
      challengeId,
      userId,
      completed: true,
    });
  }

  const lessonId = challenge.lessonId;
  revalidatePath(`/lesson/${lessonId}`);
  revalidatePath("/learn");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");

  return { success: true };
};

// NEW function for memorization hearts deduction
export const reduceHeartsForMemorization = async () => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  if (userSubscription?.isActive) {
    console.log(
      "reduceHeartsForMemorization: Subscription active. No hearts deducted."
    );
    return { error: "subscription" };
  }

  if (currentUserProgress.hearts === 0) {
    console.log("reduceHeartsForMemorization: No hearts left to deduct.");
    return { error: "hearts" };
  }

  await db
    .update(userProgress)
    .set({
      hearts: Math.max(currentUserProgress.hearts - 1, 0),
    })
    .where(eq(userProgress.userId, userId));

  revalidatePath("/shop");
  revalidatePath("/learn");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");

  console.log("reduceHeartsForMemorization: Heart deducted successfully.");
  return { success: true };
};

/* ------------------------------------------------------------------ */
/* NEW: Speak Sequence progress (continuous 10 items, complete on all) */
/* ------------------------------------------------------------------ */
export const updateSpeakSequenceProgress = async (
  challengeId: number,
  payload?: string | number | number[]
) => {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();
  if (!currentUserProgress) throw new Error("User progress not found");

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });
  if (!challenge) throw new Error("Challenge not found");

  // @ts-ignore
  const isNonGraded = (challenge as any).isNonGraded === true;
  const lessonId = challenge.lessonId;

  const existingChallengeProgress = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  // hearts gate for graded items on first completion attempt
  if (
    currentUserProgress.hearts === 0 &&
    !existingChallengeProgress &&
    !userSubscription?.isActive &&
    challenge.type !== "EXPLAINER" &&
    !isNonGraded
  ) {
    return { error: "hearts" };
  }

  // Helper to finalize completion + scoring (mirror speak-advanced)
  const finalize = async () => {
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({ completed: true })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    if (challenge.type !== "EXPLAINER" && !isNonGraded) {
      await db
        .update(userProgress)
        .set({
          points: currentUserProgress.points + 10,
          hearts: Math.min(currentUserProgress.hearts + 1, 5),
        })
        .where(eq(userProgress.userId, userId));
    }

    revalidatePath("/learn");
    revalidatePath("/lesson");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
    revalidatePath(`/lesson/${lessonId}`);

    return { success: true as const };
  };

  const norm = (v: unknown) =>
    typeof v === "string" ? v.trim().toLowerCase() : v;

  const flag = norm(payload);

  // Case A: explicit completion flag (client signals all items matched)
  if (flag === "complete" || flag === "done" || flag === "all_matched") {
    return await finalize();
  }

  // Fetch expected options and expected count
  const expectedOpts = await db.query.challengeOptions.findMany({
    where: eq(challengeOptionsSchema.challengeId, challengeId),
    columns: { id: true },
  });
  const expectedIds = new Set(expectedOpts.map((o) => o.id));

  // @ts-ignore
  const expectedCount =
    (challenge as any).speakItemCount ??
    // @ts-ignore snake_case fallback
    (challenge as any).speak_item_count ??
    10;

  // Case B: comma-separated string of option IDs
  if (typeof payload === "string" && payload.includes(",")) {
    const ids = payload
      .split(",")
      .map((s) => s.trim())
      .filter(Boolean)
      .map((s) => Number(s))
      .filter((n) => !Number.isNaN(n));

    if (ids.length === 0) return { error: "invalid_payload" };

    const allBelong = ids.every((id) => expectedIds.has(id));
    if (!allBelong) return { error: "incorrect" };

    if (ids.length !== expectedCount) return { error: "incorrect" };

    return await finalize();
  }

  // Case C: array of numbers (same as above)
  if (Array.isArray(payload)) {
    const ids = payload.map(Number).filter((n) => !Number.isNaN(n));
    if (ids.length === 0) return { error: "invalid_payload" };

    const allBelong = ids.every((id) => expectedIds.has(id));
    if (!allBelong) return { error: "incorrect" };

    if (ids.length !== expectedCount) return { error: "incorrect" };

    return await finalize();
  }

  // Case D: single number (partial progress — do nothing server-side)
  if (typeof payload === "number") {
    return { status: "pending" as const };
  }

  return { error: "invalid_payload" };
};

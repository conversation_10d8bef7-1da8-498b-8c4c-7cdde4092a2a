// tailwind.config.ts

import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      screens: {
        macbook: "1440px",
        lenovo: "1920px",
      },
      fontFamily: {
        sans: [
          "ProximaVara",
          'Helvetica Neue"',
          "Helvetica",
          "Arial",
          "sans-serif",
        ],
        mirza: ["Mirza", "serif"],
        hafs: ["Hafs", "sans-serif"],
      },
      letterSpacing: {
        wider: "0.05em",
      },
      fontWeight: {
        medium: "400",
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        slate: {
          "50": "rgb(248 250 252)",
          "100": "rgb(241 245 249)",
          "200": "rgb(226 232 240)",
          "300": "rgb(203 213 225)",
          "400": "rgb(148 163 184)",
          "500": "rgb(100 116 139)",
          "600": "rgb(71 85 105)",
          "700": "rgb(51 65 85)",
          "800": "rgb(30 41 59)",
          "900": "rgb(15 23 42)",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "fade-in": {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        "fade-out": {
          "0%": { opacity: "1" },
          "100%": { opacity: "0" },
        },
        "slide-in": {
          "0%": { transform: "translateY(100%)" },
          "100%": { transform: "translateY(0)" },
        },
        "fold-top": {
          "0%, 100%": { transform: "scaleY(0)" },
          "50%": { transform: "scaleY(1)" },
        },
        "fold-right": {
          "0%, 100%": { transform: "scaleX(0)" },
          "50%": { transform: "scaleX(1)" },
        },
        "fold-bottom": {
          "0%, 100%": { transform: "scaleY(0)" },
          "50%": { transform: "scaleY(1)" },
        },
        "fold-left": {
          "0%, 100%": { transform: "scaleX(0)" },
          "50%": { transform: "scaleX(1)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fade-in 0.2s ease-out",
        "fade-out": "fade-out 0.2s ease-out",
        "slide-in": "slide-in 0.2s ease-out",
        "fold-top": "fold-top 2s ease-in-out infinite",
        "fold-right": "fold-right 2s ease-in-out infinite",
        "fold-bottom": "fold-bottom 2s ease-in-out infinite",
        "fold-left": "fold-left 2s ease-in-out infinite",
      },
      transitionProperty: {
        height: "height",
        spacing: "margin, padding",
      },
      fontFeatureSettings: {
        proportional: '"pnum"',
        tabular: '"tnum"',
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("tailwind-scrollbar")],
};

export default config;

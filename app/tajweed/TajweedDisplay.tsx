import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  Play,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Undo2,
  Check,
  Square,
} from "lucide-react";
import { verseGlyphs } from "../memorization/verseGlyphs";
import {
  getRuleColor,
  getRulePatterns,
  findMatchingRules,
  isValidRuleId,
} from "./tajweedPatterns";
import { tajweedExamples } from "./tajweedExamples";
import { LessonSlide } from "./tajweedLessons";

// Define VerseRange type
export type VerseRange = {
  start: number;
  end: number;
};

export type TajweedRule = {
  id: string;
  name: string;
  arabicName: string;
  description: string;
  color: string;
};

export type RuleContext = {
  ruleId: string;
  ruleName: string;
  ruleArabicName: string;
  keyWords: string[];
  reason: string;
  difficultyLevel: string;
  practiceNotes: string;
  examples?: ExampleReference[];
};

export type ExampleReference = {
  id?: number;
  surah?: number | null;
  verse?: number | string | null;
  wordIndices?: number[];
  keyWords?: string[];
  exampleText?: string;
  explanation?: string;
  audio_url?: string;
  start_ms?: number;
  end_ms?: number;
  nextVerseToPlay?: {
    surah: number;
    verse: number;
  } | null;
};

export type TajweedWordObj = {
  id: number;
  text: string;
  audio: string;
  tajweedRule?: string | null;
  highlighted: boolean;
  verseNumber: number;
  wordIndex: number;
  letterHighlights?: { startIndex: number; endIndex: number; ruleId: string }[];
  isKeyWord?: boolean;
  isWordStart?: boolean;
};

type TajweedDisplayProps = {
  surah: {
    id?: number;
    number: number;
    name: string;
    englishName: string;
    englishNameTranslation?: string;
    revelationPlace: string;
    numberOfAyahs: number;
  };
  verses: { surahNumber: number; verseNumber: number; text: string }[];
  activeRule: TajweedRule | null;
  tajweedRules: TajweedRule[];
  onRuleSelection: (ruleId: string) => void;
  onWordClick: (word: TajweedWordObj) => void;
  totalAyahs: number;
  selectedRange: VerseRange | null;
  availableRanges: VerseRange[];
  onRangeSelectionRequest: (range: VerseRange) => void;
  onClearRangeSelection: () => void;
  previousSurahNumber?: number | null;
  nextSurahNumber?: number | null;
  onNavigateSurah?: (surahNumber: number) => void;
  ruleContext?: RuleContext | null;
  mode?: string;
  lessonMode?: boolean;
  currentLessonSlide?: LessonSlide;
  highlightedPattern?: string | null;
  highlightedLetterIndices?: { start: number; end: number }[];
  lessonTitle?: string;
  onVerseTextChange?: (text: string) => void;
  onAudioProgressChange?: (progress: number) => void;
  onAudioEnded?: () => void;
  onAudioPlaybackStateChange?: (isPlaying: boolean) => void;
};

export interface TajweedDisplayHandle {
  nextExample: () => boolean;
  previousExample: () => boolean;
  getCurrentExampleIndex: () => number;
  getTotalExamplesCount: () => number;
  isLastExample: () => boolean;
  isFirstExample: () => boolean;
  showSuccessMessage: () => void;
  hideSuccessMessage: () => void;
  playCurrentAudio: () => boolean;
  pauseAudio: () => boolean;
}

const normalizeArabicText = (text: string): string => {
  if (!text) return "";
  let normalized = text.trim().replace(/\s+/g, " ");
  if (process.env.NODE_ENV === "development" && text !== normalized) {
    console.log(
      `[TajweedDisplay] Text normalized: "${text}" -> "${normalized}"`
    );
  }
  return normalized;
};

function removeBasmala(verseText: string): string {
  const basmala = "بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ";
  const basmalaWithSpace = basmala + " ";
  if (verseText.startsWith(basmalaWithSpace)) {
    return verseText.slice(basmalaWithSpace.length).trimStart();
  } else if (verseText.startsWith(basmala)) {
    return verseText.slice(basmala.length).trimStart();
  }
  return verseText;
}

const debugPatternMatching = (
  word: string,
  ruleId: string,
  patterns: string[]
) => {
  if (process.env.NODE_ENV === "development") {
    console.debug(`[TajweedDebug] Match attempt on word: "${word}"`, {
      ruleId,
      matchedPatterns: patterns,
    });
  }
};

const TajweedDisplay = forwardRef<TajweedDisplayHandle, TajweedDisplayProps>(
  (
    {
      surah,
      verses: propsVerses,
      activeRule,
      tajweedRules,
      onRuleSelection,
      onWordClick,
      totalAyahs,
      selectedRange,
      availableRanges,
      onRangeSelectionRequest,
      onClearRangeSelection,
      previousSurahNumber,
      nextSurahNumber,
      onNavigateSurah,
      ruleContext,
      mode,
      lessonMode: propsLessonMode,
      currentLessonSlide,
      highlightedPattern,
      highlightedLetterIndices,
      onVerseTextChange,
      onAudioProgressChange,
      onAudioEnded,
      onAudioPlaybackStateChange,
      lessonTitle,
    },
    ref
  ) => {
    const isExampleMode = useMemo(() => mode === "example", [mode]);
    const isLessonMode = useMemo(
      () => mode === "lesson" || propsLessonMode === true,
      [mode, propsLessonMode]
    );

    const [processedVerses, setProcessedVerses] = useState<{
      [verseKey: number]: TajweedWordObj[];
    }>({});
    const [currentExampleIndex, setCurrentExampleIndex] = useState<number>(0);
    const [lastNotifiedText, setLastNotifiedText] = useState<string>("");
    const [lessonExampleWords, setLessonExampleWords] = useState<
      TajweedWordObj[] | null
    >(null);

    const audioRef = useRef<HTMLAudioElement>(null);
    const [isAudioPlaying, setIsAudioPlaying] = useState<boolean>(false);
    const [currentAudioInfo, setCurrentAudioInfo] = useState<{
      start_ms: number;
      end_ms: number;
      audio_url: string;
    } | null>(null);
    const audioStopTimerRef = useRef<NodeJS.Timeout | null>(null);
    const [playingSecondVerseOfPair, setPlayingSecondVerseOfPair] =
      useState<ExampleReference["nextVerseToPlay"]>(null);
    const [isInTwoVerseSequence, setIsInTwoVerseSequence] =
      useState<boolean>(false);
    const [currentVerseInSequence, setCurrentVerseInSequence] = useState<
      "first" | "second" | "first_ended" | null
    >(null);
    const [isCompleted, setIsCompleted] = useState<boolean>(false);
    const [navigationInProgress, setNavigationInProgress] =
      useState<boolean>(false);
    const [showSuccessOverlay, setShowSuccessOverlay] =
      useState<boolean>(false);
    const successTimerRef = useRef<NodeJS.Timeout | null>(null);
    const needsTextUpdateRef = useRef<boolean>(false);
    const latestExampleOrderRef = useRef<ExampleReference[]>([]);
    const latestExampleIndexRef = useRef<number>(0);

    const detectGeneralTajweedRule = useCallback(
      (text: string): string | null => {
        if (!text || text.trim().length === 0) return null;
        const normalizedText = normalizeArabicText(text);
        try {
          const matchingRules = findMatchingRules(normalizedText);
          if (matchingRules.length > 0) {
            const topMatch = matchingRules[0];
            if (process.env.NODE_ENV === "development") {
              debugPatternMatching(
                normalizedText,
                topMatch.ruleId,
                topMatch.matchedPatterns
              );
            }
            return topMatch.ruleId;
          }
        } catch (error) {
          console.error(
            "[TajweedDisplay] Error in detectGeneralTajweedRule:",
            error
          );
        }
        return null;
      },
      []
    );

    useEffect(() => {
      if (onAudioPlaybackStateChange) {
        onAudioPlaybackStateChange(isAudioPlaying);
      }
    }, [isAudioPlaying, onAudioPlaybackStateChange]);

    useEffect(() => {
      const currentAudioRef = audioRef.current;
      return () => {
        if (successTimerRef.current) clearTimeout(successTimerRef.current);
        if (audioStopTimerRef.current) clearTimeout(audioStopTimerRef.current);
        if (currentAudioRef && !currentAudioRef.paused) {
          currentAudioRef.pause();
        }
      };
    }, []);

    useEffect(() => {
      latestExampleIndexRef.current = currentExampleIndex;
      if (isCompleted) setIsCompleted(false);
      if (audioRef.current && !audioRef.current.paused) {
        audioRef.current.pause();
      }
      setCurrentAudioInfo(null);
      setPlayingSecondVerseOfPair(null);
      setIsInTwoVerseSequence(false);
      setCurrentVerseInSequence(null);
      if (audioStopTimerRef.current) {
        clearTimeout(audioStopTimerRef.current);
        audioStopTimerRef.current = null;
      }
      if (onAudioProgressChange) onAudioProgressChange(0);
    }, [currentExampleIndex, isCompleted, mode, onAudioProgressChange]);

    useImperativeHandle(ref, () => ({
      nextExample: () => {
        if (
          latestExampleIndexRef.current <
          latestExampleOrderRef.current.length - 1
        ) {
          handleNextExample();
          return true;
        }
        return false;
      },
      previousExample: () => {
        if (latestExampleIndexRef.current > 0) {
          handlePreviousExample();
          return true;
        }
        return false;
      },
      getCurrentExampleIndex: () => latestExampleIndexRef.current,
      getTotalExamplesCount: () => latestExampleOrderRef.current.length,
      isLastExample: () =>
        latestExampleIndexRef.current ===
        latestExampleOrderRef.current.length - 1,
      isFirstExample: () => latestExampleIndexRef.current === 0,
      showSuccessMessage: () => {
        if (successTimerRef.current) clearTimeout(successTimerRef.current);
        setShowSuccessOverlay(true);
        successTimerRef.current = setTimeout(() => {
          setShowSuccessOverlay(false);
          successTimerRef.current = null;
        }, 2500);
      },
      hideSuccessMessage: () => {
        setShowSuccessOverlay(false);
        if (successTimerRef.current) clearTimeout(successTimerRef.current);
      },
      playCurrentAudio: () => {
        handleAudioPlay();
        return true;
      },
      pauseAudio: () => {
        if (audioRef.current && !audioRef.current.paused) {
          audioRef.current.pause();
          return true;
        }
        return false;
      },
    }));

    const identifyLetterHighlights = useCallback(
      (
        word: string,
        currentLessonRuleIdToHighlight: string | null,
        isCurrentLessonMode: boolean,
        explicitPatternFromSlide?: string | null,
        explicitIndicesFromSlide?: { start: number; end: number }[] | null
      ): { startIndex: number; endIndex: number; ruleId: string }[] => {
        const highlights: {
          startIndex: number;
          endIndex: number;
          ruleId: string;
        }[] = [];
        if (!word || word.trim().length === 0) {
          return highlights;
        }
        const normalizedWord = normalizeArabicText(word);

        if (
          currentLessonRuleIdToHighlight &&
          !isValidRuleId(currentLessonRuleIdToHighlight)
        ) {
          console.warn(
            `[TajweedDisplay] identifyLetterHighlights: Invalid rule ID: ${currentLessonRuleIdToHighlight}`
          );
          return highlights;
        }
        if (!currentLessonRuleIdToHighlight) {
          return highlights;
        }
        try {
          if (
            isCurrentLessonMode &&
            explicitIndicesFromSlide &&
            explicitIndicesFromSlide.length > 0
          ) {
            explicitIndicesFromSlide.forEach(({ start, end }) => {
              if (start >= 0 && end < normalizedWord.length && start <= end) {
                highlights.push({
                  startIndex: start,
                  endIndex: end,
                  ruleId: currentLessonRuleIdToHighlight,
                });
              } else {
                console.warn(
                  `[TajweedDisplay] Invalid explicit indices: start=${start}, end=${end}, wordLength=${normalizedWord.length}`
                );
              }
            });
            if (highlights.length > 0) return highlights;
          }
          if (
            isCurrentLessonMode &&
            explicitPatternFromSlide &&
            explicitPatternFromSlide.trim() !== ""
          ) {
            const normalizedPattern = normalizeArabicText(
              explicitPatternFromSlide
            );
            let startIndex = normalizedWord.indexOf(normalizedPattern);
            while (startIndex !== -1) {
              highlights.push({
                startIndex,
                endIndex: startIndex + normalizedPattern.length - 1,
                ruleId: currentLessonRuleIdToHighlight,
              });
              startIndex = normalizedWord.indexOf(
                normalizedPattern,
                startIndex + normalizedPattern.length
              );
            }
            if (highlights.length > 0) return highlights;
          }
          if (currentLessonRuleIdToHighlight) {
            const patternsToSearch = getRulePatterns(
              currentLessonRuleIdToHighlight
            );
            if (patternsToSearch.length === 0) {
              return highlights;
            }
            const sortedPatterns = [...patternsToSearch].sort(
              (a, b) => b.length - a.length
            );
            sortedPatterns.forEach((pattern: string) => {
              const normalizedPattern = normalizeArabicText(pattern);
              if (normalizedPattern.length === 0) return;
              let searchFromIndex = 0;
              while (searchFromIndex < normalizedWord.length) {
                const startIndex = normalizedWord.indexOf(
                  normalizedPattern,
                  searchFromIndex
                );
                if (startIndex === -1) break;
                const endIndex = startIndex + normalizedPattern.length - 1;
                const wouldOverlap = highlights.some(
                  (existing) =>
                    startIndex <= existing.endIndex &&
                    endIndex >= existing.startIndex
                );
                if (!wouldOverlap) {
                  highlights.push({
                    startIndex,
                    endIndex: endIndex,
                    ruleId: currentLessonRuleIdToHighlight,
                  });
                }
                searchFromIndex = startIndex + 1;
              }
            });
            if (
              process.env.NODE_ENV === "development" &&
              highlights.length > 0
            ) {
              debugPatternMatching(
                normalizedWord,
                currentLessonRuleIdToHighlight,
                sortedPatterns.filter((p) =>
                  normalizedWord.includes(normalizeArabicText(p))
                )
              );
            }
          }
        } catch (error) {
          console.error(
            "[TajweedDisplay] Error in identifyLetterHighlights:",
            error
          );
        }
        return highlights;
      },
      []
    );

    useEffect(() => {
      if (!propsVerses || propsVerses.length === 0) {
        if (Object.keys(processedVerses).length > 0) {
          setProcessedVerses({});
        }
        return;
      }
      if (isLessonMode && currentLessonSlide?.arabicExample) {
        return;
      }
      const newProcessed: { [verseKey: number]: TajweedWordObj[] } = {};
      propsVerses.forEach((verseItem) => {
        const itemKey = verseItem.verseNumber;
        const textToProcess =
          mode === "regular" &&
          itemKey === 1 &&
          surah.number !== 1 &&
          surah.number !== 9
            ? removeBasmala(verseItem.text)
            : verseItem.text;
        const words = textToProcess
          .split(/\s+/)
          .filter((word) => word.trim().length > 0);
        newProcessed[itemKey] = words.map((word, index) => {
          const normalizedWord = normalizeArabicText(word);
          const generalTajweedRuleInWord =
            detectGeneralTajweedRule(normalizedWord);
          const letterHighlightsForWord = identifyLetterHighlights(
            normalizedWord,
            activeRule?.id || null,
            false,
            highlightedPattern,
            highlightedLetterIndices
          );
          return {
            id: index,
            text: word,
            audio: `/${verseItem.surahNumber || 0}_${itemKey}_${index + 1}.mp3`,
            tajweedRule: generalTajweedRuleInWord,
            highlighted: false,
            verseNumber: itemKey,
            wordIndex: index,
            letterHighlights: letterHighlightsForWord,
            isKeyWord:
              ruleContext?.keyWords?.some((kw) =>
                normalizedWord.includes(normalizeArabicText(kw))
              ) || false,
            isWordStart: index === 0,
          };
        });
      });
      if (JSON.stringify(processedVerses) !== JSON.stringify(newProcessed)) {
        setProcessedVerses(newProcessed);
      }
    }, [
      propsVerses,
      surah.number,
      mode,
      isLessonMode,
      currentLessonSlide,
      activeRule?.id,
      ruleContext?.keyWords,
      identifyLetterHighlights,
      highlightedPattern,
      highlightedLetterIndices,
      detectGeneralTajweedRule,
      processedVerses,
    ]);

    useEffect(() => {
      if (
        isLessonMode &&
        currentLessonSlide &&
        currentLessonSlide.arabicExample
      ) {
        const exampleText = currentLessonSlide.arabicExample;
        const normalizedText = normalizeArabicText(exampleText);
        const words = normalizedText
          .split(/\s+/)
          .filter((word) => word.trim().length > 0);

        const newLessonExampleWords: TajweedWordObj[] = words.map(
          (word: string, index: number) => ({
            id: index,
            text: word,
            audio: "",
            tajweedRule: detectGeneralTajweedRule(word),
            highlighted: false,
            verseNumber: 0,
            wordIndex: index,
            letterHighlights: identifyLetterHighlights(
              word,
              activeRule?.id || null,
              true,
              currentLessonSlide.highlightPattern,
              currentLessonSlide.highlightLetterIndices
            ),
            isKeyWord: false,
            isWordStart: index === 0,
          })
        );
        setLessonExampleWords(newLessonExampleWords);
      } else if (isLessonMode) {
        setLessonExampleWords(null);
      }
    }, [
      isLessonMode,
      currentLessonSlide,
      activeRule?.id,
      identifyLetterHighlights,
      detectGeneralTajweedRule,
    ]);

    const exampleOrder = useMemo<ExampleReference[]>(() => {
      if (!isExampleMode) {
        return [];
      }
      let examplesToUse: ExampleReference[] = [];
      if (
        activeRule?.id &&
        tajweedExamples[activeRule.id as keyof typeof tajweedExamples]?.examples
      ) {
        examplesToUse = [
          ...tajweedExamples[activeRule.id as keyof typeof tajweedExamples]
            .examples,
        ];
      } else if (ruleContext?.examples && ruleContext.examples.length > 0) {
        examplesToUse = [...ruleContext.examples];
      }
      const examplesWithIds = examplesToUse.map((ex, index) => ({
        ...ex,
        id:
          ex.id ??
          (typeof ex.verse === "number" ? ex.verse : undefined) ??
          index,
      }));
      const sortedExamples = examplesWithIds.sort(
        (a, b) =>
          (typeof a.id === "number" ? a.id : Infinity) -
          (typeof b.id === "number" ? b.id : Infinity)
      );
      latestExampleOrderRef.current = sortedExamples;
      return sortedExamples;
    }, [isExampleMode, activeRule?.id, ruleContext?.examples]);

    const getExampleText = useCallback(
      (exampleRef: ExampleReference | undefined): string => {
        if (!exampleRef) {
          return "";
        }
        if (exampleRef.exampleText) {
          return normalizeArabicText(exampleRef.exampleText);
        }
        const exampleKey =
          exampleRef.id !== undefined
            ? exampleRef.id
            : typeof exampleRef.verse === "number"
            ? exampleRef.verse
            : undefined;
        if (exampleKey === undefined) {
          return "";
        }
        const verseWords = processedVerses[exampleKey];
        if (verseWords && verseWords.length > 0) {
          const combinedText = verseWords.map((word) => word.text).join(" ");
          return normalizeArabicText(combinedText);
        }
        return "";
      },
      [processedVerses]
    );

    const getCurrentDisplayedText = useCallback((): string => {
      if (isLessonMode) {
        const lessonText = currentLessonSlide?.arabicExample || "";
        if (lessonText) {
          return normalizeArabicText(lessonText);
        }
        return "";
      }
      if (isExampleMode) {
        if (
          exampleOrder.length > 0 &&
          currentExampleIndex < exampleOrder.length
        ) {
          const exampleText = getExampleText(exampleOrder[currentExampleIndex]);
          if (exampleText) {
            return exampleText;
          }
        }
        return "";
      }
      if (propsVerses && propsVerses.length > 0) {
        const versesToDisplay = selectedRange
          ? propsVerses.filter(
              (v) =>
                v.verseNumber >= selectedRange.start &&
                v.verseNumber <= selectedRange.end
            )
          : propsVerses;
        if (versesToDisplay.length > 0) {
          const combinedText = versesToDisplay
            .map((v) => {
              const text =
                v.verseNumber === 1 &&
                surah.number !== 1 &&
                surah.number !== 9 &&
                mode === "regular"
                  ? removeBasmala(v.text)
                  : v.text;
              return text;
            })
            .join(" ");
          return normalizeArabicText(combinedText);
        }
      }
      return "";
    }, [
      isLessonMode,
      isExampleMode,
      currentLessonSlide,
      exampleOrder,
      currentExampleIndex,
      getExampleText,
      propsVerses,
      selectedRange,
      surah.number,
      mode,
    ]);

    useEffect(() => {
      if (typeof onVerseTextChange === "function") {
        const newText = getCurrentDisplayedText();
        const hasTextChanged = newText !== lastNotifiedText;
        const forceUpdate = needsTextUpdateRef.current;
        if (
          newText &&
          newText.trim().length > 0 &&
          (hasTextChanged || forceUpdate)
        ) {
          onVerseTextChange(newText);
          setLastNotifiedText(newText);
          needsTextUpdateRef.current = false;
        } else if (
          (!newText || newText.trim().length === 0) &&
          newText !== lastNotifiedText
        ) {
          onVerseTextChange("");
          setLastNotifiedText("");
          needsTextUpdateRef.current = false;
        }
      }
    }, [onVerseTextChange, getCurrentDisplayedText, lastNotifiedText]);

    useEffect(() => {
      if (isExampleMode) {
        if (
          currentExampleIndex >= exampleOrder.length &&
          exampleOrder.length > 0
        ) {
          setCurrentExampleIndex(0);
        } else if (exampleOrder.length === 0 && currentExampleIndex !== 0) {
          setCurrentExampleIndex(0);
        }
      }
    }, [exampleOrder, currentExampleIndex, isExampleMode]);

    useEffect(() => {
      if (isExampleMode) {
        setLastNotifiedText("");
        needsTextUpdateRef.current = true;
      }
    }, [currentExampleIndex, isExampleMode]);

    const findAudioTimestampForItem = useCallback(() => {
      if (
        !isExampleMode ||
        !exampleOrder ||
        exampleOrder.length === 0 ||
        currentExampleIndex < 0 ||
        currentExampleIndex >= exampleOrder.length
      ) {
        return null;
      }
      const currentExampleRef = exampleOrder[currentExampleIndex];
      if (currentExampleRef && currentExampleRef.audio_url) {
        if (
          typeof currentExampleRef.start_ms === "number" &&
          typeof currentExampleRef.end_ms === "number"
        ) {
          return {
            audio_url: currentExampleRef.audio_url,
            start_ms: currentExampleRef.start_ms,
            end_ms: currentExampleRef.end_ms,
          };
        }
        return { audio_url: currentExampleRef.audio_url };
      }
      return null;
    }, [isExampleMode, exampleOrder, currentExampleIndex]);

    const playAudioSegment = useCallback(
      (start_ms: number, end_ms: number, audio_url: string) => {
        if (!audioRef.current) {
          return;
        }
        const audioElement = audioRef.current;
        const startTimeInSeconds = start_ms / 1000;
        const durationInMs = end_ms - start_ms;
        if (durationInMs <= 0) {
          setIsAudioPlaying(false);
          setCurrentAudioInfo(null);
          return;
        }
        const performPlay = () => {
          audioElement.currentTime = startTimeInSeconds;
          audioElement
            .play()
            .then(() => {
              if (audioStopTimerRef.current)
                clearTimeout(audioStopTimerRef.current);
              audioStopTimerRef.current = setTimeout(() => {
                if (audioRef.current && !audioRef.current.paused) {
                  audioRef.current.pause();
                }
              }, durationInMs);
            })
            .catch((error) => {
              console.error(
                "[TajweedDisplay] Audio segment play failed:",
                error
              );
              setIsAudioPlaying(false);
              setCurrentAudioInfo(null);
              if (audioStopTimerRef.current)
                clearTimeout(audioStopTimerRef.current);
            });
        };
        if (audioElement.src !== audio_url) {
          audioElement.src = audio_url;
          const loadedDataHandler = () => {
            performPlay();
            audioElement.removeEventListener("loadeddata", loadedDataHandler);
            audioElement.removeEventListener("error", errorHandler);
          };
          const errorHandler = () => {
            setIsAudioPlaying(false);
            setCurrentAudioInfo(null);
            audioElement.removeEventListener("loadeddata", loadedDataHandler);
            audioElement.removeEventListener("error", errorHandler);
          };
          audioElement.addEventListener("loadeddata", loadedDataHandler);
          audioElement.addEventListener("error", errorHandler);
          audioElement.load();
        } else {
          if (audioElement.readyState >= HTMLMediaElement.HAVE_ENOUGH_DATA) {
            performPlay();
          } else {
            const readyHandler = () => {
              performPlay();
              audioElement.removeEventListener("canplaythrough", readyHandler);
              audioElement.removeEventListener("error", errorRetryHandler);
            };
            const errorRetryHandler = () => {
              setIsAudioPlaying(false);
              setCurrentAudioInfo(null);
              audioElement.removeEventListener("canplaythrough", readyHandler);
              audioElement.removeEventListener("error", errorRetryHandler);
            };
            audioElement.addEventListener("canplaythrough", readyHandler);
            audioElement.addEventListener("error", errorRetryHandler);
            if (
              audioElement.networkState === HTMLMediaElement.NETWORK_NO_SOURCE
            ) {
              audioElement.load();
            }
          }
        }
      },
      []
    );

    const handleAudioPlay = useCallback(() => {
      if (!audioRef.current) {
        return;
      }
      const audioElement = audioRef.current;
      if (isAudioPlaying) {
        audioElement.pause();
        return;
      }
      const currentExampleRef =
        isExampleMode && exampleOrder.length > currentExampleIndex
          ? exampleOrder[currentExampleIndex]
          : null;
      if (playingSecondVerseOfPair) {
        const { surah: sNum, verse: vNum } = playingSecondVerseOfPair;
        const audio_url = `https://audio.qurancdn.com/Alafasy/mp3/${String(
          sNum
        ).padStart(3, "0")}${String(vNum).padStart(3, "0")}.mp3`;
        setCurrentAudioInfo(null);
        if (audioElement.src !== audio_url) {
          audioElement.src = audio_url;
        }
        audioElement.currentTime = 0;
        audioElement.play().catch((error) => {
          setIsAudioPlaying(false);
          setPlayingSecondVerseOfPair(null);
          setIsInTwoVerseSequence(false);
          setCurrentVerseInSequence(null);
        });
        return;
      }
      const audioTimestampData = findAudioTimestampForItem();
      if (isExampleMode && currentExampleRef) {
        if (currentExampleRef.nextVerseToPlay) {
          const firstVerseSurah = currentExampleRef.surah;
          const firstVerseNum = currentExampleRef.verse;
          if (
            typeof firstVerseSurah === "number" &&
            (typeof firstVerseNum === "number" ||
              typeof firstVerseNum === "string")
          ) {
            const firstVerseAudioUrl = `https://audio.qurancdn.com/Alafasy/mp3/${String(
              firstVerseSurah
            ).padStart(3, "0")}${String(firstVerseNum).padStart(3, "0")}.mp3`;
            setCurrentAudioInfo(null);
            setIsInTwoVerseSequence(true);
            if (audioElement.src !== firstVerseAudioUrl) {
              audioElement.src = firstVerseAudioUrl;
            }
            audioElement.currentTime = 0;
            audioElement.play().catch((e) => {
              setIsInTwoVerseSequence(false);
            });
          }
        } else if (audioTimestampData) {
          if (
            audioTimestampData.start_ms !== undefined &&
            audioTimestampData.end_ms !== undefined
          ) {
            const { audio_url, start_ms, end_ms } = audioTimestampData;
            setCurrentAudioInfo({ start_ms, end_ms, audio_url });
            playAudioSegment(start_ms, end_ms, audio_url);
          } else {
            const { audio_url } = audioTimestampData;
            setCurrentAudioInfo(null);
            if (audioElement.src !== audio_url) {
              audioElement.src = audio_url;
            }
            audioElement.currentTime = 0;
            audioElement.play();
          }
        }
      } else if (!isLessonMode && surah?.number) {
        setCurrentAudioInfo(null);
        const url = `https://download.quranicaudio.com/qdc/mishari_al_afasy/murattal/${surah.number}.mp3`;
        if (audioElement.src !== url) {
          audioElement.src = url;
        }
        audioElement.currentTime = 0;
        audioElement.play().catch((e) => {
          setIsAudioPlaying(false);
        });
      }
    }, [
      isAudioPlaying,
      isExampleMode,
      isLessonMode,
      surah?.number,
      findAudioTimestampForItem,
      playAudioSegment,
      exampleOrder,
      currentExampleIndex,
      playingSecondVerseOfPair,
    ]);

    useEffect(() => {
      if (
        isInTwoVerseSequence &&
        currentVerseInSequence === "first_ended" &&
        playingSecondVerseOfPair
      ) {
        const timer = setTimeout(() => {
          handleAudioPlay();
        }, 50);
        return () => clearTimeout(timer);
      }
    }, [
      playingSecondVerseOfPair,
      isInTwoVerseSequence,
      currentVerseInSequence,
      handleAudioPlay,
    ]);

    useEffect(() => {
      const audioElement = audioRef.current;
      if (!audioElement) return;
      const handleTimeUpdateEvent = () => {
        if (!audioElement || audioElement.paused || !isAudioPlaying) return;
        const currentTime = audioElement.currentTime;
        let progress = 0;
        if (
          currentAudioInfo &&
          currentAudioInfo.start_ms !== undefined &&
          currentAudioInfo.end_ms !== undefined
        ) {
          const startTime = currentAudioInfo.start_ms / 1000;
          const endTime = currentAudioInfo.end_ms / 1000;
          const segmentDuration = endTime - startTime;
          if (segmentDuration > 0) {
            const elapsedInSegment = currentTime - startTime;
            progress = Math.max(
              0,
              Math.min(100, (elapsedInSegment / segmentDuration) * 100)
            );
          }
        } else if (audioElement.duration > 0 && !isNaN(audioElement.duration)) {
          progress = (currentTime / audioElement.duration) * 100;
        }
        if (onAudioProgressChange) onAudioProgressChange(progress);
      };
      const handleAudioEndedEvent = () => {
        if (audioStopTimerRef.current) clearTimeout(audioStopTimerRef.current);
        const currentExampleRef =
          isExampleMode && exampleOrder.length > currentExampleIndex
            ? exampleOrder[currentExampleIndex]
            : null;
        if (
          currentExampleRef &&
          currentExampleRef.nextVerseToPlay &&
          isInTwoVerseSequence &&
          currentVerseInSequence === "first"
        ) {
          setPlayingSecondVerseOfPair(currentExampleRef.nextVerseToPlay);
          setCurrentVerseInSequence("first_ended");
        } else {
          setIsAudioPlaying(false);
          setCurrentAudioInfo(null);
          setIsInTwoVerseSequence(false);
          setCurrentVerseInSequence(null);
          setPlayingSecondVerseOfPair(null);
          if (onAudioEnded) onAudioEnded();
          if (onAudioProgressChange) onAudioProgressChange(0);
        }
      };
      const handlePlayEvent = () => {
        setIsAudioPlaying(true);
        if (isInTwoVerseSequence) {
          const currentExampleRef =
            isExampleMode && exampleOrder.length > currentExampleIndex
              ? exampleOrder[currentExampleIndex]
              : null;
          if (
            currentVerseInSequence === null &&
            currentExampleRef?.nextVerseToPlay &&
            !playingSecondVerseOfPair
          ) {
            setCurrentVerseInSequence("first");
          } else if (
            currentVerseInSequence === "first_ended" &&
            playingSecondVerseOfPair
          ) {
            setCurrentVerseInSequence("second");
            setPlayingSecondVerseOfPair(null);
          } else if (
            currentVerseInSequence === "first" &&
            playingSecondVerseOfPair &&
            audioElement.src.includes(
              String(playingSecondVerseOfPair.verse).padStart(3, "0")
            )
          ) {
            setCurrentVerseInSequence("second");
            setPlayingSecondVerseOfPair(null);
          }
        }
      };
      const handlePauseEvent = () => {
        setIsAudioPlaying(false);
        if (audioStopTimerRef.current) clearTimeout(audioStopTimerRef.current);
        const isNaturalEndOfSegment =
          currentAudioInfo &&
          audioElement.currentTime >= currentAudioInfo.end_ms / 1000 - 0.1;
        const isNaturalEndOfFile = audioElement.ended;
        if (!isNaturalEndOfSegment && !isNaturalEndOfFile) {
          setCurrentAudioInfo(null);
          setPlayingSecondVerseOfPair(null);
          setIsInTwoVerseSequence(false);
          setCurrentVerseInSequence(null);
        } else if (isNaturalEndOfSegment && !isNaturalEndOfFile) {
          const currentExampleRef =
            isExampleMode && exampleOrder.length > currentExampleIndex
              ? exampleOrder[currentExampleIndex]
              : null;
          if (
            isInTwoVerseSequence &&
            currentVerseInSequence === "first" &&
            currentExampleRef?.nextVerseToPlay
          ) {
            setPlayingSecondVerseOfPair(currentExampleRef.nextVerseToPlay);
            setCurrentVerseInSequence("first_ended");
          } else {
            setCurrentAudioInfo(null);
            if (isInTwoVerseSequence && currentVerseInSequence === "second") {
              setIsInTwoVerseSequence(false);
              setCurrentVerseInSequence(null);
            }
            if (onAudioEnded) onAudioEnded();
            if (onAudioProgressChange) onAudioProgressChange(0);
          }
        }
      };
      const handleErrorEvent = (e: Event) => {
        setIsAudioPlaying(false);
        if (audioStopTimerRef.current) clearTimeout(audioStopTimerRef.current);
        setCurrentAudioInfo(null);
        setPlayingSecondVerseOfPair(null);
        setIsInTwoVerseSequence(false);
        setCurrentVerseInSequence(null);
      };
      audioElement.addEventListener("timeupdate", handleTimeUpdateEvent);
      audioElement.addEventListener("ended", handleAudioEndedEvent);
      audioElement.addEventListener("play", handlePlayEvent);
      audioElement.addEventListener("playing", handlePlayEvent);
      audioElement.addEventListener("pause", handlePauseEvent);
      audioElement.addEventListener("error", handleErrorEvent);
      return () => {
        audioElement.removeEventListener("timeupdate", handleTimeUpdateEvent);
        audioElement.removeEventListener("ended", handleAudioEndedEvent);
        audioElement.removeEventListener("play", handlePlayEvent);
        audioElement.removeEventListener("playing", handlePlayEvent);
        audioElement.removeEventListener("pause", handlePauseEvent);
        audioElement.removeEventListener("error", handleErrorEvent);
      };
    }, [
      isAudioPlaying,
      currentAudioInfo,
      onAudioProgressChange,
      onAudioEnded,
      onAudioPlaybackStateChange,
      isExampleMode,
      exampleOrder,
      currentExampleIndex,
      isInTwoVerseSequence,
      currentVerseInSequence,
      playingSecondVerseOfPair,
    ]);

    const handlePreviousSurah = useCallback(() => {
      if (onNavigateSurah && previousSurahNumber) {
        onNavigateSurah(previousSurahNumber);
      }
    }, [onNavigateSurah, previousSurahNumber]);

    const handleNextSurah = useCallback(() => {
      if (onNavigateSurah && nextSurahNumber) {
        onNavigateSurah(nextSurahNumber);
      }
    }, [onNavigateSurah, nextSurahNumber]);

    const handlePreviousExample = useCallback(() => {
      if (currentExampleIndex > 0 && !navigationInProgress) {
        if (isAudioPlaying && audioRef.current && !audioRef.current.paused)
          audioRef.current.pause();
        setPlayingSecondVerseOfPair(null);
        setIsInTwoVerseSequence(false);
        setCurrentVerseInSequence(null);
        setCurrentAudioInfo(null);
        if (audioStopTimerRef.current) clearTimeout(audioStopTimerRef.current);
        setNavigationInProgress(true);
        setCurrentExampleIndex((prev) => prev - 1);
        needsTextUpdateRef.current = true;
        setTimeout(() => setNavigationInProgress(false), 50);
      }
    }, [currentExampleIndex, navigationInProgress, isAudioPlaying]);

    const handleNextExample = useCallback(() => {
      if (
        currentExampleIndex < exampleOrder.length - 1 &&
        !navigationInProgress
      ) {
        if (isAudioPlaying && audioRef.current && !audioRef.current.paused)
          audioRef.current.pause();
        setPlayingSecondVerseOfPair(null);
        setIsInTwoVerseSequence(false);
        setCurrentVerseInSequence(null);
        setCurrentAudioInfo(null);
        if (audioStopTimerRef.current) clearTimeout(audioStopTimerRef.current);
        setNavigationInProgress(true);
        setCurrentExampleIndex((prev) => prev + 1);
        needsTextUpdateRef.current = true;
        setTimeout(() => setNavigationInProgress(false), 50);
      } else if (
        currentExampleIndex === exampleOrder.length - 1 &&
        exampleOrder.length > 0
      ) {
        setIsCompleted(true);
      }
    }, [
      currentExampleIndex,
      exampleOrder.length,
      navigationInProgress,
      isAudioPlaying,
    ]);

    const handleWordClickInternal = useCallback(
      (word: TajweedWordObj) => {
        if (onWordClick) onWordClick(word);
      },
      [onWordClick]
    );

    const getWordStyles = useCallback((word: TajweedWordObj): string => {
      if (!word) return "px-1 text-gray-400";
      let baseClasses =
        "relative px-1 transition-all duration-300 hover:scale-105";
      if (
        word.tajweedRule ||
        word.isKeyWord ||
        (word.letterHighlights && word.letterHighlights.length > 0)
      ) {
        baseClasses += " cursor-pointer";
      }
      return baseClasses;
    }, []);

    const renderTajweedWord = useCallback((word: TajweedWordObj) => {
      if (!word.text) return null;
      if (!word.letterHighlights || word.letterHighlights.length === 0) {
        return <span>{word.text}</span>;
      }
      const points = new Set<number>();
      points.add(0);
      points.add(word.text.length);
      word.letterHighlights.forEach((hl) => {
        points.add(hl.startIndex);
        points.add(hl.endIndex + 1);
      });
      const sortedPoints = Array.from(points)
        .sort((a, b) => a - b)
        .filter((p, i, arr) => i === 0 || p > arr[i - 1]);
      const segments = [];
      for (let i = 0; i < sortedPoints.length - 1; i++) {
        const start = sortedPoints[i];
        const end = sortedPoints[i + 1];
        if (start >= end || start >= word.text.length) continue;
        const segmentText = word.text.substring(
          start,
          Math.min(end, word.text.length)
        );
        if (!segmentText) continue;
        const midPoint = start + Math.floor(segmentText.length / 2);
        const activeHighlight = word.letterHighlights.find(
          (hl) => midPoint >= hl.startIndex && midPoint <= hl.endIndex
        );
        if (activeHighlight) {
          const ruleColor = getRuleColor(activeHighlight.ruleId);
          const hexToRgba = (hex: string, alpha: number = 0.25): string => {
            if (!/^#[0-9A-F]{6}$/i.test(hex)) {
              return "rgba(100, 100, 100, 0.25)";
            }
            const r = parseInt(hex.slice(1, 3), 16);
            const g = parseInt(hex.slice(3, 5), 16);
            const b = parseInt(hex.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
          };
          const style = {
            backgroundColor: hexToRgba(ruleColor),
            borderBottom: `2px solid ${ruleColor}`,
            paddingBottom: "1px",
            borderRadius: "2px",
          };
          segments.push(
            <span
              key={`${word.id}-${start}-${end}`}
              style={style}
              className="relative"
            >
              {segmentText}
            </span>
          );
        } else {
          segments.push(
            <span key={`${word.id}-${start}-${end}`}>{segmentText}</span>
          );
        }
      }
      return <>{segments}</>;
    }, []);

    const getCurrentExampleData = useCallback(() => {
      if (
        !isExampleMode ||
        exampleOrder.length === 0 ||
        currentExampleIndex >= exampleOrder.length
      ) {
        return {
          verseKey: null,
          words: null,
          explanation: null,
          exampleRef: null,
        };
      }
      const currentExampleRef = exampleOrder[currentExampleIndex];
      const exampleKeyToLookup =
        currentExampleRef.id ??
        (typeof currentExampleRef.verse === "number"
          ? currentExampleRef.verse
          : currentExampleIndex);

      const actualVerseNumberForGlyph =
        typeof currentExampleRef.verse === "number"
          ? currentExampleRef.verse
          : null;

      let currentWordsForDisplay: TajweedWordObj[] | null = null;
      const explanation = currentExampleRef.explanation || ruleContext?.reason;
      if (currentExampleRef.exampleText) {
        const normalizedText = normalizeArabicText(
          currentExampleRef.exampleText
        );
        const wordsFromText = normalizedText
          .split(/\s+/)
          .filter((word) => word.trim().length > 0);
        currentWordsForDisplay = wordsFromText.map((word, index) => {
          const ruleForHighlighting = activeRule?.id || null;
          const isKeyWordMatch =
            currentExampleRef.keyWords?.some((kw) =>
              normalizeArabicText(word).includes(normalizeArabicText(kw))
            ) || false;
          return {
            id: index,
            text: word,
            audio: "",
            tajweedRule: detectGeneralTajweedRule(word),
            highlighted:
              isKeyWordMatch ||
              currentExampleRef.wordIndices?.includes(index) ||
              false,
            verseNumber:
              typeof exampleKeyToLookup === "number" ? exampleKeyToLookup : 0,
            wordIndex: index,
            letterHighlights: identifyLetterHighlights(
              word,
              ruleForHighlighting,
              false,
              null,
              null
            ),
            isKeyWord: isKeyWordMatch,
            isWordStart: index === 0,
          };
        });
      } else if (
        typeof exampleKeyToLookup === "number" &&
        processedVerses[exampleKeyToLookup]
      ) {
        currentWordsForDisplay = processedVerses[exampleKeyToLookup].map(
          (wordObj) => {
            const ruleForHighlighting = activeRule?.id || null;
            const isKeyWordMatch =
              currentExampleRef.keyWords?.some((kw) =>
                normalizeArabicText(wordObj.text).includes(
                  normalizeArabicText(kw)
                )
              ) || false;
            return {
              ...wordObj,
              highlighted:
                isKeyWordMatch ||
                currentExampleRef.wordIndices?.includes(wordObj.wordIndex) ||
                false,
              isKeyWord: isKeyWordMatch,
              letterHighlights: identifyLetterHighlights(
                wordObj.text,
                ruleForHighlighting,
                false,
                null,
                null
              ),
            };
          }
        );
      }
      return {
        verseKey: actualVerseNumberForGlyph,
        words: currentWordsForDisplay,
        explanation,
        exampleRef: currentExampleRef,
      };
    }, [
      isExampleMode,
      exampleOrder,
      currentExampleIndex,
      processedVerses,
      identifyLetterHighlights,
      activeRule?.id,
      detectGeneralTajweedRule,
      ruleContext?.reason,
    ]);

    const renderFourLettersExamples = useCallback(() => {
      if (
        !isLessonMode ||
        !currentLessonSlide ||
        !currentLessonSlide.letterExamples ||
        currentLessonSlide.letterExamples.length === 0
      ) {
        return null;
      }
      const letterExamples = currentLessonSlide.letterExamples || [];
      if (letterExamples.length === 0) return null;
      return (
        <div className="w-full my-6">
          <h3 className="text-xl font-medium text-gray-900 mb-4 text-center md:text-left">
            {"The 4 Letters of Idgham with Ghunnah"}
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {letterExamples.map((ex, index) => (
              <div
                key={index}
                className="flex items-center p-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="flex-shrink-0 bg-gray-50 rounded-lg w-12 h-12 flex items-center justify-center border border-gray-100">
                  <span className="text-gray-900 text-2xl font-amiri-tajweed">
                    {ex.letter}
                  </span>
                </div>
                <div className="ml-4 flex-1 overflow-hidden">
                  <div className="text-sm font-medium text-gray-800">
                    {ex.transliteration}
                  </div>
                  <div className="text-base text-gray-700 mt-1 font-amiri-tajweed">
                    {ex.example}
                  </div>
                </div>
                {ex.audioUrl && (
                  <button
                    className="w-10 h-10 bg-gray-50 rounded-full flex items-center justify-center ml-2 text-gray-700 hover:bg-gray-100 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      new Audio(ex.audioUrl).play();
                    }}
                    aria-label={`Play audio for ${ex.transliteration}`}
                  >
                    <Play size={18} className="ml-0.5" />
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      );
    }, [isLessonMode, currentLessonSlide]);

    const WordWithBoundary = useCallback(
      ({ word, onClick }: { word: TajweedWordObj; onClick: () => void }) => {
        return (
          <span
            className={`${getWordStyles(word)} ${
              word.isWordStart && word.wordIndex > 0 ? "mr-1" : ""
            }`}
            onClick={onClick}
            role={
              word.tajweedRule ||
              word.isKeyWord ||
              (word.letterHighlights && word.letterHighlights.length > 0)
                ? "button"
                : undefined
            }
            tabIndex={
              word.tajweedRule ||
              word.isKeyWord ||
              (word.letterHighlights && word.letterHighlights.length > 0)
                ? 0
                : undefined
            }
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") onClick();
            }}
            aria-label={word.text}
          >
            <span className="relative group">
              {renderTajweedWord(word)}
              {word.isWordStart && word.wordIndex > 0 && (
                <span className="absolute -right-1 top-0 bottom-0 w-[1px] bg-gray-200 opacity-30"></span>
              )}
              <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-current transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 opacity-50"></span>
            </span>
          </span>
        );
      },
      [getWordStyles, renderTajweedWord]
    );

    const renderLessonExample = useCallback(() => {
      if (
        !isLessonMode ||
        !lessonExampleWords ||
        lessonExampleWords.length === 0
      ) {
        return (
          <div className="flex-1 flex items-center justify-center p-6">
            <div className="text-center">
              <p className="text-gray-500 mb-2">
                No example available for this lesson slide.
              </p>
            </div>
          </div>
        );
      }
      return (
        <div className="flex-1 overflow-auto p-6 flex flex-col items-center justify-center">
          <div
            className="flex flex-wrap text-6xl font-amiri-tajweed tracking-wide leading-relaxed relative max-w-6xl mx-auto space-x-2 justify-center"
            dir="rtl"
            lang="ar"
          >
            {lessonExampleWords.map((wordObj, wordIdx) => (
              <WordWithBoundary
                key={`lesson-word-${wordObj.id}-${wordIdx}`}
                word={wordObj}
                onClick={() => handleWordClickInternal(wordObj)}
              />
            ))}
          </div>
          {renderFourLettersExamples()}
        </div>
      );
    }, [
      isLessonMode,
      lessonExampleWords,
      handleWordClickInternal,
      renderFourLettersExamples,
      WordWithBoundary,
    ]);

    const shouldShowRangePicker = useMemo(
      () => mode === "regular" && totalAyahs > 60 && !selectedRange,
      [mode, totalAyahs, selectedRange]
    );

    const isLastExample = useMemo(
      () =>
        isExampleMode &&
        exampleOrder.length > 0 &&
        currentExampleIndex === exampleOrder.length - 1,
      [isExampleMode, currentExampleIndex, exampleOrder.length]
    );

    const isFirstExample = useMemo(
      () => isExampleMode && currentExampleIndex === 0,
      [isExampleMode, currentExampleIndex]
    );

    const processedVerseEntries = useMemo(
      () =>
        Object.entries(processedVerses || {}).sort(
          ([a], [b]) => parseInt(a) - parseInt(b)
        ),
      [processedVerses]
    );

    return (
      <div
        className="tajweed-display h-full w-full rounded-xl shadow-lg flex flex-col justify-between relative min-h-0 overflow-hidden text-sm border border-gray-200"
        style={{
          backgroundColor: "#ffffff",
          backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
          backgroundRepeat: "repeat",
        }}
      >
        <header className="flex-none h-14 md:h-16 sticky top-0 z-10 bg-white/80 backdrop-blur-md px-4 md:px-6 border-b border-gray-200">
          <div className="max-w-7xl mx-auto h-full flex items-center justify-between">
            <div className="flex items-center space-x-3 md:space-x-6">
              <span className="text-gray-700 font-medium hidden sm:inline">
                {isLessonMode ? "Lesson" : isExampleMode ? "Examples" : "Surah"}
              </span>
              <div className="flex items-center space-x-3">
                <h2 className="text-base md:text-lg font-semibold text-gray-900 tracking-tight">
                  {isLessonMode
                    ? lessonTitle || "Lesson"
                    : isExampleMode
                    ? activeRule?.name || "Examples"
                    : surah.englishName}
                </h2>
                {isExampleMode && exampleOrder.length > 0 && (
                  <span className="text-xs md:text-sm text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                    ({currentExampleIndex + 1}/{exampleOrder.length})
                  </span>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {!isLessonMode && (mode === "example" || mode === "regular") && (
                <button
                  onClick={handleAudioPlay}
                  className="inline-flex items-center space-x-2 px-3 py-2 md:px-4 md:py-2.5 bg-neutral-100 text-neutral-700 rounded-full transition-all duration-300 hover:bg-neutral-900 hover:text-white hover:scale-105 focus:outline-none focus:ring-2 focus:ring-teal-400 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={
                    (isExampleMode && !findAudioTimestampForItem()) ||
                    (mode === "regular" && (!surah || !surah.number))
                  }
                  aria-label={isAudioPlaying ? "Stop Audio" : "Play Audio"}
                >
                  {isAudioPlaying ? <Square size={16} /> : <Play size={16} />}
                  <span className="font-medium text-xs md:text-sm hidden sm:inline">
                    {isAudioPlaying ? "Stop Audio" : "Play Audio"}
                  </span>
                </button>
              )}
              {totalAyahs > 60 &&
                selectedRange &&
                mode === "regular" &&
                !shouldShowRangePicker &&
                !isExampleMode &&
                !isLessonMode && (
                  <button
                    onClick={onClearRangeSelection}
                    className="inline-flex items-center space-x-2 px-3 py-2 md:px-4 md:py-2.5 bg-neutral-100 text-neutral-700 rounded-full transition-all duration-300 hover:bg-neutral-900 hover:text-white hover:scale-105 focus:outline-none focus:ring-2 focus:ring-teal-400 focus:ring-offset-2"
                  >
                    <Undo2 size={16} />
                    <span className="font-medium text-xs md:text-sm hidden sm:inline">
                      Change Range
                    </span>
                  </button>
                )}
            </div>
          </div>
        </header>

        {shouldShowRangePicker ? (
          <div className="flex-1 flex flex-col items-center justify-center p-4 md:p-6 bg-gray-50">
            <h3 className="text-base md:text-lg font-semibold mb-3 text-gray-700">
              Select Verse Range ({totalAyahs} total Ayahs)
            </h3>
            <div
              className="w-full max-w-md flex flex-col space-y-2 overflow-y-auto p-2"
              style={{ maxHeight: "calc(100vh - 200px)" }}
            >
              {availableRanges.length > 0 ? (
                availableRanges.map((r) => (
                  <button
                    key={`${r.start}-${r.end}`}
                    onClick={() => onRangeSelectionRequest(r)}
                    className="w-full bg-white border border-gray-200 rounded-lg px-4 py-3 text-center text-base font-medium text-gray-800 hover:bg-teal-50 hover:border-teal-300 hover:shadow-sm transition-all duration-200"
                  >
                    Verses {r.start} - {r.end}
                  </button>
                ))
              ) : (
                <p className="text-gray-500 text-center">
                  No ranges available.
                </p>
              )}
            </div>
          </div>
        ) : isLessonMode ? (
          renderLessonExample()
        ) : isExampleMode ? (
          (() => {
            if (exampleOrder.length === 0) {
              return (
                <div className="flex-1 flex items-center justify-center p-4 md:p-6 text-center">
                  <div>
                    <p className="text-gray-500 mb-2">
                      No examples available for this rule.
                    </p>
                    <p className="text-gray-400 text-sm">
                      Try selecting a different tajweed rule.
                    </p>
                  </div>
                </div>
              );
            }
            const {
              verseKey,
              words: currentWordsToDisplay,
              explanation,
              exampleRef,
            } = getCurrentExampleData();
            if (!currentWordsToDisplay || currentWordsToDisplay.length === 0) {
              return (
                <div className="flex-1 flex items-center justify-center p-6 text-gray-500">
                  Loading example {currentExampleIndex + 1}...
                  {explanation && (
                    <p className="mt-2 text-xs italic">{explanation}</p>
                  )}
                </div>
              );
            }
            return (
              <div className="flex-1 overflow-auto p-4 md:p-6 flex flex-col items-center justify-center relative">
                {showSuccessOverlay && (
                  <div className="absolute inset-0 flex items-center justify-center z-30 bg-[#1a1a18]/10 rounded-xl backdrop-blur-sm">
                    <div className="bg-white rounded-full p-4 md:p-6 shadow-xl transform animate-success-bounce">
                      <Check size={32} className="text-green-500" />
                    </div>
                  </div>
                )}
                {explanation && (
                  <p className="mb-3 md:mb-4 text-xs md:text-sm text-center text-gray-600 italic max-w-2xl">
                    {explanation}
                  </p>
                )}
                <div
                  className="flex flex-wrap text-3xl md:text-4xl lg:text-5xl font-amiri-tajweed tracking-wide leading-relaxed relative max-w-full md:max-w-6xl mx-auto space-x-1 md:space-x-2 justify-center"
                  dir="rtl"
                  lang="ar"
                >
                  {currentWordsToDisplay.map((wordObj, wordIdx) => (
                    <WordWithBoundary
                      key={`example-word-${
                        exampleRef?.id || verseKey?.toString() || "unk"
                      }-${wordObj.id}-${wordIdx}`}
                      word={wordObj}
                      onClick={() => handleWordClickInternal(wordObj)}
                    />
                  ))}
                  {typeof verseKey === "number" && verseKey > 0 && (
                    <span className="px-1 md:px-2 text-gray-400 text-3xl md:text-4xl opacity-75 select-none self-center font-hafs">
                      {verseGlyphs[verseKey] || `(${verseKey})`}
                    </span>
                  )}
                </div>
                {isCompleted && isLastExample && (
                  <div className="mt-4 md:mt-6 p-2 md:p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-xs md:text-sm flex items-center justify-center">
                    <Check size={16} className="mr-2" /> All examples completed!
                  </div>
                )}
              </div>
            );
          })()
        ) : (
          <div
            className={`flex-1 overflow-auto p-4 md:p-6 flex ${
              processedVerseEntries.length <= 10 &&
              processedVerseEntries.length > 0
                ? "items-center justify-center"
                : "items-start justify-start"
            }`}
          >
            {processedVerseEntries.length === 0 &&
            propsVerses &&
            propsVerses.length > 0 ? (
              <div className="w-full text-center text-gray-500">
                Loading verses...
              </div>
            ) : (
              <div
                className="flex flex-wrap text-2xl md:text-3xl font-amiri-tajweed tracking-wide leading-relaxed relative max-w-full md:max-w-4xl mx-auto space-x-1 md:space-x-2 justify-start"
                dir="rtl"
                lang="ar"
              >
                {processedVerseEntries.map(([verseNumStr, verseWords]) => {
                  const verseNum = parseInt(verseNumStr, 10);
                  return (
                    <React.Fragment key={`verse-${verseNum}`}>
                      {verseWords.map((wordObj, wordIdx) => (
                        <WordWithBoundary
                          key={`word-${verseNum}-${wordObj.id}-${wordIdx}`}
                          word={{ ...wordObj, isWordStart: wordIdx === 0 }}
                          onClick={() => handleWordClickInternal(wordObj)}
                        />
                      ))}
                      <span className="px-1 md:px-2 text-gray-400 text-2xl md:text-3xl opacity-75 select-none self-center font-hafs">
                        {verseGlyphs[verseNum] || `(${verseNum})`}
                      </span>
                    </React.Fragment>
                  );
                })}
              </div>
            )}
          </div>
        )}

        <audio ref={audioRef} className="hidden" />

        <div className="flex-none w-full border-t border-gray-200 bg-white/80 backdrop-blur-md text-xs md:text-sm">
          <div className="max-w-4xl mx-auto px-4 md:px-6 py-2 md:py-3 flex justify-between items-center">
            {isLessonMode ? (
              <div className="text-gray-500 text-center w-full">
                Interactive lesson content.
              </div>
            ) : isExampleMode ? (
              <>
                <button
                  className={`flex items-center gap-1 px-2 py-1 md:px-3 md:py-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-all duration-200 ${
                    isFirstExample || navigationInProgress
                      ? "opacity-50 cursor-not-allowed"
                      : ""
                  }`}
                  onClick={handlePreviousExample}
                  disabled={isFirstExample || navigationInProgress}
                  aria-label="Previous Example"
                >
                  <ChevronLeft size={16} />{" "}
                  <span className="font-medium hidden sm:inline">Prev</span>
                </button>
                <div className="text-gray-500">
                  {exampleOrder.length > 0
                    ? `${currentExampleIndex + 1} / ${exampleOrder.length}`
                    : "0/0"}
                </div>
                <button
                  className={`flex items-center gap-1 px-2 py-1 md:px-3 md:py-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-all duration-200 ${
                    isLastExample || navigationInProgress
                      ? "opacity-50 cursor-not-allowed"
                      : ""
                  }`}
                  onClick={handleNextExample}
                  disabled={
                    isLastExample ||
                    navigationInProgress ||
                    exampleOrder.length === 0
                  }
                  aria-label="Next Example"
                >
                  <span className="font-medium hidden sm:inline">Next</span>{" "}
                  <ChevronRight size={16} />
                </button>
              </>
            ) : (
              <>
                <button
                  className="flex items-center gap-1 px-2 py-1 md:px-3 md:py-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={handlePreviousSurah}
                  disabled={!previousSurahNumber || !onNavigateSurah}
                  aria-label="Previous Surah"
                >
                  <ChevronLeft size={16} />{" "}
                  <span className="font-medium hidden sm:inline">Prev</span>
                </button>
                <button
                  className="flex items-center gap-1 px-2 py-1 md:px-3 md:py-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-all duration-200"
                  onClick={() => {
                    if (audioRef.current) {
                      audioRef.current.currentTime = 0;
                      if (!isAudioPlaying) handleAudioPlay();
                      else {
                        audioRef.current
                          .play()
                          .catch((e) =>
                            console.error("Restart audio play error:", e)
                          );
                      }
                    }
                  }}
                  aria-label="Restart Audio"
                >
                  <RotateCcw size={16} />{" "}
                  <span className="font-medium hidden sm:inline">Restart</span>
                </button>
                <button
                  className="flex items-center gap-1 px-2 py-1 md:px-3 md:py-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={handleNextSurah}
                  disabled={!nextSurahNumber || !onNavigateSurah}
                  aria-label="Next Surah"
                >
                  <span className="font-medium hidden sm:inline">Next</span>{" "}
                  <ChevronRight size={16} />
                </button>
              </>
            )}
          </div>
        </div>

        <style jsx global>{`
          .font-amiri-tajweed {
            font-family: Amiri, "Times New Roman", serif;
            line-height: 2.2;
            font-weight: 400;
          }
          .font-hafs {
            font-family: "Hafs", sans-serif !important;
          }
          @keyframes success-bounce {
            0% {
              transform: scale(0.5);
              opacity: 0;
            }
            50% {
              transform: scale(1.1);
              opacity: 1;
            }
            70% {
              transform: scale(0.9);
            }
            100% {
              transform: scale(1);
              opacity: 1;
            }
          }
          .animate-success-bounce {
            animation: success-bounce 0.5s
              cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
          }
          *:focus-visible {
            outline: 2px solid teal;
            outline-offset: 2px;
            border-radius: 2px;
          }
        `}</style>
      </div>
    );
  }
);

TajweedDisplay.displayName = "TajweedDisplay";
export default TajweedDisplay;

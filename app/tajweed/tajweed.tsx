// Tajweed.tsx
"use client";

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  FC,
  useRef,
} from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  Check,
  Mic,
  Square,
  CheckCircle,
} from "lucide-react";
import LoadingPage from "../memorization/loading";
import TajweedDisplay, {
  TajweedRule, // Already correctly typed
  TajweedWordObj,
  <PERSON>erseR<PERSON><PERSON>,
  RuleContext, // Already correctly typed from props
  TajweedDisplayHandle,
} from "./TajweedDisplay";
import { tajweedExamples } from "./tajweedExamples";
import { tajweedLessons, LessonSlide, TajweedLesson } from "./tajweedLessons";
import TajweedFeedbackSection from "./tajweedFeedbackSection";
import { isValidRuleId } from "./tajweedPatterns";

// Define the interfaces for TajweedExample (if still needed locally, otherwise rely on imports)
interface TajweedExample {
  surah: number;
  verse: number;
  exampleText: string;
  explanation: string;
  keyWords: string[];
}

interface Window {
  webkitAudioContext: typeof AudioContext;
}

type Surah = {
  id?: number;
  number: number;
  name: string;
  englishName: string;
  englishNameTranslation: string;
  revelationPlace: string;
  numberOfAyahs: number;
};

type Verse = {
  surahNumber: number;
  verseNumber: number;
  text: string;
};

interface Violation {
  rule: string;
  message: string;
  severity: "critical" | "moderate" | "mild" | "stylistic";
  word?: string;
  colorClass?: string;
  bgColorClass?: string;
  borderColorClass?: string;
  comparisonData?: {
    userValue: string;
    correctValue: string;
  };
}

interface RecitationContext {
  speed: string;
  style?: string;
  displayName?: string;
  description?: string;
  recommendedFocus?: string;
}

type Props = {
  sessionId: number;
  userId: string;
  surahNumber: number;
  verses: Verse[];
  initialPercentage: number;
  versesRange: string;
  surahData: Surah;
  userSubscription: {
    isActive: boolean;
  } | null;
  initialHearts: number;
  ruleId?: string;
  ruleName?: string;
  ruleArabicName?: string;
  ruleContext?: RuleContext | null; // Prop for initial rule context
  verseStartIndex?: number;
  verseEndIndex?: number;
  mode?: string;
};

function audioBufferToWav(buffer: AudioBuffer): Blob {
  const numOfChan = buffer.numberOfChannels;
  const length = buffer.length * numOfChan * 2 + 44;
  const arrayBuffer = new ArrayBuffer(length);
  const view = new DataView(arrayBuffer);
  let offset = 0;

  function writeString(str: string) {
    for (let i = 0; i < str.length; i++) {
      view.setUint8(offset + i, str.charCodeAt(i));
    }
    offset += str.length;
  }

  writeString("RIFF");
  view.setUint32(offset, length - 8, true);
  offset += 4;
  writeString("WAVE");
  writeString("fmt ");
  view.setUint32(offset, 16, true);
  offset += 4;
  view.setUint16(offset, 1, true);
  offset += 2;
  view.setUint16(offset, numOfChan, true);
  offset += 2;
  view.setUint32(offset, buffer.sampleRate, true);
  offset += 4;
  view.setUint32(offset, buffer.sampleRate * numOfChan * 2, true);
  offset += 4;
  view.setUint16(offset, numOfChan * 2, true);
  offset += 2;
  view.setUint16(offset, 16, true);
  offset += 2;
  writeString("data");
  view.setUint32(offset, length - offset - 4, true);
  offset += 4;

  const channels: Float32Array[] = [];
  for (let ch = 0; ch < numOfChan; ch++) {
    channels.push(buffer.getChannelData(ch));
  }
  let idx = 0;
  for (let i = 0; i < buffer.length; i++) {
    for (let ch = 0; ch < numOfChan; ch++) {
      const sample = Math.max(-1, Math.min(1, channels[ch][i]));
      const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
      view.setInt16(offset + idx * 2, intSample, true);
      idx++;
    }
  }
  return new Blob([view], { type: "audio/wav" });
}

async function resampleAudioBuffer(
  audioBuffer: AudioBuffer,
  targetSampleRate: number
): Promise<AudioBuffer> {
  const currentSampleRate = audioBuffer.sampleRate;
  if (currentSampleRate === targetSampleRate) {
    return audioBuffer;
  }
  const numberOfChannels = audioBuffer.numberOfChannels;
  const offlineContext = new OfflineAudioContext(
    numberOfChannels,
    (audioBuffer.length * targetSampleRate) / currentSampleRate,
    targetSampleRate
  );
  const bufferSource = offlineContext.createBufferSource();
  bufferSource.buffer = audioBuffer;
  bufferSource.connect(offlineContext.destination);
  bufferSource.start();
  return await offlineContext.startRendering();
}

const tajweedRulesData: TajweedRule[] = [
  {
    id: "ghunnah",
    name: "Ghunnah",
    arabicName: "غُنَّة",
    description: "Nasalization when pronouncing the letters Noon and Meem",
    color: "#66BB6A",
  },
  {
    id: "idgham",
    name: "Idgham",
    arabicName: "إدغام",
    description: "Merging of certain letters",
    color: "#42A5F5",
  },
  {
    id: "idgham-ghunnah",
    name: "Idgham with Ghunnah",
    arabicName: "إدغام بغنة",
    description: "Merging with nasalization",
    color: "#1e40af",
  },
  {
    id: "idgham-without-ghunnah",
    name: "Idgham without Ghunnah",
    arabicName: "إدغام بلا غنة",
    description: "Merging without nasalization",
    color: "#0ea5e9",
  },
  {
    id: "ikhfa",
    name: "Ikhfa",
    arabicName: "إخفاء",
    description:
      "Partial nasalization when Noon Sakinah or Tanween is followed by specific letters",
    color: "#AB47BC",
  },
  {
    id: "ikhfa-shafawi",
    name: "Ikhfa Shafawi",
    arabicName: "إخفاء شفوي",
    description: "Lip concealment of meem sakin before baa",
    color: "#6366f1",
  },
  {
    id: "iqlab",
    name: "Iqlab",
    arabicName: "إقلاب",
    description:
      "Converting Noon Sakinah or Tanween to Meem when followed by Baa",
    color: "#EF5350",
  },
  {
    id: "izhar",
    name: "Izhar",
    arabicName: "إظهار",
    description: "Clear pronunciation before throat letters",
    color: "#16a34a",
  },
  {
    id: "izhar-shafawi",
    name: "Izhar Shafawi",
    arabicName: "إظهار شفوي",
    description: "Clear pronunciation of meem sakin before non-labial letters",
    color: "#16a34a",
  },
  {
    id: "qalqalah",
    name: "Qalqalah",
    arabicName: "قلقلة",
    description:
      "Vibration in pronunciation of certain letters when they have sukoon",
    color: "#FFA726",
  },
  {
    id: "madd-natural",
    name: "Natural Madd",
    arabicName: "مد طبيعي",
    description: "Natural elongation for 2 counts",
    color: "#0891b2",
  },
  {
    id: "madd-hamza",
    name: "Hamza Madd",
    arabicName: "مد همزة",
    description: "Elongation related to hamza",
    color: "#0891b2",
  },
  {
    id: "madd-sukoon",
    name: "Sukoon Madd",
    arabicName: "مد سكون",
    description: "Elongation before sukoon",
    color: "#0891b2",
  },
  {
    id: "tafkheem-letters",
    name: "Tafkheem Letters",
    arabicName: "حروف تفخيم",
    description: "Heavy pronunciation letters",
    color: "#b91c1c",
  },
  {
    id: "tafkheem-ra",
    name: "Tafkheem Ra",
    arabicName: "تفخيم الراء",
    description: "Heavy pronunciation of Ra",
    color: "#b91c1c",
  },
  {
    id: "tarqeeq-ra",
    name: "Tarqeeq Ra",
    arabicName: "ترقيق الراء",
    description: "Light pronunciation of Ra",
    color: "#f97316",
  },
  {
    id: "laam-allah",
    name: "Laam Allah",
    arabicName: "لام الجلالة",
    description: "Laam in the word Allah",
    color: "#9333ea",
  },
  {
    id: "laam-qamariyyah",
    name: "Laam Qamariyyah",
    arabicName: "لام قمرية",
    description: "Clear laam before moon letters",
    color: "#9333ea",
  },
  {
    id: "laam-shamsiyyah",
    name: "Laam Shamsiyyah",
    arabicName: "لام شمسية",
    description: "Silent laam before sun letters",
    color: "#9333ea",
  },
  {
    id: "makharij-jawf",
    name: "Jawf",
    arabicName: "الجوف",
    description: "Oral cavity articulation",
    color: "#84cc16",
  },
  {
    id: "makharij-halq",
    name: "Halq",
    arabicName: "الحلق",
    description: "Throat articulation",
    color: "#84cc16",
  },
  {
    id: "makharij-lisan",
    name: "Lisan",
    arabicName: "اللسان",
    description: "Tongue articulation",
    color: "#84cc16",
  },
  {
    id: "makharij-shafatain",
    name: "Shafatain",
    arabicName: "الشفتان",
    description: "Lip articulation",
    color: "#84cc16",
  },
  {
    id: "makharij-khayshum",
    name: "Khayshum",
    arabicName: "الخيشوم",
    description: "Nasal cavity",
    color: "#84cc16",
  },
  {
    id: "hamzat-wasl",
    name: "Hamzatul Wasl",
    arabicName: "همزة الوصل",
    description: "Connecting hamza",
    color: "#3b82f6",
  },
  {
    id: "hamzat-qat",
    name: "Hamzatul Qat",
    arabicName: "همزة القطع",
    description: "Cutting hamza",
    color: "#3b82f6",
  },
  {
    id: "waqf-taam",
    name: "Waqf Taam",
    arabicName: "وقف تام",
    description: "Complete stop",
    color: "#8b5cf6",
  },
  {
    id: "waqf-kaafi",
    name: "Waqf Kaafi",
    arabicName: "وقف كافي",
    description: "Sufficient stop",
    color: "#8b5cf6",
  },
  {
    id: "waqf-hasan",
    name: "Waqf Hasan",
    arabicName: "وقف حسن",
    description: "Good stop",
    color: "#8b5cf6",
  },
  {
    id: "waqf-qabih",
    name: "Waqf Qabih",
    arabicName: "وقف قبيح",
    description: "Ugly stop",
    color: "#8b5cf6",
  },
];

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case "critical":
      return {
        dot: "bg-red-500",
        text: "text-red-600",
        bg: "bg-white",
        border: "border-gray-200",
      };
    case "moderate":
      return {
        dot: "bg-amber-500",
        text: "text-amber-600",
        bg: "bg-white",
        border: "border-gray-200",
      };
    case "mild":
      return {
        dot: "bg-yellow-500",
        text: "text-yellow-600",
        bg: "bg-white",
        border: "border-gray-200",
      };
    case "stylistic":
      return {
        dot: "bg-purple-400",
        text: "text-purple-600",
        bg: "bg-white",
        border: "border-gray-200",
      };
    default:
      return {
        dot: "bg-gray-400",
        text: "text-gray-600",
        bg: "bg-white",
        border: "border-gray-200",
      };
  }
};

const Tajweed: FC<Props> = ({
  surahNumber,
  verses,
  surahData,
  initialHearts,
  ruleId: initialRuleIdProp,
  ruleName: initialRuleNameProp,
  ruleContext: initialRuleContextProp,
  verseStartIndex,
  verseEndIndex,
  mode: initialModeProp,
}) => {
  const router = useRouter();
  const tajweedDisplayRef = useRef<TajweedDisplayHandle>(null);

  const [lessonMode, setLessonMode] = useState<boolean>(false);
  const [currentLessonSlideNumber, setCurrentLessonSlideNumber] =
    useState<number>(1);
  const [currentLesson, setCurrentLesson] = useState<TajweedLesson | null>(
    null
  );

  const [effectiveModeForDisplay, setEffectiveModeForDisplay] = useState<
    string | undefined
  >(initialModeProp);

  const [highlightedPattern, setHighlightedPattern] = useState<string | null>(
    null
  );
  type HighlightIndexRange = { start: number; end: number };
  const [highlightedLetterIndices, setHighlightedLetterIndices] = useState<
    HighlightIndexRange[]
  >([]);

  const [isGlobalAudioPlaying, setIsGlobalAudioPlaying] =
    useState<boolean>(false);
  const [audioProgress, setAudioProgress] = useState<number>(0);

  const [debugInfo, setDebugInfo] = useState<{
    lastRuleId?: string;
    lastActiveRule?: string;
    lastLessonId?: string;
    timestamp?: number;
  }>({});

  const [activeRule, setActiveRule] = useState<TajweedRule | null>(null);
  const currentContextRuleIdRef = useRef<string | undefined>(initialRuleIdProp);
  const [loading, setLoading] = useState<boolean>(true);
  const [hearts, setHearts] = useState<number>(initialHearts);
  const [selectedTab, setSelectedTab] = useState<string>("Assistant");
  const [score, setScore] = useState<number>(0);
  const [hasBeenAssessed, setHasBeenAssessed] = useState<boolean>(false);
  const [isSuccessful, setIsSuccessful] = useState<boolean>(false);
  const [currentExampleIdxForParent, setCurrentExampleIdxForParent] =
    useState<number>(0);
  const [totalExamples, setTotalExamples] = useState<number>(0);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null
  );
  const [recording, setRecording] = useState<boolean>(false);
  const audioChunks = useRef<Blob[]>([]);
  const [feedbackLoading, setFeedbackLoading] = useState<boolean>(false);
  const [violations, setViolations] = useState<Violation[]>([]);
  const [feedbackHTML, setFeedbackHTML] = useState<string>("");
  const [phonemes, setPhonemes] = useState<string[]>([]);
  const [recitationContext, setRecitationContext] =
    useState<RecitationContext | null>({ speed: "normal" });
  const [displayRuleContext, setDisplayRuleContext] =
    useState<RuleContext | null>(initialRuleContextProp || null);
  const [selectedRange, setSelectedRange] = useState<VerseRange | null>(null);
  const [totalAyahsInSurah, setTotalAyahsInSurah] = useState<number>(0);
  const [currentDisplayedVerseText, setCurrentDisplayedVerseText] =
    useState<string>("");
  const latestVerseTextRef = useRef<string>("");
  const lastTextUpdateTimestamp = useRef<number>(Date.now());
  const navigationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    currentContextRuleIdRef.current = initialRuleIdProp;
    if (process.env.NODE_ENV === "development") {
      setDebugInfo({ lastRuleId: initialRuleIdProp, timestamp: Date.now() });
    }
    let determinedMode = initialModeProp;
    let determinedLessonMode = false;
    let lessonToLoad: TajweedLesson | null = null;
    if (initialRuleIdProp && tajweedLessons[initialRuleIdProp]) {
      lessonToLoad = tajweedLessons[initialRuleIdProp];
      determinedLessonMode = true;
      determinedMode = "lesson";
    } else if (initialRuleIdProp && tajweedExamples[initialRuleIdProp]) {
      determinedMode = "example";
    } else if (!initialRuleIdProp && initialModeProp === "lesson") {
      const firstLessonKey = Object.keys(tajweedLessons)[0];
      if (firstLessonKey && tajweedLessons[firstLessonKey]) {
        lessonToLoad = tajweedLessons[firstLessonKey];
        currentContextRuleIdRef.current = firstLessonKey;
        determinedLessonMode = true;
        determinedMode = "lesson";
      }
    } else if (!initialRuleIdProp && !initialModeProp) {
      determinedMode = "regular";
    }
    setEffectiveModeForDisplay(determinedMode);
    setLessonMode(determinedLessonMode);
    if (lessonToLoad) {
      setCurrentLesson(lessonToLoad);
      setCurrentLessonSlideNumber(1);
      const slideForInit = lessonToLoad.slides.find((s) => s.id === 1);
      if (slideForInit) {
        setHighlightedPattern(slideForInit.highlightPattern || null);
        setHighlightedLetterIndices(slideForInit.highlightLetterIndices || []);
      }
      const lessonRule = tajweedRulesData.find(
        (r) => r.id === lessonToLoad.ruleId
      );
      if (lessonRule) setActiveRule(lessonRule);
    } else if (initialRuleIdProp) {
      const ruleData = tajweedRulesData.find((r) => r.id === initialRuleIdProp);
      if (ruleData) setActiveRule(ruleData);
    }
  }, [initialModeProp, initialRuleIdProp]);

  const currentSlideData: LessonSlide | undefined = useMemo(() => {
    if (!currentLesson || !currentLessonSlideNumber) return undefined;
    return currentLesson.slides.find((s) => s.id === currentLessonSlideNumber);
  }, [currentLesson, currentLessonSlideNumber]);

  const isLastSlide = useMemo(() => {
    return currentSlideData?.isLastSlide || false;
  }, [currentSlideData]);

  const updateHighlightedPatterns = useCallback(
    (slideId: number, lesson?: TajweedLesson | null) => {
      const lessonToUse = lesson || currentLesson;
      if (!lessonToUse) return;
      const slide = lessonToUse.slides.find((s) => s.id === slideId);
      if (!slide) return;
      setHighlightedPattern(slide.highlightPattern || null);
      setHighlightedLetterIndices(slide.highlightLetterIndices || []);
    },
    [currentLesson]
  );

  const handlePreviousSlide = useCallback(() => {
    if (currentLessonSlideNumber > 1) {
      const newSlideNumber = currentLessonSlideNumber - 1;
      setCurrentLessonSlideNumber(newSlideNumber);
      updateHighlightedPatterns(newSlideNumber);
    }
  }, [currentLessonSlideNumber, updateHighlightedPatterns]);

  const handleNextSlide = useCallback(() => {
    if (
      currentLesson &&
      currentLessonSlideNumber < currentLesson.slides.length
    ) {
      const newSlideNumber = currentLessonSlideNumber + 1;
      setCurrentLessonSlideNumber(newSlideNumber);
      updateHighlightedPatterns(newSlideNumber);
    }
  }, [currentLesson, currentLessonSlideNumber, updateHighlightedPatterns]);

  const handleFinishLesson = useCallback(() => {
    const ruleIdForExamples = currentContextRuleIdRef.current;
    setLessonMode(false);
    setEffectiveModeForDisplay("example");
    setHighlightedPattern(null);
    setHighlightedLetterIndices([]);
    if (ruleIdForExamples) {
      const ruleData = tajweedRulesData.find((r) => r.id === ruleIdForExamples);
      if (ruleData) setActiveRule(ruleData);
      router.push(`/tajweed?ruleId=${ruleIdForExamples}&mode=example`);
    } else {
      router.push(`/tajweed?mode=example`);
    }
  }, [router]);

  const nextSurahNumber: number | null =
    surahNumber < 114 ? surahNumber + 1 : null;
  const previousSurahNumber: number | null =
    surahNumber > 1 ? surahNumber - 1 : null;

  const handleNavigateSurah = useCallback(
    (targetSurah: number): void => {
      const ruleIdToPreserve = currentContextRuleIdRef.current;
      const currentMode = effectiveModeForDisplay || "regular";
      if (ruleIdToPreserve) {
        router.push(
          `/tajweed?ruleId=${ruleIdToPreserve}&surahNumber=${targetSurah}&mode=${currentMode}`
        );
      } else {
        router.push(`/tajweed?surahNumber=${targetSurah}&mode=${currentMode}`);
      }
    },
    [router, effectiveModeForDisplay]
  );

  useEffect(() => {
    if (currentDisplayedVerseText) {
      latestVerseTextRef.current = currentDisplayedVerseText;
      lastTextUpdateTimestamp.current = Date.now();
    }
  }, [currentDisplayedVerseText]);

  useEffect(() => {
    if (navigationTimeoutRef.current) {
      clearTimeout(navigationTimeoutRef.current);
      navigationTimeoutRef.current = null;
    }
    if (isSuccessful && effectiveModeForDisplay === "example") {
      if (tajweedDisplayRef.current) {
        tajweedDisplayRef.current.showSuccessMessage();
      }
      navigationTimeoutRef.current = setTimeout(() => {
        if (tajweedDisplayRef.current) {
          const isCurrentlyLastExample =
            tajweedDisplayRef.current.isLastExample();
          if (!isCurrentlyLastExample) {
            const navigationSuccessful =
              tajweedDisplayRef.current.nextExample();
            if (navigationSuccessful) {
              const newIndex =
                tajweedDisplayRef.current.getCurrentExampleIndex();
              setCurrentExampleIdxForParent(newIndex);
              setIsSuccessful(false);
              setHasBeenAssessed(false);
              setViolations([]);
              setScore(0);
              setFeedbackHTML("");
            }
          } else {
            const ruleIdToUse = currentContextRuleIdRef.current;
            if (ruleIdToUse) {
              router.push(`/tajweed?complete=true&ruleId=${ruleIdToUse}`);
            }
          }
        }
      }, 3000);
    }
    return () => {
      if (navigationTimeoutRef.current) {
        clearTimeout(navigationTimeoutRef.current);
      }
    };
  }, [isSuccessful, effectiveModeForDisplay, router]);

  const getCurrentlyDisplayedVerseText = useCallback((): string => {
    if (
      latestVerseTextRef.current &&
      latestVerseTextRef.current.trim().length > 0
    ) {
      return latestVerseTextRef.current;
    }
    if (
      currentDisplayedVerseText &&
      currentDisplayedVerseText.trim().length > 0
    ) {
      return currentDisplayedVerseText;
    }
    if (lessonMode && currentSlideData && currentSlideData.arabicExample) {
      return currentSlideData.arabicExample;
    }
    const contextRuleId = currentContextRuleIdRef.current;
    if (
      effectiveModeForDisplay === "example" &&
      contextRuleId &&
      tajweedExamples[contextRuleId]
    ) {
      const examples = tajweedExamples[contextRuleId].examples;
      if (
        examples &&
        examples.length > 0 &&
        examples[currentExampleIdxForParent]
      ) {
        return examples[currentExampleIdxForParent].exampleText || "";
      }
    }
    if (selectedRange && verses && verses.length > 0) {
      const { start, end } = selectedRange;
      const filteredVerses = verses.filter(
        (v) => v.verseNumber >= start && v.verseNumber <= end
      );
      if (filteredVerses.length > 0)
        return filteredVerses.map((v) => v.text).join(" ");
    }
    if (verses && verses.length > 0) return verses[0].text;
    return "";
  }, [
    currentDisplayedVerseText,
    verses,
    selectedRange,
    lessonMode,
    currentSlideData,
    effectiveModeForDisplay,
    currentExampleIdxForParent,
  ]);

  const handleAudioSubmit = useCallback(async () => {
    if (audioChunks.current.length === 0) return;
    const currentVerseTextForAssessment =
      latestVerseTextRef.current || getCurrentlyDisplayedVerseText();
    if (
      !currentVerseTextForAssessment ||
      currentVerseTextForAssessment.trim().length === 0
    ) {
      console.error(
        "[Tajweed] handleAudioSubmit: No text available for assessment!"
      );
      return;
    }
    const ruleIdForAssessment = currentContextRuleIdRef.current;
    const webmBlob = new Blob(audioChunks.current, { type: "audio/webm" });
    audioChunks.current = [];
    setFeedbackLoading(true);

    try {
      const arrayBuf = await webmBlob.arrayBuffer();
      const AudioContextClass =
        window.AudioContext || (window as any).webkitAudioContext;
      if (!AudioContextClass) throw new Error("AudioContext is not supported.");
      const audioCtx = new AudioContextClass();
      const decodedAudioBuffer = await audioCtx.decodeAudioData(arrayBuf);
      const targetSampleRate = 16000;
      const resampledBuffer = await resampleAudioBuffer(
        decodedAudioBuffer,
        targetSampleRate
      );
      const wavBlob = audioBufferToWav(resampledBuffer);
      const form = new FormData();
      form.append("audio", wavBlob, "recording.wav");
      if (ruleIdForAssessment) form.append("ruleId", ruleIdForAssessment);
      if (
        !currentVerseTextForAssessment ||
        currentVerseTextForAssessment.trim().length === 0
      ) {
        setFeedbackLoading(false);
        return;
      }
      form.append("verseText", currentVerseTextForAssessment);
      form.append("assessmentType", "tajweed");

      const res = await fetch("/api/tajweed", { method: "POST", body: form });
      const json = await res.json();

      if (!res.ok) {
        console.error(
          "Assessment API Error:",
          json.error || `Status ${res.status}`
        );
        setViolations([]);
        setScore(0);
        setHasBeenAssessed(false);
        setIsSuccessful(false);
      } else {
        const assessmentScore = json.score ?? 0;
        setScore(assessmentScore);
        setHasBeenAssessed(true);
        const isAboveThreshold = assessmentScore >= 80;
        setIsSuccessful(isAboveThreshold);
        if (!isAboveThreshold) {
          const enhancedViolations =
            json.violations?.map((v: Violation) => {
              const colors = getSeverityColor(v.severity);
              return {
                ...v,
                colorClass: colors.text,
                bgColorClass: colors.bg,
                borderColorClass: colors.border,
              };
            }) || [];
          setViolations(enhancedViolations);
          if (json.summaryHTML) setFeedbackHTML(json.summaryHTML);
          if (json.phonemes) setPhonemes(json.phonemes);
          if (json.recitationContext)
            setRecitationContext(json.recitationContext);
        } else {
          setViolations([]);
          setFeedbackHTML("");
          setHearts((prev) => prev + 1);
        }
      }
    } catch (e: any) {
      console.error("Error during audio processing/submission:", e);
      setViolations([]);
      setScore(0);
      setHasBeenAssessed(false);
      setIsSuccessful(false);
    } finally {
      setFeedbackLoading(false);
    }
  }, [getCurrentlyDisplayedVerseText]);

  useEffect(() => {
    navigator.mediaDevices
      .getUserMedia({ audio: true })
      .then((stream) => {
        const recorder = new MediaRecorder(stream);
        recorder.ondataavailable = (e) => audioChunks.current.push(e.data);
        recorder.onstop = handleAudioSubmit;
        setMediaRecorder(recorder);
      })
      .catch((err) => console.error("Mic access error:", err));

    if (
      effectiveModeForDisplay === "example" &&
      currentContextRuleIdRef.current &&
      tajweedExamples[currentContextRuleIdRef.current]
    ) {
      const examplesCount =
        tajweedExamples[currentContextRuleIdRef.current].examples?.length || 0;
      setTotalExamples(examplesCount);
      setCurrentExampleIdxForParent(0);
      const currentRuleData = tajweedExamples[
        currentContextRuleIdRef.current
      ] as unknown as RuleContext;
      if (currentRuleData) {
        setDisplayRuleContext({
          ruleId: currentContextRuleIdRef.current,
          ruleName:
            initialRuleNameProp ||
            currentRuleData.ruleName ||
            currentContextRuleIdRef.current,
          ruleArabicName:
            initialRuleContextProp?.ruleArabicName ||
            currentRuleData.ruleArabicName ||
            "",
          keyWords: currentRuleData.keyWords || [],
          reason: currentRuleData.reason || "",
          difficultyLevel: currentRuleData.difficultyLevel || "beginner",
          practiceNotes: currentRuleData.practiceNotes || "",
          examples: currentRuleData.examples || [],
        });
      }
    } else if (lessonMode && currentLesson) {
      setDisplayRuleContext({
        ruleId: currentLesson.ruleId,
        ruleName: currentLesson.title,
        ruleArabicName: activeRule?.arabicName || "",
        keyWords: [],
        reason: currentLesson.introduction,
        difficultyLevel: currentLesson.difficultyLevel,
        practiceNotes: "",
        examples: [],
      });
    } else if (initialRuleContextProp) {
      setDisplayRuleContext(initialRuleContextProp);
    }
  }, [
    initialRuleIdProp,
    effectiveModeForDisplay,
    lessonMode,
    currentLesson,
    initialRuleContextProp,
    initialRuleNameProp,
    handleAudioSubmit,
    activeRule,
  ]);

  const handleVerseTextChange = useCallback((text: string) => {
    if (!text || !text.trim()) {
      return;
    }
    latestVerseTextRef.current = text;
    lastTextUpdateTimestamp.current = Date.now();
    setCurrentDisplayedVerseText(text);
  }, []);

  const handleAudioPlaybackStateUpdate = useCallback((isPlaying: boolean) => {
    setIsGlobalAudioPlaying(isPlaying);
  }, []);

  const handleAudioPlayClick = useCallback(() => {
    if (!tajweedDisplayRef.current) return;
    if (isGlobalAudioPlaying) {
      tajweedDisplayRef.current.pauseAudio();
    } else {
      tajweedDisplayRef.current.playCurrentAudio();
    }
  }, [isGlobalAudioPlaying]);

  const handleAudioProgress = useCallback((progress: number) => {
    setAudioProgress(progress);
  }, []);

  const handleAudioEnded = useCallback(() => {
    setIsGlobalAudioPlaying(false);
    setAudioProgress(0);
  }, []);

  const startRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "inactive") {
      if (isGlobalAudioPlaying && tajweedDisplayRef.current) {
        tajweedDisplayRef.current.pauseAudio();
      }
      const currentText =
        latestVerseTextRef.current || getCurrentlyDisplayedVerseText();
      if (!currentText || currentText.trim().length === 0) {
        return;
      }
      audioChunks.current = [];
      mediaRecorder.start();
      setRecording(true);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "recording") {
      mediaRecorder.stop();
      setRecording(false);
    }
  };

  useEffect(() => {
    if (initialRuleIdProp) {
      if (!isValidRuleId(initialRuleIdProp)) {
        return;
      }
      const foundRule: TajweedRule | undefined = tajweedRulesData.find(
        (rule: TajweedRule) => rule.id === initialRuleIdProp
      );
      if (foundRule) {
        setActiveRule(foundRule);
        currentContextRuleIdRef.current = initialRuleIdProp;
      }
    } else if (!lessonMode) {
      setActiveRule(null);
      currentContextRuleIdRef.current = undefined;
    }
  }, [initialRuleIdProp, lessonMode]);

  useEffect(() => {
    if (surahData && typeof surahData.numberOfAyahs === "number") {
      setTotalAyahsInSurah(surahData.numberOfAyahs);
    } else {
      setTotalAyahsInSurah(verses?.length ?? 0);
    }
  }, [surahData, verses]);

  useEffect(() => {
    if (verseStartIndex && verseEndIndex) {
      setSelectedRange({ start: verseStartIndex, end: verseEndIndex });
    } else if (surahData && surahData.numberOfAyahs <= 50) {
      setSelectedRange({ start: 1, end: surahData.numberOfAyahs });
    } else {
      setSelectedRange(null);
    }
  }, [verseStartIndex, verseEndIndex, surahData]);

  const availableRanges: VerseRange[] = useMemo(() => {
    const DYNAMIC_FETCH_THRESHOLD = 50;
    const VERSES_PER_RANGE = 50;
    if (
      totalAyahsInSurah <= DYNAMIC_FETCH_THRESHOLD ||
      effectiveModeForDisplay !== "regular"
    )
      return [];
    const arr: VerseRange[] = [];
    let start = 1;
    while (start <= totalAyahsInSurah) {
      const end = Math.min(start + VERSES_PER_RANGE - 1, totalAyahsInSurah);
      arr.push({ start, end });
      start += VERSES_PER_RANGE;
    }
    return arr;
  }, [totalAyahsInSurah, effectiveModeForDisplay]);

  const handleRangeSelectionRequest = useCallback((range: VerseRange): void => {
    setSelectedRange(range);
  }, []);

  const handleClearRangeSelection = useCallback((): void => {
    setSelectedRange(null);
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  const handleRuleSelection = useCallback(
    (selectedRuleId: string): void => {
      if (!isValidRuleId(selectedRuleId)) {
        return;
      }
      const selectedRuleObj = tajweedRulesData.find(
        (r) => r.id === selectedRuleId
      );
      if (!selectedRuleObj) {
        return;
      }

      setActiveRule(selectedRuleObj);
      currentContextRuleIdRef.current = selectedRuleId;

      if (tajweedLessons[selectedRuleId]) {
        const lesson = tajweedLessons[selectedRuleId];
        setLessonMode(true);
        setEffectiveModeForDisplay("lesson");
        setCurrentLesson(lesson);
        setCurrentLessonSlideNumber(1);
        const slide = lesson.slides.find((s) => s.id === 1);
        if (slide) {
          setHighlightedPattern(slide.highlightPattern || null);
          setHighlightedLetterIndices(slide.highlightLetterIndices || []);
        }
        router.push(`/tajweed?ruleId=${selectedRuleId}&mode=lesson`);
      } else if (tajweedExamples[selectedRuleId]) {
        setLessonMode(false);
        setEffectiveModeForDisplay("example");
        const egRuleContext = tajweedExamples[
          selectedRuleId
        ] as unknown as RuleContext;
        setDisplayRuleContext({
          ruleId: selectedRuleId,
          ruleName: selectedRuleObj.name,
          ruleArabicName: selectedRuleObj.arabicName,
          keyWords: egRuleContext.keyWords || [],
          reason: egRuleContext.reason || "",
          difficultyLevel: egRuleContext.difficultyLevel || "beginner",
          practiceNotes: egRuleContext.practiceNotes || "",
          examples: egRuleContext.examples || [],
        });
        router.push(`/tajweed?ruleId=${selectedRuleId}&mode=example`);
      } else if (surahNumber) {
        setLessonMode(false);
        setEffectiveModeForDisplay("regular");
        router.push(
          `/tajweed?ruleId=${selectedRuleId}&surahNumber=${surahNumber}&mode=regular`
        );
      } else {
        setLessonMode(false);
        setEffectiveModeForDisplay("example");
        router.push(`/tajweed?ruleId=${selectedRuleId}&mode=example`);
      }
    },
    [router, surahNumber]
  );

  const handleWordClick = (word: TajweedWordObj): void => {
    if (word.tajweedRule) {
      const ruleData = tajweedRulesData.find((r) => r.id === word.tajweedRule);
      if (ruleData) {
        setSelectedTab("Rules");
        if (!lessonMode) {
          setActiveRule(ruleData);
        }
      }
    }
    if (word.isKeyWord && initialRuleContextProp) {
      setSelectedTab("Assistant");
    }
  };

  if (loading) {
    return <LoadingPage />;
  }

  const successStyles = `/* ... */`;
  const audioProgressStyles = `/* ... */`;

  return (
    <div
      className="flex flex-col h-screen overflow-hidden bg-gray-50 text-sm"
      style={{
        backgroundImage: 'url("data:image/svg+xml,%3Csvg ... %3C/svg%3E")',
        backgroundRepeat: "repeat",
      }}
    >
      <style>{successStyles}</style>
      <style>{audioProgressStyles}</style>

      <header className="flex-none sticky top-0 z-10 bg-white border-b border-gray-200 shadow-sm">
        <div className="px-6 py-4 flex items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push("/tajweedGrid")}
              className="p-1.5 md:p-2 rounded-full hover:bg-gray-100 transition-all duration-200 group"
            >
              <ArrowLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600 group-hover:text-gray-900 transition-colors duration-200" />
            </button>
            <div className="h-6 w-px bg-gray-300"></div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">Tajweed</h1>
              <p className="text-sm text-gray-500 mt-0.5">
                {initialRuleNameProp
                  ? `Focus: ${initialRuleNameProp}`
                  : lessonMode
                  ? "Lesson Overview"
                  : "Practice Exercises"}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3 md:gap-4 mr-12">
            {!lessonMode && initialRuleNameProp && (
              <div className="bg-gray-100 rounded-full p-0.5 flex"></div>
            )}
            <div className="text-rose-500 flex items-center font-bold">
              <svg
                viewBox="0 0 128 128"
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 md:h-7 md:w-7 mr-1 md:mr-2"
                aria-hidden="true"
              >
                <g>
                  <polygon
                    fill="#FF5050"
                    points="114,34 114,24 104,24 104,14 74,14 74,24 64,24 54,24 54,14 24,14 24,24 14,24 14,34 4,34 4,64 14,64 14,74 24,74 24,84 34,84 34,94 44,94 44,104 54,104 54,114 64,114 124,54 124,34"
                  />
                </g>
                <g>
                  <rect fill="#FFFFFF" height="10" width="10" x="24" y="34" />
                </g>
                <g>
                  <g>
                    <rect
                      fill="#9B0270"
                      height="10"
                      width="10"
                      x="64"
                      y="104"
                    />
                  </g>
                  <g>
                    <rect fill="#9B0270" height="10" width="10" x="74" y="94" />
                  </g>
                  <g>
                    <rect fill="#9B0270" height="10" width="10" x="84" y="84" />
                  </g>
                  <g>
                    <rect fill="#9B0270" height="10" width="10" x="94" y="74" />
                  </g>
                  <g>
                    <rect
                      fill="#9B0270"
                      height="10"
                      width="10"
                      x="104"
                      y="64"
                    />
                  </g>
                  <g>
                    <rect
                      fill="#9B0270"
                      height="10"
                      width="10"
                      x="114"
                      y="54"
                    />
                  </g>
                </g>
              </svg>
              <span
                className={`text-base md:text-base ${
                  isSuccessful ? "success-animation" : ""
                }`}
              >
                {hearts}
              </span>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1 flex flex-row gap-4 px-4 py-6 overflow-hidden">
        <div className="bg-white rounded-xl border border-gray-200 flex flex-col shadow-sm flex-1 min-h-0 transition-all duration-300 transform hover:shadow-md">
          <TajweedDisplay
            ref={tajweedDisplayRef}
            surah={surahData}
            verses={verses}
            activeRule={activeRule}
            lessonTitle={currentLesson?.title}
            tajweedRules={tajweedRulesData}
            onRuleSelection={handleRuleSelection}
            onWordClick={handleWordClick}
            totalAyahs={totalAyahsInSurah}
            selectedRange={selectedRange}
            availableRanges={availableRanges}
            onRangeSelectionRequest={handleRangeSelectionRequest}
            onClearRangeSelection={handleClearRangeSelection}
            previousSurahNumber={previousSurahNumber}
            nextSurahNumber={nextSurahNumber}
            onNavigateSurah={handleNavigateSurah}
            ruleContext={displayRuleContext}
            mode={effectiveModeForDisplay}
            lessonMode={lessonMode}
            currentLessonSlide={currentSlideData}
            highlightedPattern={highlightedPattern}
            highlightedLetterIndices={highlightedLetterIndices}
            onVerseTextChange={handleVerseTextChange}
            onAudioProgressChange={handleAudioProgress}
            onAudioEnded={handleAudioEnded}
            onAudioPlaybackStateChange={handleAudioPlaybackStateUpdate}
          />
        </div>

        <div className="bg-white rounded-xl border border-gray-200 flex flex-col shadow-sm flex-1 min-h-0 transition-all duration-300 transform hover:shadow-md">
          <div className="p-4 border-b border-gray-100 bg-white rounded-t-xl">
            <h3 className="font-semibold text-lg text-gray-900 tracking-tight">
              {lessonMode
                ? currentSlideData?.title ||
                  currentLesson?.title ||
                  "Tajweed Lesson"
                : "Tajweed Assistant"}
            </h3>
          </div>

          <TajweedFeedbackSection
            // Pass lesson props only when in lesson mode
            lessonTitle={lessonMode ? currentSlideData?.title : undefined}
            content={lessonMode ? currentSlideData?.content : undefined}
            practiceNotes={
              lessonMode ? displayRuleContext?.practiceNotes : undefined
            }
            audioUrl={lessonMode ? currentSlideData?.audioUrl : undefined}
            imageUrl={lessonMode ? currentSlideData?.imageUrl : undefined}
            currentSlide={lessonMode ? currentLessonSlideNumber : undefined}
            totalSlides={lessonMode ? currentLesson?.slides.length : undefined}
            onNextSlide={lessonMode ? handleNextSlide : undefined}
            onPrevSlide={lessonMode ? handlePreviousSlide : undefined}
            // Pass assessment props universally
            isLoading={feedbackLoading}
            hasBeenAssessed={hasBeenAssessed}
            isSuccessful={isSuccessful}
            score={score}
            violations={violations}
            feedbackHTML={feedbackHTML}
          />

          <div className="border-t border-gray-100 bg-white h-16 p-1 rounded-b-xl mt-auto">
            {lessonMode ? (
              <div className="h-full w-full flex items-center justify-between px-6">
                <button
                  onClick={handlePreviousSlide}
                  disabled={currentLessonSlideNumber === 1}
                  className={`px-4 py-2 rounded-lg flex items-center transition-all duration-200 ${
                    currentLessonSlideNumber === 1
                      ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                      : "bg-gray-200 text-gray-800 hover:bg-gray-300"
                  }`}
                >
                  <ChevronLeft className="w-4 h-4 mr-2" /> Previous
                </button>
                {isLastSlide ? (
                  <button
                    onClick={handleFinishLesson}
                    className="px-4 py-2 rounded-lg bg-green-600 text-white hover:bg-green-700 flex items-center transition-all duration-200 transform hover:translate-y-[-1px] shadow-sm hover:shadow"
                  >
                    Finish Lesson <Check className="w-4 h-4 ml-2" />
                  </button>
                ) : (
                  <button
                    onClick={handleNextSlide}
                    disabled={
                      !currentLesson ||
                      currentLessonSlideNumber >= currentLesson.slides.length
                    }
                    className={`px-4 py-2 rounded-lg flex items-center transition-all duration-200 ${
                      !currentLesson ||
                      currentLessonSlideNumber >= currentLesson.slides.length
                        ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                        : "bg-[#1a1a18] text-white hover:bg-gray-800 transform hover:translate-y-[-1px] shadow-sm hover:shadow"
                    }`}
                  >
                    Next <ChevronRight className="w-4 h-4 ml-2" />
                  </button>
                )}
              </div>
            ) : (
              <div className="h-full w-full flex items-center justify-center">
                <div className="flex items-center space-x-4">
                  <button
                    className={`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 shadow-sm transform hover:translate-y-[-2px] hover:shadow ${
                      recording
                        ? "bg-red-500 text-white hover:bg-red-600"
                        : isSuccessful
                        ? "bg-green-500 text-white hover:bg-green-600 success-animation"
                        : "bg-[#1a1a18] text-white hover:bg-gray-800"
                    }`}
                    onClick={() => {
                      if (recording) stopRecording();
                      else startRecording();
                    }}
                    aria-label={
                      recording ? "Stop recording" : "Start recording"
                    }
                    disabled={isSuccessful || isGlobalAudioPlaying}
                  >
                    {recording ? (
                      <Square className="h-4 w-4" />
                    ) : isSuccessful ? (
                      <Check className="h-5 w-5" />
                    ) : (
                      <Mic className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
            )}
            {isGlobalAudioPlaying && !lessonMode && (
              <div className="audio-progress-bar mx-4 mt-1">
                <div
                  className="audio-progress-fill"
                  style={{ width: `${audioProgress}%` }}
                />
              </div>
            )}
          </div>
        </div>
      </main>

      <footer className="flex-none h-4 bg-white text-xs"></footer>
    </div>
  );
};

export default Tajweed;

// app/deepDive/deepDive.tsx

"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { deepDiveContent, DeepDiveLesson } from "./content";

const ORANGE = "#ff914d";
const INK = "#1a1a18";

/** Filled right-pointing triangle (animated when selected/hovered) */
function FilledTriangle({
  size = 12,
  color = INK,
  className,
  selected = false,
}: {
  size?: number;
  color?: string;
  className?: string;
  selected?: boolean;
}) {
  return (
    <motion.svg
      viewBox="0 0 24 24"
      width={size}
      height={size}
      className={className}
      aria-hidden
      focusable="false"
      initial={false}
      animate={{
        x: selected ? 2 : 0,
        rotate: selected ? 8 : 0,
      }}
      transition={{ duration: 0.16, ease: "easeOut" }}
    >
      <polygon points="6,4 20,12 6,20" style={{ fill: color }} />
    </motion.svg>
  );
}

type DeepDiveProps = {
  lessonNumber?: number;
  className?: string;
  /** Optional: preselect a section by key; if omitted we start with list-only state until user clicks */
  initialSectionKey?: string;
};

export default function DeepDive({
  lessonNumber = 1,
  className,
  initialSectionKey,
}: DeepDiveProps) {
  const lesson: DeepDiveLesson | undefined = deepDiveContent[lessonNumber];

  // ---- State ----
  const [activeKey, setActiveKey] = React.useState<string | null>(
    initialSectionKey ?? null
  );

  // Reset selection when lesson changes (preserve explicit initialSectionKey if provided)
  React.useEffect(() => {
    setActiveKey(initialSectionKey ?? null);
  }, [lessonNumber, initialSectionKey]);

  // Keep refs to buttons for keyboard navigation
  const buttonRefs = React.useRef<Array<HTMLButtonElement | null>>([]);

  if (!lesson) {
    return (
      <section className={className}>
        <div className="flex items-start gap-6">
          <HeaderBadge number={lessonNumber} />
          <p className="text-muted-foreground">No deep dive content found.</p>
        </div>
      </section>
    );
  }

  const isFocused = Boolean(activeKey);
  const activeSection =
    lesson.sections.find((s) => s.key === activeKey) ?? null;

  // Motion configuration - Apple-grade cubic-bezier
  const dockTransition = {
    duration: 0.26,
    ease: [0.34, 0.08, 0.22, 1],
  };

  const contentTransition = {
    duration: 0.2,
    delay: 0.06,
    ease: "easeOut",
  };

  // ---- Keyboard navigation for the left rail ----
  const onKeyDownRail = (e: React.KeyboardEvent<HTMLDivElement>) => {
    const count = lesson.sections.length;
    if (count === 0) return;

    const indexFromKey = lesson.sections.findIndex((s) => s.key === activeKey);
    const currentIndex =
      indexFromKey === -1 ? Math.max(0, Math.min(count - 1, 0)) : indexFromKey;

    const focusAt = (i: number) => {
      const node = buttonRefs.current[i];
      if (node) node.focus();
    };

    switch (e.key) {
      case "ArrowDown":
      case "j": {
        e.preventDefault();
        const next = (currentIndex + 1) % count;
        focusAt(next);
        break;
      }
      case "ArrowUp":
      case "k": {
        e.preventDefault();
        const prev = (currentIndex - 1 + count) % count;
        focusAt(prev);
        break;
      }
      case "Home": {
        e.preventDefault();
        focusAt(0);
        break;
      }
      case "End": {
        e.preventDefault();
        focusAt(count - 1);
        break;
      }
      case "Enter":
      case " ": {
        // Toggle/open selected button
        const i = buttonRefs.current.findIndex(
          (n) => n === document.activeElement
        );
        if (i >= 0) {
          const key = lesson.sections[i].key;
          setActiveKey((p) => (p === key ? null : key));
        }
        break;
      }
      default:
        break;
    }
  };

  return (
    <section
      className={className}
      aria-labelledby={`deep-dive-lesson-${lesson.lessonNumber}`}
    >
      {/* Layout container */}
      <div
        className={`grid gap-10 items-start transition-all duration-300`}
        style={{
          gridTemplateColumns: isFocused ? "280px 1fr" : "1fr",
        }}
      >
        {/* LEFT RAIL: starts large; shrinks + docks top-left when a section is selected */}
        <motion.aside
          animate={{
            scale: isFocused ? 0.86 : 1,
            x: isFocused ? -32 : 0,
            y: isFocused ? -16 : 0,
          }}
          transition={dockTransition}
          style={{
            transformOrigin: "top left",
            width: isFocused ? 280 : "100%",
            maxWidth: isFocused ? 280 : "64rem",
          }}
          className="mx-auto md:mx-0"
          role="navigation"
          aria-label="Dive Deeper topics"
          onKeyDown={onKeyDownRail}
        >
          <div className="flex items-start gap-4">
            <HeaderBadge number={lesson.lessonNumber} compact={isFocused} />
          </div>

          {/* Indented list with a subtle left guide; no boxes */}
          <nav
            aria-label="Deep Dive Sections"
            className={`${isFocused ? "mt-8" : "mt-12"} pl-8 relative`}
          >
            {/* left guide line - subtle hairline */}
            <span
              aria-hidden
              className="absolute left-0 top-1 bottom-1 w-[2px]"
              style={{ backgroundColor: "rgba(26, 26, 24, 0.08)" }}
            />
            <ul
              className={isFocused ? "space-y-4" : "space-y-6"}
              role="listbox"
              aria-orientation="vertical"
            >
              {lesson.sections.map((section, i) => {
                const selected = activeKey === section.key;
                const controlId = `deep-dive-panel-${section.key}`;
                return (
                  <li key={section.key} className="list-none">
                    <button
                      type="button"
                      ref={(el) => (buttonRefs.current[i] = el)}
                      onClick={() =>
                        setActiveKey((p) =>
                          p === section.key ? null : section.key
                        )
                      }
                      aria-current={selected ? "true" : undefined}
                      aria-selected={selected}
                      aria-controls={controlId}
                      className={`group inline-flex items-center gap-3 font-semibold leading-tight
                                 hover:opacity-70 focus:outline-none transition-all
                                 focus-visible:ring-[3px] focus-visible:ring-offset-2 rounded-md px-1 py-0.5`}
                      style={{
                        fontSize: isFocused ? "15px" : "22px",
                        letterSpacing: isFocused ? "-0.005em" : "-0.01em",
                        color: selected ? ORANGE : INK,
                        // Orange focus ring for active title
                        ["--tw-ring-color" as any]: selected
                          ? "rgba(255, 145, 77, 0.25)"
                          : "rgba(26, 26, 24, 0.2)",
                      }}
                    >
                      <FilledTriangle
                        size={isFocused ? 12 : 16}
                        color={INK}
                        selected={selected}
                        className="flex-shrink-0 translate-y-[1px] group-hover:translate-x-[2px]"
                      />
                      <span>{section.title}</span>
                    </button>
                  </li>
                );
              })}
            </ul>
          </nav>
        </motion.aside>

        {/* MAIN CONTENT: takes center stage; aligned with the rail */}
        <main
          className={`min-h-[45vh] ${isFocused ? "block" : "hidden"}`}
          aria-live="polite"
        >
          <AnimatePresence mode="wait">
            {!activeSection ? (
              <motion.div
                key="hint"
                id="deep-dive-hint"
                initial={{ opacity: 0, y: 6 }}
                animate={{ opacity: 0.7, y: 0 }}
                exit={{ opacity: 0, y: -6 }}
                transition={{ duration: 0.2 }}
                className="text-sm"
                style={{ color: "rgba(26, 26, 24, 0.70)" }}
              >
                Select a topic on the left to dive in.
              </motion.div>
            ) : (
              <motion.article
                key={activeSection.key}
                id={`deep-dive-panel-${activeSection.key}`}
                initial={{ opacity: 0, y: 12 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -8 }}
                transition={contentTransition}
                className="max-w-[1100px] mx-auto px-20 py-16"
                aria-labelledby={`deep-dive-title-${activeSection.key}`}
              >
                <h2
                  id={`deep-dive-title-${activeSection.key}`}
                  className="text-[32px] font-semibold leading-tight"
                  style={{
                    letterSpacing: "-0.02em",
                    color: INK,
                  }}
                >
                  {activeSection.title}
                </h2>

                {activeSection.summary && (
                  <p
                    className="mt-6 text-[17px] leading-relaxed"
                    style={{
                      color: "rgba(26, 26, 24, 0.70)",
                      lineHeight: "1.55",
                    }}
                  >
                    {activeSection.summary}
                  </p>
                )}

                {activeSection.bullets?.length ? (
                  <ul
                    className="mt-6 list-disc pl-6 space-y-2 text-[17px]"
                    style={{
                      color: INK,
                      lineHeight: "1.55",
                    }}
                  >
                    {activeSection.bullets.map((b, i) => (
                      <li key={i}>{b}</li>
                    ))}
                  </ul>
                ) : null}

                {activeSection.examples?.length ? (
                  <div className="mt-10 overflow-x-auto">
                    <table className="w-full text-center border-collapse">
                      <thead>
                        <tr
                          className="text-sm font-medium"
                          style={{ color: "rgba(26, 26, 24, 0.70)" }}
                        >
                          <th className="py-3">Letter</th>
                          <th className="py-3">Isolated</th>
                          <th className="py-3">Initial</th>
                          <th className="py-3">Medial</th>
                          <th className="py-3">Final</th>
                        </tr>
                      </thead>
                      <tbody>
                        {activeSection.examples.map((ex, i) => (
                          <tr
                            key={i}
                            className="border-t"
                            style={{
                              borderColor: "rgba(26, 26, 24, 0.08)",
                            }}
                          >
                            <td
                              className="py-4 font-semibold"
                              style={{ color: INK }}
                            >
                              {ex.label}
                            </td>
                            <HoverLetterCell text={ex.isolated} />
                            <HoverLetterCell text={ex.initial} />
                            <HoverLetterCell text={ex.medial} />
                            <HoverLetterCell text={ex.final} />
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : null}
              </motion.article>
            )}
          </AnimatePresence>
        </main>
      </div>
    </section>
  );
}

/** Big number + stacked "Dive / Deeper".
 *  Shrinks nicely when the rail docks (compact=true).
 */
function HeaderBadge({
  number,
  compact = false,
}: {
  number: number;
  compact?: boolean;
}) {
  return (
    <div className="flex items-start gap-4 select-none">
      <div
        className={`${
          compact ? "text-[72px]" : "text-[96px]"
        } font-bold leading-[0.95] tabular-nums`}
        style={{
          letterSpacing: "-0.03em",
          color: INK,
        }}
      >
        {String(number).padStart(2, "0")}
      </div>
      <div
        className="text-[18px] leading-tight font-medium tracking-wide"
        style={{
          color: "rgba(26, 26, 24, 0.70)",
          letterSpacing: "0.01em",
        }}
      >
        <div>Dive</div>
        <div>Deeper</div>
      </div>
    </div>
  );
}

/** Arabic glyph cell with hover animation */
function HoverLetterCell({ text }: { text: string }) {
  const [isHovered, setIsHovered] = React.useState(false);

  return (
    <td className="py-4">
      <motion.span
        className="inline-block font-arabic text-[28px] cursor-pointer select-none rounded-lg"
        style={{
          color: INK,
          transformOrigin: "center bottom",
        }}
        animate={{
          scale: isHovered ? 1.1 : 1,
          rotate: isHovered ? 0.8 : 0,
        }}
        transition={{
          duration: 0.16,
          ease: "easeOut",
        }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        {text}
      </motion.span>
    </td>
  );
}

// app/deepDive/page.tsx
import React, { Suspense } from "react";
import Loading from "./loading";
import DeepDive from "./deepDive";

type PageProps = {
  searchParams?: { lesson?: string };
};

export default function Page({ searchParams }: PageProps) {
  const lessonParam = searchParams?.lesson?.trim();
  const lessonNumber =
    Number.isFinite(Number(lessonParam)) && Number(lessonParam) > 0
      ? parseInt(lessonParam as string, 10)
      : 1;

  return (
    <main className="container mx-auto max-w-5xl px-4 py-8">
      <Suspense fallback={<Loading />}>
        <DeepDive lessonNumber={lessonNumber} />
      </Suspense>
    </main>
  );
}

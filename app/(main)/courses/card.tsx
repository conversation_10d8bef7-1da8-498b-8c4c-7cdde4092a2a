"use client";

import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

type Props = {
  englishTitle: string;
  arabicTitle: string;
  id: number;
  index: number;
  onClick: (id: number) => void;
  disabled?: boolean;
  active?: boolean;
};

export const Card = ({
  englishTitle,
  arabicTitle,
  id,
  index,
  disabled,
  onClick,
  active,
}: Props) => {
  // Similar status-based styling as SurahGrid
  const getStatusBorderColor = () => {
    if (active) return "border-amber-500";
    return "border-gray-200";
  };

  return (
    <div
      onClick={() => onClick(id)}
      className={cn(
        "group relative bg-white border rounded-xl",
        "transition-all duration-300 ease-out cursor-pointer",
        // **** INCREASED HEIGHT HERE ****
        "p-4 flex flex-col min-h-[180px]", // Changed from min-h-[140px] to min-h-[180px]
        // ******************************
        "hover:shadow-md hover:scale-[1.02]",
        getStatusBorderColor(),
        disabled && "opacity-50 pointer-events-none"
      )}
    >
      {/* Header with number and status */}
      <div className="flex items-center justify-between mb-3">
        {/* Number Badge */}
        <div className="relative h-8 w-8">
          <div
            className={`
              absolute inset-0 rounded-lg
              ${active ? "bg-amber-50" : "bg-gray-50"}
              ${
                active ? "group-hover:bg-amber-500" : "group-hover:bg-[#1a1a18]"
              }
              transition-transform duration-300 delay-200
              group-hover:rotate-45
            `}
          />
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <span
              className={`
                font-bold text-sm transition-colors duration-200
                group-hover:text-white
                ${active ? "text-amber-700" : "text-gray-900"}
              `}
            >
              {index}
            </span>
          </div>
        </div>

        {/* Status indicator */}
        {active && (
          <div className="bg-amber-500 text-white rounded-full p-1.5">
            <Check className="h-3 w-3 stroke-[3]" />
          </div>
        )}
      </div>

      {/* Content section */}
      <div className="flex-1 flex flex-col justify-center text-center space-y-2">
        {/* English Title */}
        <h3 className="text-lg font-semibold text-gray-900 leading-tight">
          {englishTitle}
        </h3>

        {/* Arabic Title */}
        <p
          className="text-lg font-arabic text-gray-700 leading-relaxed"
          dir="rtl"
        >
          {arabicTitle}
        </p>
      </div>
    </div>
  );
};

// app/(main)/surahGrid/SurahGrid.tsx

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import type { Surah } from "./page";

type SurahWithJuz = Surah & {
  juzNumber?: number[];
};

type JuzData = {
  number: number;
  surahs: SurahWithJuz[];
};

type SurahGridProps = {
  surahs: Surah[];
  // MODIFIED: The onSurahSelect prop is now OPTIONAL.
  onSurahSelect?: (surah: Surah) => void;
};

const SurahGrid: React.FC<SurahGridProps> = ({ surahs, onSurahSelect }) => {
  // RESTORED: useRouter is back for the standalone page functionality.
  const router = useRouter();

  const [activeTab, setActiveTab] = useState("juz");
  // RESTORED: Modal state is back for the standalone page.
  const [selectedSurah, setSelectedSurah] = useState<Surah | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [hoveredSurah, setHoveredSurah] = useState<{
    surahNumber: number;
    juzNumber?: number;
  } | null>(null);
  const [juzData, setJuzData] = useState<JuzData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchJuzData = useCallback(async () => {
    setIsLoading(true);
    try {
      const juzArray: JuzData[] = Array.from({ length: 30 }, (_, i) => ({
        number: i + 1,
        surahs: [],
      }));

      const juzSurahMap = [
        [1, 2],
        [2],
        [2, 3],
        [3, 4],
        [4],
        [4, 5],
        [5, 6],
        [6, 7],
        [7, 8],
        [8, 9],
        [9, 10, 11],
        [11, 12],
        [12, 13, 14],
        [15, 16],
        [17, 18],
        [18, 19, 20],
        [21, 22],
        [23, 24, 25],
        [25, 26, 27],
        [27, 28, 29],
        [29, 30, 31, 32, 33],
        [33, 34, 35, 36],
        [36, 37, 38, 39],
        [39, 40, 41],
        [41, 42, 43, 44, 45],
        [46, 47, 48, 49, 50, 51],
        [51, 52, 53, 54, 55, 56, 57],
        [58, 59, 60, 61, 62, 63, 64, 65, 66],
        [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77],
        [
          78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94,
          95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109,
          110, 111, 112, 113, 114,
        ],
      ];

      juzSurahMap.forEach((surahNumbers, juzIndex) => {
        const juzNumber = juzIndex + 1;
        const surahsInThisJuz = surahs.filter((s) =>
          surahNumbers.includes(s.number)
        );
        if (juzArray[juzIndex]) {
          juzArray[juzIndex].surahs = surahsInThisJuz.map((surah) => ({
            ...surah,
            juzNumber: [juzNumber],
          }));
        } else {
          console.warn(`Juz index ${juzIndex} out of bounds for juzArray`);
        }
      });
      setJuzData(juzArray);
    } catch (error) {
      console.error("Error organizing Juz data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [surahs]);

  useEffect(() => {
    if (activeTab === "juz" && juzData.length === 0) {
      fetchJuzData();
    }
  }, [activeTab, juzData.length, fetchJuzData]);

  // MODIFIED: This function now checks if onSurahSelect is provided.
  // If it is, it calls it. If not, it falls back to the original navigation logic.
  const handleSurahClick = (surah: Surah) => {
    if (onSurahSelect) {
      onSurahSelect(surah);
    } else {
      if (surah.status === "inProgress") {
        setSelectedSurah(surah);
        setShowModal(true);
      } else {
        startMemorization(
          surah.number,
          "startFresh",
          surah.numberOfAyahs,
          surah.englishName
        );
      }
    }
  };

  const startMemorization = (
    surahNumber: number,
    mode: "continue" | "startFresh",
    numberOfAyahs: number,
    englishName: string
  ) => {
    const versesRange = mode === "continue" ? "1-50" : `1-${numberOfAyahs}`;
    const url = `/memorization?surahNumber=${encodeURIComponent(
      surahNumber
    )}&versesRange=${encodeURIComponent(
      versesRange
    )}&mode=${mode}&surahName=${encodeURIComponent(englishName)}`;
    router.push(url);
    setShowModal(false);
  };

  const handleUserChoice = (choice: "continue" | "startFresh") => {
    if (selectedSurah) {
      startMemorization(
        selectedSurah.number,
        choice,
        selectedSurah.numberOfAyahs,
        selectedSurah.englishName
      );
    }
  };

  const handleReadJuz = (juzNumber: number) => {
    router.push(`/read-juz/${juzNumber}`);
  };

  const getStatusBgColor = (status?: string, isHovered?: boolean) => {
    if (isHovered) {
      return status === "completed"
        ? "bg-emerald-500"
        : status === "inProgress"
        ? "bg-amber-500"
        : "bg-[#1a1a18]";
    }
    return status === "completed"
      ? "bg-emerald-50"
      : status === "inProgress"
      ? "bg-amber-50"
      : "bg-gray-50";
  };

  const getStatusBorderColor = (status?: string) => {
    return status === "completed"
      ? "border-emerald-500"
      : status === "inProgress"
      ? "border-amber-500"
      : "border-gray-200";
  };

  const renderSurahCard = (surah: SurahWithJuz, juzNumber?: number) => {
    const isHovered =
      hoveredSurah?.surahNumber === surah.number &&
      (hoveredSurah.juzNumber === juzNumber ||
        (hoveredSurah.juzNumber === undefined && juzNumber === undefined));
    const statusBorderColor = getStatusBorderColor(surah.status);
    const isJuzView = activeTab === "juz";

    return (
      <div
        key={`${surah.number}-${juzNumber || "surahView"}`}
        onClick={() => handleSurahClick(surah)}
        onMouseEnter={() =>
          setHoveredSurah({ surahNumber: surah.number, juzNumber })
        }
        onMouseLeave={() => setHoveredSurah(null)}
        className={`
          group relative bg-white border rounded-xl ${isJuzView ? "p-3" : "p-4"}
          transition-all duration-300 ease-out cursor-pointer
          hover:shadow-md hover:scale-[1.02]
          ${statusBorderColor}
        `}
      >
        <div className="flex items-center">
          <div
            className={`relative flex-shrink-0 mr-3 ${
              isJuzView ? "w-8 h-8" : "w-10 h-10"
            }`}
          >
            <div
              className={`
                absolute inset-0 rounded-lg
                ${getStatusBgColor(surah.status, isHovered)}
                transition-transform duration-300 delay-200
                ${isHovered ? "rotate-45" : ""}
                group-hover:rotate-45
              `}
            />
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <span
                className={`
                  font-bold ${isJuzView ? "text-sm" : "text-base"}
                  transition-colors duration-200
                  ${isHovered ? "text-white" : "text-gray-900"}
                  group-hover:text-white
                `}
              >
                {surah.number}
              </span>
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start">
              <div>
                <h3
                  className={`${isJuzView ? "text-xs" : "text-sm"}
                    font-semibold text-gray-900 mb-1 group-hover:text-gray-900`}
                >
                  {surah.englishName}
                </h3>
                <p
                  className={`${isJuzView ? "text-xs" : "text-xs"}
                    text-gray-500 group-hover:text-gray-500`}
                >
                  {surah.englishNameTranslation}
                </p>
              </div>
              <div className="text-right flex-shrink-0 pl-2">
                <span
                  className={`${isJuzView ? "text-sm" : "text-base"}
                    font-semibold text-gray-800 font-arabic block group-hover:text-gray-800`}
                >
                  {surah.name}
                </span>
                <p className="text-xs text-gray-500 group-hover:text-gray-500">
                  {surah.numberOfAyahs} Ayahs
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderSurahView = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {surahs.map((surah) => renderSurahCard(surah, undefined))}
    </div>
  );

  const renderJuzView = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900"></div>
          <span className="ml-2 text-gray-700">Loading Juz data...</span>
        </div>
      );
    }
    if (!juzData || juzData.length === 0) {
      return (
        <div className="text-center py-10 text-gray-500">
          No Juz data available. Try switching tabs or reloading.
        </div>
      );
    }
    const leftColumnJuz = juzData.slice(0, 19);
    const middleColumnJuz = juzData.slice(19, 28);
    const rightColumnJuz = juzData.slice(28, 30);

    const renderJuzSection = (juz: JuzData) => {
      if (!juz || !juz.surahs) {
        return null;
      }
      return (
        <div key={juz.number} className="bg-gray-50 rounded-xl p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-base font-semibold text-gray-900">
              Juz {juz.number}
            </h2>
            <span className="text-xs text-gray-600">Read Juz</span>
          </div>
          <div className="space-y-3">
            {juz.surahs.length > 0 ? (
              juz.surahs.map((surah) => renderSurahCard(surah, juz.number))
            ) : (
              <p className="text-xs text-gray-400 text-center py-2">
                No Surahs listed for this Juz.
              </p>
            )}
          </div>
        </div>
      );
    };

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-4">{leftColumnJuz.map(renderJuzSection)}</div>
        <div className="space-y-4">{middleColumnJuz.map(renderJuzSection)}</div>
        <div className="space-y-4">{rightColumnJuz.map(renderJuzSection)}</div>
      </div>
    );
  };

  return (
    <div
      className="flex flex-col h-screen text-sm"
      style={{
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
    >
      <div className="sticky top-0 z-40 bg-white px-3 py-2 md:px-4 md:py-3 border-b border-gray-200">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <button
            onClick={() => router.push("/challenges")}
            className="p-1.5 rounded-full hover:bg-gray-100 transition-all duration-200"
          >
            <ArrowLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
          </button>
          <h1 className="text-lg font-bold text-neutral-700 whitespace-nowrap">
            Hifz Exercises
          </h1>
          <div className="flex items-center space-x-3">
            <nav className="flex space-x-1 border rounded-lg bg-white p-0.5">
              <button
                onClick={() => setActiveTab("juz")}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === "juz"
                    ? "bg-[#1a1a18] text-gray-100"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Juz
              </button>
              <button
                onClick={() => setActiveTab("surah")}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === "surah"
                    ? "bg-[#1a1a18] text-gray-100"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Surah
              </button>
            </nav>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-auto px-3 py-6 md:px-6">
        <div className="max-w-7xl mx-auto">
          {activeTab === "surah" ? renderSurahView() : renderJuzView()}
        </div>
      </div>

      {showModal && selectedSurah && (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-900/40 backdrop-blur-sm z-50">
          <div className="bg-white rounded-xl shadow-lg p-5 w-full max-w-sm mx-3 transform transition-all duration-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              Continue Your Progress
            </h2>
            <div className="w-12 h-px bg-gray-200 mb-3"></div>
            <p className="text-sm text-gray-600 mb-5">
              You have existing progress in Surah {selectedSurah.englishName}.
              Would you like to continue where you left off or start fresh?
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-white border border-gray-200 hover:bg-gray-50 text-gray-800 font-medium rounded-lg transition-colors duration-200"
                onClick={() => handleUserChoice("startFresh")}
              >
                Start Fresh
              </button>
              <button
                className="px-4 py-2 bg-[#1a1a18] hover:bg-gray-800 text-white font-medium rounded-lg transition-colors duration-200"
                onClick={() => handleUserChoice("continue")}
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SurahGrid;

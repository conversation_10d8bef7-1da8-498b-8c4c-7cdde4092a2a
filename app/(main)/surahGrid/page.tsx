// app/(main)/surahGrid/page.tsx

import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { memorizationSessions, memorizedSurahs } from "@/db/schema";
import { eq } from "drizzle-orm";
import SurahGrid from "./SurahGrid";

// Enhanced type to include englishNameTranslation
export type Surah = {
  number: number;
  name: string;
  englishName: string;
  englishNameTranslation: string;
  revelationPlace: string;
  numberOfAyahs: number;
  status?: "completed" | "inProgress" | "notStarted";
};

// This is an async Server Component again.
const SurahGridPage = async () => {
  // 1) Check authentication
  const { userId } = await auth();
  if (!userId) {
    redirect("/");
  }

  // 2) Fetch Surah metadata from AlQuranCloud
  const response = await fetch("https://api.alquran.cloud/v1/surah", {
    cache: "no-store",
  });
  if (!response.ok) {
    throw new Error("Failed to fetch Surah metadata from AlQuranCloud");
  }
  const apiData = await response.json();
  const apiSurahs = apiData?.data || [];

  // 3) Fetch user's memorization data from your local DB (RESTORED)
  const inProgressSessions = await db.query.memorizationSessions.findMany({
    where: eq(memorizationSessions.userId, userId),
    columns: {
      surahNumber: true,
    },
  });

  const completedSurahs = await db.query.memorizedSurahs.findMany({
    where: eq(memorizedSurahs.userId, userId),
    columns: {
      surahNumber: true,
    },
  });

  // Create sets for quick lookup
  const inProgressSet = new Set<number>(
    inProgressSessions.map((item) => item.surahNumber)
  );
  const completedSet = new Set<number>(
    completedSurahs.map((item) => item.surahNumber)
  );

  // 4) Map AlQuranCloud data + memorization status
  const surahsWithStatus: Surah[] = apiSurahs.map((s: any) => {
    const num = s.number;
    let status: Surah["status"] = "notStarted";
    if (completedSet.has(num)) {
      status = "completed";
    } else if (inProgressSet.has(num)) {
      status = "inProgress";
    }

    return {
      number: s.number,
      name: s.name,
      englishName: s.englishName,
      englishNameTranslation: s.englishNameTranslation,
      revelationPlace: s.revelationType,
      numberOfAyahs: s.numberOfAyahs,
      status,
    };
  });

  // 5) Render the grid with the new Surah array. The prop issue will be fixed in SurahGrid.tsx
  return <SurahGrid surahs={surahsWithStatus} />;
};

export default SurahGridPage;

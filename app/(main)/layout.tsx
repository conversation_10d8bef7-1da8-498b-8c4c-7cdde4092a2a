// app/(main)/layout.tsx

import { ReactNode } from "react";
import { headers } from "next/headers";
import { SignedIn } from "@clerk/nextjs"; // <-- IMPORT Clerk's <SignedIn> component
import { Sidebar } from "@/components/sidebar";
import { MobileHeader } from "@/components/mobile-header";
import Onboarding<PERSON>hecker from "@/components/OnboardingChecker";

type Props = {
  children: ReactNode;
  params: {}; // Layouts receive a params object
};

/**
 * This is the final, optimized main layout.
 * It uses Clerk's <SignedIn> component to ensure that the main application UI
 * only renders AFTER the user's session is fully loaded and verified on the client,
 * preventing any authentication race conditions with API calls.
 */
export default async function MainLayout({ children }: Props) {
  const headersList = headers();
  const isEmbedded = headersList.get("sec-fetch-dest") === "iframe";

  if (isEmbedded) {
    return <div className="h-full w-full">{children}</div>;
  }

  console.log(
    "[MainLayout] Rendering layout shell immediately (non-blocking)."
  );

  return (
    <>
      {/* The <SignedIn> component ensures its children only render when a user is logged in. */}
      {/* This prevents API calls from firing before the auth state is ready. */}
      {/* It will show nothing until the user is confirmed, then show everything inside. */}
      <SignedIn>
        <MobileHeader />
        <OnboardingChecker />
        <div className="flex h-screen">
          <Sidebar />
          <main className="flex-1 h-full pt-[50px] lg:pt-0 overflow-auto">
            <div className="w-full h-full">{children}</div>
          </main>
        </div>
      </SignedIn>
    </>
  );
}

"use client";

import React, { useContext, useEffect, useState } from "react";
import { EventContext } from "@/app/(main)/schedule/EventContext";
import { MapPin, ChevronRight, PlusCircle } from "lucide-react";
import { Button } from "@/components/shadcn-ui/button";
import { Card, CardContent } from "@/components/shadcn-ui/card";
import { motion } from "framer-motion";
import { Manrope } from "next/font/google";

// Font configuration
const manrope = Manrope({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-manrope",
});

interface CalendarEvent {
  id?: number;
  title: string;
  description?: string;
  startTime?: string;
  endTime?: string;
  googleEventId?: string;
  calendarLink?: string;
  recurrenceRule?: string;
  location?: string;
  attendees?: number;
  status?: "upcoming" | "in-progress" | "completed";
  progress?: number;
}

interface EventWithProgress extends CalendarEvent {
  progress: number;
}

interface TodaysTasksProps {
  events?: CalendarEvent[];
  loading?: boolean;
}

const StatusColumn = ({
  title,
  events,
  children,
}: {
  title: string;
  events: EventWithProgress[];
  children: React.ReactNode;
}) => {
  const numChildren = React.Children.count(children);

  return (
    <div className="bg-gray-50 rounded-2xl p-6 flex flex-col min-h-0">
      <div className="flex justify-between items-center mb-6 flex-shrink-0">
        <h3
          className={`font-semibold text-lg text-[#1a1a18] ${manrope.variable} font-sans`}
        >
          {title}
        </h3>
        <span className="bg-[#1a1a18] text-white text-sm font-bold w-7 h-7 rounded-full flex items-center justify-center shadow-sm">
          {events.length}
        </span>
      </div>
      <div className="flex-1 flex flex-col gap-4 overflow-hidden">
        {Array.from({ length: 2 }).map((_, index) => {
          const card = React.Children.toArray(children)[index];
          const isFirstEmptyToDoSlot =
            title === "To Do" && index === numChildren;

          return (
            <div
              key={index}
              className={`group border-2 border-dashed rounded-xl flex-1 min-h-0 transition-all duration-200 ${
                isFirstEmptyToDoSlot
                  ? "border-[#1a1a18] cursor-pointer hover:bg-gray-100/50"
                  : "border-gray-200"
              }`}
            >
              {card ? (
                <div className="h-full w-full p-2">{card}</div>
              ) : isFirstEmptyToDoSlot ? (
                <div className="h-full w-full flex items-center justify-center">
                  <PlusCircle className="w-10 h-10 text-[#1a1a18] transition-all duration-200 group-hover:scale-110" />
                </div>
              ) : (
                <div className="h-full w-full" />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

const TodaysTasks = (props: TodaysTasksProps) => {
  const { events = [], loading } = props;
  const { events: contextEvents, loading: contextLoading } =
    useContext(EventContext);
  const [expandedEventId, setExpandedEventId] = useState<number | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());

  useEffect(() => {
    console.time("todaysTasks render");
    const timer = setInterval(() => {
      setCurrentDate(new Date());
    }, 60000);
    return () => {
      clearInterval(timer);
      console.timeEnd("todaysTasks render");
    };
  }, []);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
    }).format(date);
  };

  const formatCardDate = (dateString?: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    }).format(date);
  };

  const finalEvents = contextEvents.length ? contextEvents : events;
  const finalLoading = contextLoading || loading;

  const todayStr = currentDate.toDateString();
  const todaysEvents: EventWithProgress[] = finalEvents
    .filter((event: CalendarEvent) => {
      if (!event.startTime) return false;
      return new Date(event.startTime).toDateString() === todayStr;
    })
    .map((event) => ({
      ...event,
      progress: Math.floor(Math.random() * 80) + 10,
    }));

  const getEventStatus = (
    event: CalendarEvent
  ): "upcoming" | "in-progress" | "completed" => {
    const now = new Date();
    const startTime = event.startTime ? new Date(event.startTime) : null;
    const endTime = event.endTime ? new Date(event.endTime) : null;

    if (!startTime || !endTime) return "upcoming";
    if (now < startTime) return "upcoming";
    if (now > endTime) return "completed";
    return "in-progress";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-emerald-600";
      default:
        return "text-gray-400";
    }
  };

  const todoEvents = todaysEvents.filter(
    (event) => getEventStatus(event) === "upcoming"
  );
  const inProgressEvents = todaysEvents.filter(
    (event) => getEventStatus(event) === "in-progress"
  );
  const completedEvents = todaysEvents.filter(
    (event) => getEventStatus(event) === "completed"
  );

  const renderEventCard = (event: EventWithProgress) => {
    const status = getEventStatus(event);
    const isExpanded = event.id === expandedEventId;
    const progress = status === "completed" ? 100 : event.progress;

    return (
      <motion.div
        key={event.id || Math.random()}
        layout
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="group h-full w-full"
      >
        <Card
          className={`relative bg-white border border-gray-300 overflow-hidden transition-all duration-300 rounded-xl h-full flex flex-col shadow-sm hover:shadow-md ${
            isExpanded
              ? "ring-2 ring-gray-500 shadow-lg"
              : "hover:ring-1 hover:ring-gray-400"
          }`}
        >
          <span
            className={`absolute inset-y-0 left-0 w-2 rounded-l-xl ${
              status === "in-progress"
                ? "bg-blue-500"
                : status === "completed"
                ? "bg-green-500"
                : "bg-sky-500"
            }`}
          ></span>
          <CardContent className="p-5 pl-6 flex-1 flex flex-col min-h-0">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1 pr-3">
                <h3
                  className={`text-base font-semibold text-[#1a1a18] truncate leading-snug ${manrope.variable} font-sans`}
                  style={{ marginTop: "-2px" }}
                >
                  {event.title}
                </h3>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="w-8 h-8 -mr-2 -mt-2 text-gray-500 hover:text-[#1a1a18] flex-shrink-0 transition-all hover:bg-gray-100"
                onClick={() =>
                  setExpandedEventId(isExpanded ? null : event.id || null)
                }
              >
                <ChevronRight
                  className={`w-4 h-4 transition-transform duration-200 ${
                    isExpanded ? "rotate-90" : ""
                  }`}
                />
              </Button>
            </div>

            <div className="my-auto space-y-2">
              <div className="flex justify-between items-center text-sm">
                <div className="flex items-center text-gray-500 font-medium">
                  <span className="font-serif">Progress</span>
                </div>
                <span
                  className={`font-mono text-gray-600 ${manrope.variable} font-sans font-semibold`}
                >
                  {Math.round(progress / 10)}/10
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-emerald-400 to-emerald-500 h-2 rounded-full transition-all duration-500 shadow-sm"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>

            <motion.div
              animate={{ height: isExpanded ? "auto" : 0 }}
              className="overflow-hidden"
              transition={{ duration: 0.3 }}
            >
              <div className="space-y-3 pt-4 text-sm border-t border-gray-200 mt-4 font-serif">
                {event.location && (
                  <div className="flex items-start gap-3 text-gray-500">
                    <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
                    <p className="leading-relaxed">{event.location}</p>
                  </div>
                )}
              </div>
            </motion.div>

            <div className="mt-auto pt-4 flex justify-between items-center">
              <div
                className={`bg-red-50 text-red-600 text-xs font-semibold px-3 py-1.5 rounded-lg ${manrope.variable} font-sans`}
              >
                {formatCardDate(event.startTime)}
              </div>
              <p
                className={`${getStatusColor(
                  status
                )} text-sm capitalize font-semibold font-serif`}
              >
                {status}
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  return (
    <div
      className={`w-full h-full flex flex-col text-gray-400 ${manrope.variable} font-sans`}
    >
      <div className="mb-8 flex-shrink-0">
        <div className="flex items-center justify-between mb-3"></div>
        <p
          className="text-sm text-gray-500 font-medium lining-nums tabular-nums font-serif"
          style={{ marginTop: "-2px" }}
        >
          {formatDate(currentDate)}
        </p>
      </div>
      {finalLoading ? (
        <div className="flex-1 flex items-center justify-center h-full bg-gray-100 rounded-2xl">
          <div className="animate-pulse flex gap-6">
            <div className="w-14 h-14 bg-gray-200 rounded-full"></div>
            <div className="space-y-3">
              <div className="h-5 w-36 bg-gray-200 rounded-lg"></div>
              <div className="h-5 w-28 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-6 min-h-0">
          <StatusColumn title="To Do" events={todoEvents}>
            {todoEvents.map(renderEventCard)}
          </StatusColumn>

          <StatusColumn title="In Progress" events={inProgressEvents}>
            {inProgressEvents.map(renderEventCard)}
          </StatusColumn>

          <StatusColumn title="Completed" events={completedEvents}>
            {completedEvents.map(renderEventCard)}
          </StatusColumn>
        </div>
      )}
    </div>
  );
};

export default TodaysTasks;

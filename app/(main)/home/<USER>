// home/page.tsx
import React from "react";
import WelcomeMessage from "./welcomeMessage";
import TodaysTasks from "./todaysTasks";

type CardProps = {
  children: React.ReactNode;
  className?: string;
};

const Card = ({ children, className = "" }: CardProps) => (
  <div
    className={`bg-white/70 backdrop-blur-xl border border-gray-200/80 shadow-sm rounded-2xl ${className}`}
  >
    {children}
  </div>
);

const home = () => {
  return (
    // The Canvas: Now with the specified background pattern
    <div
      className="flex flex-col h-screen w-full text-sm"
      style={{
        backgroundColor: "#fdfdfd", // Changed to gray-50
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`, // Changed SVG fill to gray-200
        backgroundRepeat: "repeat",
      }}
    >
      {/* Main Content Area using the new 12x12 grid */}
      <div className="p-4 sm:p-6 lg:p-8 h-screen grid grid-cols-12 grid-rows-12 gap-4">
        {/* 1 - Welcome Message */}
        <div className="col-span-12 row-span-2">
          <WelcomeMessage />
        </div>

        {/* 2 - Todays Classes */}
        <Card className="col-span-12 row-span-10 col-start-1 row-start-3 p-4 sm:p-4 flex flex-col min-h-0  border-2">
          <div className="flex-1 overflow-y-auto pr-2">
            <TodaysTasks />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default home;

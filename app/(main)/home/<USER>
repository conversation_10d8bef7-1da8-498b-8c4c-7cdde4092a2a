"use client";

import React, { useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { AlignJustify, Smartphone } from "lucide-react";
import { Manrope } from "next/font/google";

// Font configuration
const manrope = Manrope({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-manrope",
});

const WelcomeMessage: React.FC = () => {
  const { user } = useUser();

  useEffect(() => {
    console.time("welcomeMessage render");
    return () => {
      console.timeEnd("welcomeMessage render");
    };
  }, []);

  const nameToDisplay = user?.firstName || "Guest";
  const fullMessage = `Your tasks for today, ${nameToDisplay}`;

  const currentCommaIndexInFullMessage = fullMessage.indexOf(",");
  let displayGreetingPart = fullMessage;
  let displayNamePart = "";

  if (currentCommaIndexInFullMessage !== -1) {
    displayGreetingPart = fullMessage.substring(
      0,
      currentCommaIndexInFullMessage + 1
    );
    if (fullMessage.length > currentCommaIndexInFullMessage + 1) {
      displayNamePart = fullMessage.substring(
        currentCommaIndexInFullMessage + 2
      );
    }
  }

  return (
    <div
      className={`relative flex flex-col h-full justify-end ${manrope.variable} font-sans`}
    >
      {/*
        The <style jsx> block that was here has been completely removed.
        This was the source of the persistent "Unclosed string" build error.
      */}

      <div className="flex items-center justify-between w-full py-4">
        {/* The 'shooting-star' class was part of the removed style block */}
        <div className="relative pb-2">
          <h1 className="text-4xl font-extralight tracking-tight h-[44px] flex items-center">
            <span className="text-gray-500 font-serif">
              {displayGreetingPart}
            </span>
            {displayNamePart && (
              // The 'font-canva-sans' class was also removed as it is no longer defined
              <span className="font-normal text-[#1a1a18] ml-2">
                {displayNamePart}
              </span>
            )}
          </h1>
        </div>

        <div className="flex items-center gap-6">
          <AlignJustify className="h-10 w-6 text-[#1a1a18] transition-opacity hover:opacity-60" />

          <button className="flex items-center justify-center h-10 gap-x-2 bg-[#1a1a18] rounded-full p-4 text-white transition-transform hover:scale-105 shadow-sm">
            <Smartphone className="h-5 w-5" />
            <span className="text-sm font-medium">App</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default WelcomeMessage;

/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  BookOpen,
  Info,
  Layers,
  Volume2,
  MessageSquare,
  Clock,
  Mic,
  Type,
  AlignJustify,
  PauseCircle,
  Check,
} from "lucide-react";
import type { TajweedRule } from "./page";
import { tajweedExamples } from "@/app/tajweed/tajweedExamples";
import { tajweedLessons } from "@/app/tajweed/tajweedLessons"; // Import lessons

/* ------------------------------------------------------------------ */
/*  Local helper types (unchanged except for optional nonExamples)    */
/* ------------------------------------------------------------------ */

type TajweedRuleWithCategory = TajweedRule & {
  categoryId?: string[];
  learningStage?: string;
};
type CategoryData = {
  id: string;
  name: string;
  arabicName: string;
  description: string;
  rules: TajweedRuleWithCategory[];
};
type LearningStage = {
  id: string;
  name: string;
  arabicName: string;
  description: string;
  categories: string[];
  icon: React.ReactNode;
  unit: number;
};
type TajweedGridProps = { tajweedRules: TajweedRule[] };

/* ============================ COMPONENT ============================ */

const TajweedGrid: React.FC<TajweedGridProps> = ({ tajweedRules }) => {
  const router = useRouter();

  /* -------------------- state & refs (enhanced with viewMode) ------------------- */
  const [viewMode, setViewMode] = useState<"landing" | "unit-detail">(
    "landing"
  );
  const [activeTab, setActiveTab] = useState("all");
  const [selectedRule, setSelectedRule] = useState<TajweedRule | null>(null);
  const [showModal, setShowModal] = useState(false);
  // REMOVED: const [hoveredRule, setHoveredRule] = useState<{ ruleId: string; categoryId?: string; } | null>(null); // This state is no longer needed with pure CSS group-hover transitions
  const [categoryData, setCategoryData] = useState<CategoryData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollIndicators, setShowScrollIndicators] = useState(false);
  const [activeScrollIndex, setActiveScrollIndex] = useState(0);

  /* ------------------------ verify examples ------------------------ */
  useEffect(() => {
    console.log(
      "TajweedGrid: tajweedExamples keys →",
      Object.keys(tajweedExamples)
    );
    console.log(
      "TajweedGrid: tajweedLessons keys →",
      Object.keys(tajweedLessons)
    );
  }, []);

  /* ----------------- learning-stage descriptor list ---------------- */
  const learningStages: LearningStage[] = [
    {
      id: "introduction",
      name: "Introduction to Tajweed",
      arabicName: "مقدمة في علم التجويد",
      description:
        "Learn the importance, benefits, and basic principles of Tajweed.",
      categories: ["intro"],
      icon: <BookOpen className="w-4 h-4" />,
      unit: 1,
    },
    {
      id: "makharij",
      name: "Makharij al-Huruf",
      arabicName: "مخارج الحروف",
      description:
        "Points of articulation - 5 areas and 17 specific points where each letter is pronounced.",
      categories: ["makharij"],
      icon: <Mic className="w-4 h-4" />,
      unit: 2,
    },
    {
      id: "sifaat",
      name: "Sifaat al-Huruf",
      arabicName: "صفات الحروف",
      description:
        "Characteristics of letters - permanent and conditional attributes that define how each letter sounds.",
      categories: ["sifaat"],
      icon: <Info className="w-4 h-4" />,
      unit: 3,
    },
    {
      id: "noon-tanween",
      name: "Noon Saakin & Tanween Rules",
      arabicName: "أحكام النون الساكنة والتنوين",
      description:
        "Rules for noon with sukoon and tanween - Izhar, Idgham, Iqlab, and Ikhfa.",
      categories: ["noon"],
      icon: <Layers className="w-4 h-4" />,
      unit: 4,
    },
    {
      id: "meem",
      name: "Meem Saakin Rules",
      arabicName: "أحكام الميم الساكنة",
      description:
        "Rules for meem with sukoon - Ikhfa Shafawi, Idgham Shafawi, and Izhar Shafawi.",
      categories: ["meem"],
      icon: <MessageSquare className="w-4 h-4" />,
      unit: 5,
    },
    {
      id: "qalqalah",
      name: "Qalqalah",
      arabicName: "القلقلة",
      description:
        "The echoing sound of five specific letters (ق ط ب ج د) when they have sukoon.",
      categories: ["qalqalah"],
      icon: <Volume2 className="w-4 h-4" />,
      unit: 6,
    },
    {
      id: "ghunnah",
      name: "Ghunnah",
      arabicName: "الغنة",
      description:
        "The nasal sound that occurs in noon and meem with shaddah, held for 2 counts.",
      categories: ["ghunnah"],
      icon: <Mic className="w-4 h-4" />,
      unit: 7,
    },
    {
      id: "laam",
      name: "Laam Rules",
      arabicName: "أحكام اللام",
      description:
        "Rules for the letter laam, including the laam of Allah and the definite article.",
      categories: ["laam"],
      icon: <Type className="w-4 h-4" />,
      unit: 8,
    },
    {
      id: "madd",
      name: "Madd Rules",
      arabicName: "المدود",
      description:
        "Elongation rules for vowels, from basic (2 counts) to specialized (4-6 counts).",
      categories: ["madd"],
      icon: <Clock className="w-4 h-4" />,
      unit: 9,
    },
    {
      id: "tafkheem-tarqeeq",
      name: "Tafkheem and Tarqeeq",
      arabicName: "التفخيم والترقيق",
      description:
        "Heavy and light pronunciation of letters, including special rules for Ra and Lam.",
      categories: ["tafkheem-tarqeeq"],
      icon: <Volume2 className="w-4 h-4" />,
      unit: 10,
    },
    {
      id: "hamzah",
      name: "Hamzatul Wasl and Qat'",
      arabicName: "همزة الوصل والقطع",
      description:
        "Rules for when to pronounce or skip the Alif, with Quranic examples.",
      categories: ["hamzah"],
      icon: <AlignJustify className="w-4 h-4" />,
      unit: 11,
    },
    {
      id: "waqf",
      name: "Waqf Rules",
      arabicName: "أحكام الوقف",
      description:
        "Rules for stopping and pausing, including stopping symbols and proper techniques.",
      categories: ["waqf"],
      icon: <PauseCircle className="w-4 h-4" />,
      unit: 12,
    },
  ];

  /* ##################################################################
     1.  ORGANISE CATEGORY DATA  (patched for new IDs)
  ################################################################## */
  useEffect(() => {
    organizeCategoryData();
  }, [tajweedRules]);

  const organizeCategoryData = () => {
    setIsLoading(true);
    try {
      const categories: CategoryData[] = [
        {
          id: "intro",
          name: "Introduction",
          arabicName: "المقدمة",
          description: "The importance and benefits of learning Tajweed",
          rules: [],
        },
        {
          id: "makharij",
          name: "Articulation Points",
          arabicName: "مخارج الحروف",
          description:
            "Where the letters are pronounced from in the vocal tract",
          rules: [],
        },
        {
          id: "sifaat",
          name: "Letter Characteristics",
          arabicName: "صفات الحروف",
          description:
            "How letters are pronounced (voiced/unvoiced, strong/soft)",
          rules: [],
        },
        {
          id: "noon",
          name: "Noon & Tanween Rules",
          arabicName: "أحكام النون الساكنة والتنوين",
          description: "Rules related to Noon with sukoon and tanween",
          rules: [],
        },
        {
          id: "meem",
          name: "Meem Rules",
          arabicName: "أحكام الميم الساكنة",
          description: "Rules related to Meem with sukoon",
          rules: [],
        },
        {
          id: "qalqalah",
          name: "Qalqalah",
          arabicName: "القلقلة",
          description: "The bouncing/echoing of certain letters",
          rules: [],
        },
        {
          id: "ghunnah",
          name: "Ghunnah",
          arabicName: "الغنة",
          description: "Nasalization in Tajweed recitation",
          rules: [],
        },
        {
          id: "laam",
          name: "Laam Rules",
          arabicName: "أحكام اللام",
          description: "Rules related to the letter Laam",
          rules: [],
        },
        {
          id: "madd",
          name: "Madd Rules",
          arabicName: "المدود",
          description: "Rules of elongation in recitation",
          rules: [],
        },
        {
          id: "tafkheem-tarqeeq",
          name: "Tafkheem & Tarqeeq",
          arabicName: "التفخيم والترقيق",
          description: "Heavy and light pronunciation of letters",
          rules: [],
        },
        {
          id: "hamzah",
          name: "Hamzah Rules",
          arabicName: "همزة الوصل والقطع",
          description: "Rules for connecting and cutting hamzah",
          rules: [],
        },
        {
          id: "waqf",
          name: "Waqf Rules",
          arabicName: "أحكام الوقف",
          description: "Rules of stopping in recitation",
          rules: [],
        },
      ];

      /* keep the rule → stage mapping as-is */
      const rulesWithStage = tajweedRules.map((rule) => {
        /* simple switch */
        const map: Record<string, string> = {
          intro: "introduction",
          makharij: "makharij",
          sifaat: "sifaat",
          noon: "noon-tanween",
          meem: "meem",
          qalqalah: "qalqalah",
          ghunnah: "ghunnah",
          laam: "laam",
          madd: "madd",
          "tafkheem-tarqeeq": "tafkheem-tarqeeq",
          hamzah: "hamzah",
          waqf: "waqf",
        };
        return { ...rule, learningStage: map[rule.category] || "introduction" };
      });

      rulesWithStage.forEach((rule) => {
        const cat = categories.find((c) => c.id === rule.category);
        if (cat) cat.rules.push({ ...rule, categoryId: [rule.category] });
      });

      categories.forEach((c) =>
        c.rules.sort((a, b) => (a.learningOrder || 0) - (b.learningOrder || 0))
      );
      setCategoryData(categories);
    } catch (err) {
      console.error("Error organising tajweed rules:", err);
    } finally {
      setIsLoading(false);
    }
  };

  /* ##################################################################
     2.  ID-MAPPING FUNCTION (simplified for standardized IDs)
  ################################################################## */
  const mapRuleIdToExampleId = (gridRuleId: string): string => {
    // Now that we've standardized the rule IDs, this function can be much simpler
    // It should directly pass through most IDs without mapping
    console.log(`Using standardized rule ID: ${gridRuleId}`);
    return gridRuleId;
  };

  /* ##################################################################
     3.  NEW NAVIGATION HANDLERS
  ################################################################## */
  const handleUnitCardClick = (unitId: string) => {
    setActiveTab(unitId);
    setViewMode("unit-detail");
  };

  const handleBackClick = () => {
    if (viewMode === "unit-detail") {
      setViewMode("landing");
      setActiveTab("all");
    } else {
      router.push("/challenges");
    }
  };

  /* ##################################################################
     4.  NEW LANDING PAGE RENDER
  ################################################################## */
  const renderLandingView = () => {
    // Create unit cards data from learningStages
    const unitCards = learningStages.map((stage) => ({
      id: stage.id,
      unit: stage.unit,
      englishTitle: stage.name,
      arabicTitle: stage.arabicName,
      description: stage.description,
      icon: stage.icon,
    }));

    return (
      <div className="w-full px-4 md:px-6">
        <div className="relative">
          {/* Fixed 3x4 grid layout (12 cards) */}
          <div className="grid grid-cols-3 gap-4">
            {unitCards.map((unit) => {
              // Check if unit has available lessons
              const unitRules = tajweedRules.filter((rule) => {
                const stageMap: Record<string, string> = {
                  intro: "introduction",
                  makharij: "makharij",
                  sifaat: "sifaat",
                  noon: "noon-tanween",
                  meem: "meem",
                  qalqalah: "qalqalah",
                  ghunnah: "ghunnah",
                  laam: "laam",
                  madd: "madd",
                  "tafkheem-tarqeeq": "tafkheem-tarqeeq",
                  hamzah: "hamzah",
                  waqf: "waqf",
                };
                return stageMap[rule.category] === unit.id;
              });

              const hasAvailableLessons = unitRules.some(
                (rule) =>
                  !!tajweedLessons[rule.id as keyof typeof tajweedLessons]
              );

              return (
                <div
                  key={unit.id}
                  onClick={() => handleUnitCardClick(unit.id)}
                  className={`
                    group relative bg-white border rounded-xl
                    transition-all duration-300 ease-out
                    p-4 flex flex-col min-h-[180px]
                    hover:shadow-md hover:scale-[1.02]
                    border-gray-200
                    ${
                      hasAvailableLessons
                        ? "cursor-pointer"
                        : "cursor-not-allowed opacity-70"
                    }
                  `}
                  title={
                    !hasAvailableLessons
                      ? `${unit.englishTitle} lessons are not yet available`
                      : undefined
                  }
                >
                  {/* Header with number and status */}
                  <div className="flex items-center justify-between mb-3">
                    {/* Number Badge */}
                    <div className="relative h-8 w-8">
                      <div
                        className={`
    absolute inset-0 rounded-lg
    bg-gray-50
    transition duration-300
    ${
      hasAvailableLessons
        ? "group-hover:rotate-45 group-hover:bg-[#1a1a18]"
        : ""
    }
  `}
                      />

                      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <span
                          className={`
                            font-bold text-sm transition-colors duration-200
                            text-gray-900
                            ${
                              hasAvailableLessons
                                ? "group-hover:text-white"
                                : ""
                            }
                          `}
                        >
                          {unit.unit}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Content section */}
                  <div className="flex-1 flex flex-col justify-center text-center space-y-2">
                    {/* English Title */}
                    <h3 className="text-lg font-semibold text-gray-900 leading-tight">
                      {unit.englishTitle}
                      {!hasAvailableLessons && (
                        <span className="text-gray-500 text-xs block mt-1">
                          (Coming Soon)
                        </span>
                      )}
                    </h3>

                    {/* Arabic Title */}
                    <p
                      className="text-lg font-arabic text-gray-700 leading-relaxed"
                      dir="rtl"
                    >
                      {unit.arabicTitle}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Empty state */}
          {unitCards.length === 0 && (
            <div className="flex flex-col items-center justify-center py-12 text-center bg-white rounded-xl border border-gray-200 shadow-sm p-6">
              <p className="text-lg text-gray-600 mb-4">No units available</p>
              <p className="text-sm text-gray-500">
                Check back later for new content
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Helper sub-component for rendering rule blocks
  const RuleBlock = ({
    title,
    arTitle,
    desc,
    rules,
    innerRulesContainerClass, // New prop
  }: {
    title: string;
    arTitle?: string; // Made optional as not all titles have arabic equivalent
    desc?: string; // Made optional
    rules: TajweedRuleWithCategory[];
    innerRulesContainerClass: string;
  }) => (
    <div className="bg-gray-50 rounded-lg border border-gray-200 p-4 shadow-sm">
      <div className="flex items-center mb-4">
        <h3 className="text-base font-semibold text-gray-900">{title}</h3>
        {arTitle && (
          <span className="font-arabic text-gray-700 ml-2">| {arTitle}</span>
        )}
      </div>
      {desc && <p className="text-sm text-gray-600 mb-4">{desc}</p>}
      <div className={innerRulesContainerClass}>
        {rules.map((r) => renderRuleCard(r))}
      </div>
    </div>
  );

  /* ##################################################################
     5.  RENDER HELPERS – Updated for standardized IDs (unchanged)
  ################################################################## */

  /* -- Makharij group: typo fix (shafAta**i**n) -------------------- */
  const renderMakharij = () => {
    const pts = tajweedRules.filter((r) => r.id.startsWith("makharij-"));

    const sections = [
      {
        id: "throat",
        title: "Throat Letters",
        rules: pts.filter((r) => r.id.includes("halq")),
      },
      {
        id: "oralCavity",
        title: "Oral Cavity",
        rules: pts.filter((r) => r.id.includes("jawf")),
      },
      {
        id: "tongue",
        title: "Tongue Letters",
        rules: pts.filter((r) => r.id.includes("lisan")),
      },
      {
        id: "lips",
        title: "Lip Letters",
        rules: pts.filter((r) => r.id.includes("shafat")),
      },
      {
        id: "nasalPassage",
        title: "Nasal Passage",
        rules: pts.filter((r) => r.id.includes("khayshum")),
      },
    ];

    const activeSections = sections.filter(
      (section) => section.rules.length > 0
    );
    const isOnlyOneSection = activeSections.length === 1;

    const mainContainerClass = isOnlyOneSection
      ? "space-y-6"
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {activeSections.map((section) => (
          <RuleBlock
            key={section.id}
            title={section.title}
            rules={section.rules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        ))}
      </div>
    );
  };

  /* -- Sifaat: fix "jahr" typo ------------------------------------- */
  const renderSifaat = () => {
    const charRules = tajweedRules.filter((r) => r.category === "sifaat");
    const permanent = charRules.filter(
      (r) =>
        r.id.includes("jahr") ||
        r.id.includes("hams") ||
        r.id.includes("shiddah") ||
        r.id.includes("rakhawah") ||
        r.id.includes("istilaa") ||
        r.id.includes("istifal")
    );
    const conditional = charRules.filter(
      (r) =>
        r.id.includes("tafkheem") ||
        r.id.includes("tarqeeq") ||
        r.id.includes("qalqalah") ||
        r.id.includes("leen")
    );
    // This opposite logic is robust as it takes what's left
    const opposite = charRules.filter(
      (r) => !permanent.includes(r) && !conditional.includes(r)
    );

    const sections = [
      { id: "permanent", title: "Permanent Characteristics", rules: permanent },
      {
        id: "conditional",
        title: "Conditional Characteristics",
        rules: conditional,
      },
      {
        id: "opposite",
        title: "Characteristics in Opposites",
        rules: opposite,
      },
    ];

    // Filter out sections that have no rules
    const activeSections = sections.filter(
      (section) => section.rules.length > 0
    );

    const isOnlyOneSection = activeSections.length === 1;

    // Determine the main container class (for the overall sifaat layout)
    // If only one section is active, it should be full-width, otherwise grid
    const mainContainerClass = isOnlyOneSection
      ? "space-y-6" // If only one sub-category, it takes full width
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"; // If multiple, use the standard 3-col grid

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {activeSections.map((section) => (
          <RuleBlock
            key={section.id}
            title={section.title}
            rules={section.rules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        ))}
      </div>
    );
  };

  /* -- Noon/Tanween, Meem rules: Updated for standardized IDs ------ */
  const renderNoonTanweenRules = () => {
    // Get noon-related rules with standardized IDs
    const noonRules = tajweedRules.filter((rule) => rule.category === "noon");

    // Group by the standardized rule IDs
    const izharRules = noonRules.filter((rule) => rule.id === "izhar");
    const idghamRules = noonRules.filter(
      (rule) =>
        rule.id === "idgham-ghunnah" || rule.id === "idgham-without-ghunnah"
    );
    const iqlabRules = noonRules.filter((rule) => rule.id === "iqlab");
    const ikhfaRules = noonRules.filter((rule) => rule.id === "ikhfa");

    const sections = [
      { id: "izhar", title: "Ith-haar", rules: izharRules },
      { id: "idgham", title: "Idghaam", rules: idghamRules },
      { id: "iqlab", title: "Iqlab", rules: iqlabRules },
      { id: "ikhfa", title: "Ikhfaa", rules: ikhfaRules },
    ];

    const activeSections = sections.filter(
      (section) => section.rules.length > 0
    );
    const isOnlyOneSection = activeSections.length === 1;

    const mainContainerClass = isOnlyOneSection
      ? "space-y-6"
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {activeSections.map((section) => (
          <RuleBlock
            key={section.id}
            title={section.title}
            rules={section.rules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        ))}
      </div>
    );
  };

  const renderMeemRules = () => {
    // Get meem-related rules with standardized IDs
    const meemRules = tajweedRules.filter((rule) => rule.category === "meem");

    // Group by the standardized rule IDs
    const ikhfaShafawiRules = meemRules.filter(
      (rule) => rule.id === "ikhfa-shafawi"
    );
    const idghamShafawiRules = meemRules.filter(
      (rule) => rule.id === "idgham-shafawi"
    );
    const izharShafawiRules = meemRules.filter(
      (rule) => rule.id === "izhar-shafawi"
    );

    const sections = [
      {
        id: "izharShafawi",
        title: "Ith-haar Shafawi",
        rules: izharShafawiRules,
      },
      {
        id: "idghamShafawi",
        title: "Idghaam Shafawi",
        rules: idghamShafawiRules,
      },
      { id: "ikhfaShafawi", title: "Ikhfaa Shafawi", rules: ikhfaShafawiRules },
    ];

    const activeSections = sections.filter(
      (section) => section.rules.length > 0
    );
    const isOnlyOneSection = activeSections.length === 1;

    const mainContainerClass = isOnlyOneSection
      ? "space-y-6"
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {activeSections.map((section) => (
          <RuleBlock
            key={section.id}
            title={section.title}
            rules={section.rules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        ))}
      </div>
    );
  };

  /* -- Qalqalah: accept single merged rule ------------------------- */
  const renderQalqalahRules = () => {
    const qalqalahRules = tajweedRules.filter((r) => r.category === "qalqalah");

    // Since Qalqalah is always just one logical group, it should use the full-width layout
    const isOnlyOneSection = true;

    const mainContainerClass = isOnlyOneSection
      ? "space-y-6" // Full width layout
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" // Full width grid
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {qalqalahRules.length > 0 && (
          <RuleBlock
            title="Qalqalah"
            arTitle="القلقلة"
            rules={qalqalahRules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        )}
      </div>
    );
  };

  /* -- Ghunnah unchanged (rule id still "ghunnah") ----------------- */
  const renderGhunnahRules = () => {
    // Get ghunnah-related rules
    const ghunnahRules = tajweedRules.filter(
      (rule) => rule.category === "ghunnah"
    );

    // Also get related ghunnah rules from other categories
    const idghamWithGhunnah = tajweedRules.filter(
      (rule) => rule.id === "idgham-ghunnah" && rule.category === "noon"
    );
    const ikhfaRules = tajweedRules.filter(
      (rule) => rule.id === "ikhfa" && rule.category === "noon"
    );
    const ikhfaShafawi = tajweedRules.filter(
      (rule) => rule.id === "ikhfa-shafawi" && rule.category === "meem"
    );

    const sections = [
      { id: "ghunnah", title: "Ghunnah", rules: ghunnahRules },
      {
        id: "ghunnahInIdgham",
        title: "Ghunnah in Idgham",
        rules: idghamWithGhunnah,
      },
      {
        id: "ghunnahInIkhfa",
        title: "Ghunnah in Ikhfaa",
        rules: [...ikhfaRules, ...ikhfaShafawi],
      },
    ];

    const activeSections = sections.filter(
      (section) => section.rules.length > 0
    );
    const isOnlyOneSection = activeSections.length === 1;

    const mainContainerClass = isOnlyOneSection
      ? "space-y-6"
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {activeSections.map((section) => (
          <RuleBlock
            key={section.id}
            title={section.title}
            rules={section.rules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        ))}
      </div>
    );
  };

  /* -- Laam: Updated for standardized IDs -------------------------- */
  const renderLaamRules = () => {
    const laamRules = tajweedRules.filter((r) => r.category === "laam");
    const laamAllah = laamRules.filter((r) => r.id === "laam-allah");
    const laamQamariyyah = laamRules.filter((r) => r.id === "laam-qamariyyah");
    const laamShamsiyyah = laamRules.filter((r) => r.id === "laam-shamsiyyah");
    const others = laamRules.filter(
      (r) =>
        !laamAllah.includes(r) &&
        !laamQamariyyah.includes(r) &&
        !laamShamsiyyah.includes(r)
    );

    const sections = [
      { id: "laamAllah", title: "Laam of Allah", rules: laamAllah },
      {
        id: "laamAl",
        title: "Laam of Al (Definite Article)",
        rules: [...laamQamariyyah, ...laamShamsiyyah],
      },
      { id: "otherLaam", title: "Other Laam Rules", rules: others },
    ];

    const activeSections = sections.filter(
      (section) => section.rules.length > 0
    );
    const isOnlyOneSection = activeSections.length === 1;

    const mainContainerClass = isOnlyOneSection
      ? "space-y-6"
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {activeSections.map((section) => (
          <RuleBlock
            key={section.id}
            title={section.title}
            rules={section.rules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        ))}
      </div>
    );
  };

  /* -- Madd: new condensed IDs ------------------------------------ */
  const renderMaddRules = () => {
    const maddRules = tajweedRules.filter((r) => r.category === "madd");
    const naturalRules = maddRules.filter((r) => r.id === "madd-natural");
    const hamzaRules = maddRules.filter((r) => r.id === "madd-hamza");
    const sukoonRules = maddRules.filter((r) => r.id === "madd-sukoon");
    const otherMadd = maddRules.filter(
      (r) =>
        !naturalRules.includes(r) &&
        !hamzaRules.includes(r) &&
        !sukoonRules.includes(r)
    );

    const sections = [
      {
        id: "natural",
        title: "Natural Madd",
        arTitle: "المد الأصلي",
        rules: naturalRules,
      },
      {
        id: "hamza",
        title: "Hamza-based Madds",
        arTitle: "مد بسبب الهمز",
        rules: hamzaRules,
      },
      {
        id: "sukoon",
        title: "Sukoon-based Madds",
        arTitle: "مد بسبب السكون",
        rules: sukoonRules,
      },
      {
        id: "other",
        title: "Other Madd Types",
        arTitle: "أنواع أخرى للمد",
        desc: "Additional specialised elongations",
        rules: otherMadd,
      },
    ];

    const activeSections = sections.filter(
      (section) => section.rules.length > 0
    );
    const isOnlyOneSection = activeSections.length === 1;

    const mainContainerClass = isOnlyOneSection
      ? "space-y-6"
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {activeSections.map((section) => (
          <RuleBlock
            key={section.id}
            title={section.title}
            arTitle={section.arTitle}
            desc={section.desc}
            rules={section.rules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        ))}
      </div>
    );
  };

  /* -- Hamzah, Waqf, Tafkheem/Tarqeeq remain unaffected ----------- */
  const renderTafkheemTarqeeqRules = () => {
    // Get tafkheem/tarqeeq-related rules
    const tafkheemTarqeeqRules = tajweedRules.filter(
      (rule) => rule.category === "tafkheem-tarqeeq"
    );

    // Group by rule type
    const heavyLettersRules = tafkheemTarqeeqRules.filter((rule) =>
      rule.id.includes("tafkheem")
    );
    const lightLettersRules = tafkheemTarqeeqRules.filter((rule) =>
      rule.id.includes("tarqeeq")
    );
    const specialRules = tafkheemTarqeeqRules.filter(
      (rule) => rule.id.includes("ra") || rule.id.includes("lam")
    );
    const otherRules = tafkheemTarqeeqRules.filter(
      (rule) =>
        !rule.id.includes("tafkheem") &&
        !rule.id.includes("tarqeeq") &&
        !rule.id.includes("ra") &&
        !rule.id.includes("lam")
    );

    const sections = [
      { id: "heavy", title: "Heavy Letters", rules: heavyLettersRules },
      { id: "light", title: "Light Letters", rules: lightLettersRules },
      {
        id: "special",
        title: "Special Rules for Ra and Lam",
        rules: specialRules,
      },
      { id: "other", title: "Other Tafkheem/Tarqeeq Rules", rules: otherRules },
    ];

    const activeSections = sections.filter(
      (section) => section.rules.length > 0
    );
    const isOnlyOneSection = activeSections.length === 1;

    const mainContainerClass = isOnlyOneSection
      ? "space-y-6"
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {activeSections.map((section) => (
          <RuleBlock
            key={section.id}
            title={section.title}
            rules={section.rules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        ))}
      </div>
    );
  };

  const renderHamzahRules = () => {
    // Get hamzah-related rules
    const hamzahRules = tajweedRules.filter(
      (rule) => rule.category === "hamzah"
    );

    // Group by types
    const waslRules = hamzahRules.filter((rule) => rule.id === "hamzat-wasl");
    const qatRules = hamzahRules.filter((rule) => rule.id === "hamzat-qat");
    const positionsRules = hamzahRules.filter(
      (rule) => rule.id === "hamzah-positions"
    );
    const otherHamzahRules = hamzahRules.filter(
      (rule) =>
        !rule.id.includes("wasl") &&
        !rule.id.includes("qat") &&
        !rule.id.includes("positions")
    );

    const sections = [
      { id: "wasl", title: "Hamzatul Wasl", rules: waslRules },
      { id: "qat", title: "Hamzatul Qat'", rules: qatRules },
      { id: "positions", title: "Hamzah Positions", rules: positionsRules },
      { id: "other", title: "Other Hamzah Rules", rules: otherHamzahRules },
    ];

    const activeSections = sections.filter(
      (section) => section.rules.length > 0
    );
    const isOnlyOneSection = activeSections.length === 1;

    const mainContainerClass = isOnlyOneSection
      ? "space-y-6"
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {activeSections.map((section) => (
          <RuleBlock
            key={section.id}
            title={section.title}
            rules={section.rules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        ))}
      </div>
    );
  };

  const renderWaqfRules = () => {
    // Get waqf-related rules
    const waqfRules = tajweedRules.filter((rule) => rule.category === "waqf");

    // Group by types
    const taamRules = waqfRules.filter((rule) => rule.id === "waqf-taam");
    const kaafiRules = waqfRules.filter((rule) => rule.id === "waqf-kaafi");
    const hasanRules = waqfRules.filter((rule) => rule.id === "waqf-hasan");
    const qabihRules = waqfRules.filter((rule) => rule.id === "waqf-qabih");
    const signsRules = waqfRules.filter((rule) => rule.id === "waqf-signs");
    const otherWaqfRules = waqfRules.filter(
      (rule) =>
        !taamRules.includes(rule) &&
        !kaafiRules.includes(rule) &&
        !hasanRules.includes(rule) &&
        !qabihRules.includes(rule) &&
        !signsRules.includes(rule)
    );

    const sections = [
      { id: "taam", title: "Waqf Taam", rules: taamRules },
      { id: "kaafi", title: "Waqf Kaafi", rules: kaafiRules },
      { id: "hasan", title: "Waqf Hasan", rules: hasanRules },
      { id: "qabih", title: "Waqf Qabih", rules: qabihRules },
      { id: "signs", title: "Waqf Signs", rules: signsRules },
      { id: "other", title: "Other Waqf Rules", rules: otherWaqfRules },
    ];

    const activeSections = sections.filter(
      (section) => section.rules.length > 0
    );
    const isOnlyOneSection = activeSections.length === 1;

    const mainContainerClass = isOnlyOneSection
      ? "space-y-6"
      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6";

    const innerRulesContainerClass = isOnlyOneSection
      ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      : "space-y-4";

    return (
      <div className={mainContainerClass}>
        {activeSections.map((section) => (
          <RuleBlock
            key={section.id}
            title={section.title}
            rules={section.rules}
            innerRulesContainerClass={innerRulesContainerClass}
          />
        ))}
      </div>
    );
  };

  /* ##################################################################
     6.  EVERYTHING BELOW THIS LINE UPDATED FOR LESSON CHECKING
  ################################################################## */

  // Check for scrollability of tabs
  useEffect(() => {
    const checkScrollability = () => {
      const container = tabsContainerRef.current;
      if (container) {
        setShowScrollIndicators(container.scrollWidth > container.clientWidth);
      }
    };

    checkScrollability();
    window.addEventListener("resize", checkScrollability);

    return () => {
      window.removeEventListener("resize", checkScrollability);
    };
  }, []);

  // Handle scroll event on tabs container
  const handleTabsScroll = () => {
    const container = tabsContainerRef.current;
    if (container) {
      const scrollPosition = container.scrollLeft;
      const maxScroll = container.scrollWidth - container.clientWidth;

      // Calculate which indicator should be active
      const numIndicators = 3; // Number of scroll indicators
      const scrollPercentage = scrollPosition / maxScroll;
      const newActiveIndex = Math.min(
        Math.floor(scrollPercentage * numIndicators),
        numIndicators - 1
      );

      setActiveScrollIndex(newActiveIndex);
    }
  };

  // Function to scroll to a certain percentage of the container width
  const scrollToPercentage = (percentage: number) => {
    const container = tabsContainerRef.current;
    if (container) {
      const maxScroll = container.scrollWidth - container.clientWidth;
      container.scrollLeft = maxScroll * percentage;
    }
  };

  const handleRuleClick = (rule: TajweedRule) => {
    // Check if a lesson exists for this rule ID in tajweedLessons
    const lessonExists =
      !!tajweedLessons[rule.id as keyof typeof tajweedLessons];

    if (!lessonExists) {
      console.warn(`No lesson found for rule ID: ${rule.id}`);
      alert(`Lesson for ${rule.name} is not yet available.`);
      return;
    }

    if (rule.status === "inProgress") {
      setSelectedRule(rule);
      setShowModal(true);
    } else {
      startTajweedLearning(rule.id, "startFresh", rule.name, rule.arabicName);
    }
  };

  const startTajweedLearning = (
    ruleId: string,
    mode: "continue" | "startFresh",
    ruleName: string,
    arabicName: string
  ) => {
    // Map the rule ID to the example ID (should now be a direct match with standardized IDs)
    const exampleRuleId = mapRuleIdToExampleId(ruleId);

    // Log both IDs for debugging
    console.log(`Rule ID: ${ruleId}, Mapped example ID: ${exampleRuleId}`);

    // Check if a lesson exists for this rule ID
    const lessonExists =
      !!tajweedLessons[ruleId as keyof typeof tajweedLessons];
    if (!lessonExists) {
      console.error(`No lesson found for rule ID: ${ruleId}`);
      alert(`Lesson for ${ruleName} is not yet available.`);
      return;
    }

    // Check if the rule has examples in tajweedExamples
    const ruleExamples =
      tajweedExamples[exampleRuleId as keyof typeof tajweedExamples];

    let url = "";

    // Build URL based on available examples
    if (
      ruleExamples &&
      ruleExamples.examples &&
      ruleExamples.examples.length > 0
    ) {
      url = `/tajweed?ruleId=${encodeURIComponent(
        exampleRuleId
      )}&mode=example&ruleName=${encodeURIComponent(
        ruleName
      )}&ruleArabicName=${encodeURIComponent(arabicName)}`;

      console.log(
        `Using example mode for ${ruleName} with ${ruleExamples.examples.length} dedicated examples. URL: ${url}`
      );
    } else {
      // Fallback to regular mode with Surah 1 (Al-Fatihah)
      let surahNumber = 1;
      let versesRange = "";

      url = `/tajweed?ruleId=${encodeURIComponent(
        exampleRuleId
      )}&surahNumber=${encodeURIComponent(
        surahNumber
      )}&versesRange=${encodeURIComponent(
        versesRange
      )}&mode=${encodeURIComponent(mode)}&ruleName=${encodeURIComponent(
        ruleName
      )}&ruleArabicName=${encodeURIComponent(arabicName)}`;

      console.log(
        `No specific examples found for ${ruleName} (${exampleRuleId}), using regular mode with Surah ${surahNumber}`
      );
    }

    router.push(url);
    setShowModal(false);
  };

  const handleUserChoice = (choice: "continue" | "startFresh") => {
    if (selectedRule) {
      startTajweedLearning(
        selectedRule.id,
        choice,
        selectedRule.name,
        selectedRule.arabicName
      );
    }
  };

  // REMOVED: getStatusBgColor function as its logic is now inlined in JSX

  const getStatusBorderColor = (status?: string) => {
    switch (status) {
      case "completed":
        return "border-emerald-500";
      case "inProgress":
        return "border-amber-500";
      default:
        return "border-gray-200";
    }
  };

  // Get a unique color for each learning stage
  const getLearningStageColor = (stageId: string) => {
    switch (stageId) {
      case "introduction":
        return "text-gray-800";
      case "makharij":
        return "text-blue-800";
      case "sifaat":
        return "text-teal-800";
      case "noon-tanween":
        return "text-purple-800";
      case "meem":
        return "text-green-800";
      case "qalqalah":
        return "text-orange-800";
      case "ghunnah":
        return "text-pink-800";
      case "laam":
        return "text-cyan-800";
      case "madd":
        return "text-indigo-800";
      case "tafkheem-tarqeeq":
        return "text-red-800";
      case "hamzah":
        return "text-amber-800";
      case "waqf":
        return "text-lime-800";
      default:
        return "text-gray-800";
    }
  };

  // Render a single Tajweed Rule card (with learning stage badge and lesson availability check)
  const renderRuleCard = (
    rule: TajweedRuleWithCategory,
    categoryId?: string
  ) => {
    // REMOVED: isHovered state logic as it's handled by pure CSS group-hover
    const statusBorderColor = getStatusBorderColor(rule.status);
    const isCategoryView = activeTab !== "all";
    const stageColor = getLearningStageColor(
      rule.learningStage || "introduction"
    );

    // Check if a lesson exists for this rule
    const lessonExists =
      !!tajweedLessons[rule.id as keyof typeof tajweedLessons];
    const cardOpacity = lessonExists ? "opacity-100" : "opacity-70";
    const cursorStyle = lessonExists ? "cursor-pointer" : "cursor-not-allowed";

    return (
      <div
        key={`${rule.id}-${categoryId || "allView"}`}
        onClick={() => (lessonExists ? handleRuleClick(rule) : null)}
        // REMOVED: onMouseEnter and onMouseLeave handlers as they are no longer needed
        className={`
          group relative bg-white border rounded-xl ${
            isCategoryView ? "p-3" : "p-4"
          }
          transition-all duration-300 ease-out
          ${lessonExists ? "hover:shadow-md hover:scale-[1.02]" : ""}
          ${statusBorderColor} ${cardOpacity}
        `}
        title={
          !lessonExists
            ? `Lesson for ${rule.name} is not yet available`
            : undefined
        }
      >
        <div className="flex items-center">
          <div
            className={`relative flex-shrink-0 mr-3 ${
              isCategoryView ? "w-8 h-8" : "w-10 h-10"
            }`}
          >
            <div
              className={`
                absolute inset-0 rounded-lg
                bg-gray-50 // Default background color
                transition-all duration-300
                ${
                  lessonExists
                    ? "group-hover:rotate-45 group-hover:bg-[#1a1a18]"
                    : ""
                } // Rotate and change background on hover if lesson exists
                // Removed fixed 'rotate-45' from base state
              `}
            />
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <span
                className={`
                  font-bold ${
                    isCategoryView ? "text-sm" : "text-base"
                  } transition-colors duration-200
                  text-gray-900 // Default text color
                  ${
                    lessonExists ? "group-hover:text-white" : ""
                  } // Change text color to white on hover if lesson exists
                `}
              >
                {rule.id.substring(0, 1).toUpperCase()}
              </span>
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <h3
                    className={`${
                      isCategoryView ? "text-xs" : "text-sm"
                    } font-semibold text-gray-900 group-hover:text-gray-900`}
                  >
                    {rule.name}{" "}
                    {!lessonExists && (
                      <span className="text-gray-500 text-[10px]">
                        (Coming Soon)
                      </span>
                    )}
                  </h3>
                  {!isCategoryView && rule.learningStage && (
                    <span className={`text-xs ${stageColor}`}>
                      {learningStages.find(
                        (stage) => stage.id === rule.learningStage
                      )?.unit || ""}
                    </span>
                  )}
                </div>
                <p
                  className={`${
                    isCategoryView ? "text-xs" : "text-xs"
                  } text-gray-500 group-hover:text-gray-500`}
                >
                  {rule.description.substring(0, 50)}
                  {rule.description.length > 50 ? "..." : ""}
                </p>
              </div>
              <div className="text-right flex-shrink-0 pl-2">
                <span
                  className={`${
                    isCategoryView ? "text-sm" : "text-base"
                  } font-semibold text-gray-800 font-arabic block group-hover:text-gray-800`}
                >
                  {rule.arabicName}
                </span>
                {/* Show examples hint if available */}
                {rule.examples && rule.examples.length > 0 && (
                  <span className="text-xs text-gray-500">
                    {isCategoryView
                      ? `${
                          rule.examples.length > 3
                            ? rule.examples.slice(0, 3).join(", ") + "..."
                            : rule.examples.join(", ")
                        }`
                      : `${rule.examples.length} example${
                          rule.examples.length !== 1 ? "s" : ""
                        }`}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Method to render introduction to Tajweed
  const renderIntroduction = () => {
    // Get introduction rules
    const introRules = tajweedRules.filter((rule) => rule.category === "intro");

    // Since this section always represents a single logical group, apply the horizontal grid directly
    const innerRulesContainerClass =
      "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4";

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {introRules.length > 0 && ( // Only render if there are rules
          <div className="bg-gray-50 rounded-lg border border-gray-200 p-4 shadow-sm">
            <div className="flex items-center mb-4">
              <div>
                <h3 className="text-base font-semibold text-gray-900">
                  Introduction to Tajweed
                </h3>
              </div>
            </div>
            <div className={innerRulesContainerClass}>
              {introRules.map((rule) => renderRuleCard(rule))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderAllRulesView = () => {
    // Group rules by unit number for a more organized view
    const rulesByUnit = learningStages
      .sort((a, b) => a.unit - b.unit)
      .map((stage) => {
        const stageRules = tajweedRules
          .filter((rule) => {
            return stage.categories.includes(rule.category);
          })
          .sort((a, b) => (a.learningOrder || 0) - (b.learningOrder || 0));

        return {
          stage,
          rules: stageRules,
        };
      });

    return (
      <div className="space-y-8">
        {rulesByUnit.map(({ stage, rules }) => (
          <div key={stage.id} className="space-y-6">
            {/* Render rules directly without unit header card */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {rules.map((rule) => renderRuleCard(rule))}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderLearningStageView = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900"></div>
          <span className="ml-2 text-gray-700">Loading tajweed rules...</span>
        </div>
      );
    }

    // Render the appropriate content based on the active tab
    switch (activeTab) {
      case "introduction":
        return renderIntroduction();
      case "makharij":
        return renderMakharij();
      case "sifaat":
        return renderSifaat();
      case "noon-tanween":
        return renderNoonTanweenRules();
      case "meem":
        return renderMeemRules();
      case "qalqalah":
        return renderQalqalahRules();
      case "ghunnah":
        return renderGhunnahRules();
      case "laam":
        return renderLaamRules();
      case "madd":
        return renderMaddRules();
      case "tafkheem-tarqeeq":
        return renderTafkheemTarqeeqRules();
      case "hamzah":
        return renderHamzahRules();
      case "waqf":
        return renderWaqfRules();
      default:
        // For any other stage not specifically handled
        const selectedStage = learningStages.find(
          (stage) => stage.id === activeTab
        );
        if (!selectedStage) {
          return (
            <div className="text-center py-10 text-gray-500">
              No tajweed rules available for this learning stage. Try switching
              tabs.
            </div>
          );
        }

        return (
          <div className="space-y-8">
            {selectedStage.categories.map((categoryId) => {
              const category = categoryData.find(
                (cat) => cat.id === categoryId
              );
              if (!category || category.rules.length === 0) return null;

              // For general categories, not specifically rendered by dedicated functions,
              // we can keep the default grid layout for rules
              return (
                <div
                  key={category.id}
                  className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm"
                >
                  <div className="flex items-center mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {category.name}{" "}
                        <span className="font-arabic text-gray-700">
                          | {category.arabicName}
                        </span>
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {category.description}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {category.rules.map((rule) =>
                      renderRuleCard(rule, category.id)
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        );
    }
  };

  const renderCategoryView = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900"></div>
          <span className="ml-2 text-gray-700">Loading tajweed rules...</span>
        </div>
      );
    }

    const selectedCategory = categoryData.find((cat) => cat.id === activeTab);

    if (!selectedCategory || selectedCategory.rules.length === 0) {
      return (
        <div className="text-center py-10 text-gray-500">
          No tajweed rules available for this category. Try switching tabs.
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {selectedCategory.rules.map((rule) =>
          renderRuleCard(rule, selectedCategory.id)
        )}
      </div>
    );
  };

  // Determine current view based on view mode and tab
  const renderCurrentView = () => {
    if (viewMode === "landing") {
      return renderLandingView();
    }

    if (activeTab === "all") {
      return renderAllRulesView();
    }

    // Check if the active tab is a learning stage
    const isLearningStage = learningStages.some(
      (stage) => stage.id === activeTab
    );
    if (isLearningStage) {
      return renderLearningStageView();
    }

    // Otherwise, it's a category view
    return renderCategoryView();
  };

  // Group stages by units to create dropdown sections
  const stagesByUnit = learningStages.reduce((acc, stage) => {
    if (!acc[stage.unit]) {
      acc[stage.unit] = [];
    }
    acc[stage.unit].push(stage);
    return acc;
  }, {} as { [key: number]: LearningStage[] });

  return (
    <div
      className="flex flex-col h-screen text-sm"
      style={{
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
    >
      {/* Enhanced header with conditional rendering based on viewMode */}
      <div className="sticky top-0 z-40 bg-white px-3 py-2 md:px-4 md:py-3 border-b border-gray-200">
        {viewMode === "landing" ? (
          // Landing page header - compact style with title
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            {/* Back Button */}
            <button
              onClick={handleBackClick}
              className="p-1.5 rounded-full hover:bg-gray-100 transition-all duration-200"
            >
              <ArrowLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
            </button>

            {/* Title - Centered */}
            <h1 className="text-lg font-bold text-neutral-700 whitespace-nowrap">
              Tajweed Exercises
            </h1>

            {/* Placeholder div to balance the layout */}
            <div className="w-6 h-6"></div>
          </div>
        ) : (
          // Unit detail header - compact style with unit name
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            {/* Back Button */}
            <button
              onClick={handleBackClick}
              className="p-1.5 rounded-full hover:bg-gray-100 transition-all duration-200"
            >
              <ArrowLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
            </button>

            {/* Unit Title - Centered */}
            <h1 className="text-lg font-bold text-neutral-400 whitespace-nowrap">
              {learningStages.find((stage) => stage.id === activeTab)?.name ||
                "Unit Details"}
            </h1>

            {/* Placeholder div to balance the layout */}
            <div className="w-6 h-6"></div>
          </div>
        )}
      </div>

      <div className="flex-1 overflow-auto px-3 py-6 md:px-6">
        <div className="max-w-7xl mx-auto">{renderCurrentView()}</div>
      </div>

      {showModal && selectedRule && (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-900/40 backdrop-blur-sm z-50">
          <div className="bg-white rounded-xl shadow-lg p-5 w-full max-w-sm mx-3 transform transition-all duration-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              Continue Your Progress
            </h2>
            <div className="w-12 h-px bg-gray-200 mb-3"></div>
            <p className="text-sm text-gray-600 mb-5">
              You have existing progress with {selectedRule!.name} (
              {selectedRule!.arabicName}). Would you like to continue where you
              left off or start fresh?
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-white border border-gray-200 hover:bg-gray-50 text-gray-800 font-medium rounded-lg transition-colors duration-200"
                onClick={() => handleUserChoice("startFresh")}
              >
                Start Fresh
              </button>
              <button
                className="px-4 py-2 bg-[#1a1a18] hover:bg-gray-800 text-white font-medium rounded-lg transition-colors duration-200"
                onClick={() => handleUserChoice("continue")}
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx global>{`
        .hide-scrollbar {
          -ms-overflow-style: none; /* IE and Edge */
          scrollbar-width: none; /* Firefox */
        }
        .hide-scrollbar::-webkit-scrollbar {
          display: none; /* Chrome, Safari and Opera */
        }
      `}</style>
    </div>
  );
};

export default TajweedGrid;

// app/curriculum/page.tsx
import React, { Suspense } from "react";
import Link from "next/link";
import Loading from "./loading";
import { getCurriculums } from "@/db/queries";
import { ArrowLeft, Play, BookOpen, Clock, Users } from "lucide-react";

// Force dynamic rendering to prevent caching issues
export const dynamic = "force-dynamic";
export const revalidate = 0;

interface Curriculum {
  id: number;
  name: string;
  description?: string | null;
}

const CurriculumContent = async () => {
  console.log(
    "[CurriculumContent] Fetching curriculums at:",
    new Date().toISOString()
  );

  const curriculums: Curriculum[] = await getCurriculums();

  console.log("[CurriculumContent] Received curriculums:", {
    count: curriculums.length,
    ids: curriculums.map((c) => c.id),
    names: curriculums.map((c) => c.name),
  });

  if (!curriculums || curriculums.length === 0) {
    return (
      <div className="w-full px-6">
        <div className="flex flex-col items-center justify-center py-16">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <BookOpen className="w-12 h-12 text-gray-400" />
          </div>
          <p className="text-gray-600 text-xl font-medium mb-2">
            No curriculums found
          </p>
          <p className="text-gray-400 text-sm">
            Please check back later for new courses
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full pl-6 pr-6">
      <div className="flex flex-col gap-12 items-start">
        {curriculums.map((curriculum, index) => (
          <div
            key={curriculum.id}
            className="flex flex-col lg:flex-row items-start w-full gap-8 lg:gap-12 group"
          >
            {/* Card (left side) */}
            <Link
              href={`/coursesPlayer?curriculumId=${curriculum.id}`}
              className="relative shrink-0 block w-full lg:w-auto"
            >
              {index === 0 ? (
                // First card with background image - Enhanced
                <div className="relative overflow-hidden rounded-2xl w-full lg:w-[28rem] aspect-video transition-all duration-700 ease-out transform hover:scale-[1.02] hover:-translate-y-1">
                  <div
                    className="absolute inset-0 bg-cover bg-center transition-all duration-700 ease-out group-hover:scale-110"
                    style={{ backgroundImage: "url('/thubnail3.png')" }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent group-hover:from-black/60 transition-all duration-700 ease-out" />
                  <div className="absolute inset-0 bg-[#1a1a18]/20 group-hover:bg-[#1a1a18]/0 transition-all duration-700 ease-out" />

                  {/* Play button overlay */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out">
                    <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30 transform scale-75 group-hover:scale-100 transition-all duration-500 ease-out">
                      <Play
                        className="w-7 h-7 text-white ml-1"
                        fill="currentColor"
                      />
                    </div>
                  </div>

                  <div className="absolute bottom-0 left-0 right-0 p-8">
                    <div className="text-white space-y-3">
                      <h2 className="text-2xl font-bold leading-tight tracking-tight transform translate-y-2 group-hover:translate-y-0 transition-all duration-500 ease-out">
                        {curriculum.name}
                      </h2>
                      <div className="flex items-center text-gray-200 group-hover:text-white transition-all duration-500 ease-out opacity-80 group-hover:opacity-100">
                        <span className="text-sm font-medium tracking-wide">
                          Explore Exercises
                        </span>
                        <svg
                          className="w-4 h-4 ml-2 transition-all duration-500 ease-out transform group-hover:translate-x-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Subtle shadow enhancement */}
                  <div className="absolute inset-0 rounded-2xl shadow-2xl group-hover:shadow-3xl transition-all duration-700 ease-out pointer-events-none" />
                </div>
              ) : (
                // All other cards - Enhanced
                <div className="relative bg-white border border-gray-200 rounded-2xl w-full lg:w-[28rem] aspect-video transition-all duration-500 ease-out transform hover:scale-[1.02] hover:-translate-y-1 group-hover:shadow-2xl group-hover:border-gray-300 overflow-hidden">
                  {/* Subtle gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out" />

                  {/* Play button overlay */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out">
                    <div className="w-14 h-14 bg-gray-900/10 backdrop-blur-sm rounded-full flex items-center justify-center border border-gray-300/50 transform scale-75 group-hover:scale-100 transition-all duration-500 ease-out">
                      <Play
                        className="w-6 h-6 text-gray-700 ml-0.5"
                        fill="currentColor"
                      />
                    </div>
                  </div>

                  <div className="relative h-full flex flex-col justify-end p-8">
                    <div className="space-y-3">
                      <h2 className="text-2xl font-bold text-gray-900 leading-tight tracking-tight transform translate-y-1 group-hover:translate-y-0 transition-all duration-500 ease-out">
                        {curriculum.name}
                      </h2>
                      <div className="flex items-center text-gray-500 group-hover:text-gray-700 transition-all duration-500 ease-out">
                        <span className="text-sm font-medium tracking-wide">
                          Explore Exercises
                        </span>
                        <svg
                          className="w-4 h-4 ml-2 transition-all duration-500 ease-out transform group-hover:translate-x-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Border glow effect */}
                  <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-gray-200/50 transition-all duration-500 ease-out pointer-events-none" />
                </div>
              )}
            </Link>

            {/* Description (right side) - Enhanced */}
            <div className="flex-1 lg:py-4">
              {curriculum.description && (
                <div className="space-y-4">
                  <p className="text-gray-600 text-lg leading-relaxed font-light tracking-wide max-w-2xl">
                    {curriculum.description}
                  </p>

                  {/* Optional: Add some course metadata */}
                  <div className="flex items-center gap-6 text-sm text-gray-400 pt-2">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      <span>Self-paced</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      <span>All levels</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const CurriculumPage = async () => {
  return (
    <main
      className="min-h-screen"
      style={{
        backgroundColor: "#fafafa",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg fill='%23f0f0f0' fill-opacity='0.3'%3E%3Cpath fill-rule='evenodd' d='M0 0h16v16H0V0zm16 16h16v16H16V16zM0 0h8v8H0V0zm8 8h8v8H8V8zm8-8h8v8h-8V0zM8 0h8v8H8V0zm8 8h8v8h-8V8zM0 8h8v8H0V8zm16 0h8v8h-8V8zm8 8h8v8h-8v-8zm-8 0h8v8h-8V8zM0 16h8v8H0v-8zm8 0h8v8H8v-8zm8 0h8v8h-8v-8zM0 24h8v8H0v-8zm8 0h8v8H8v-8zm8 0h8v8h-8v-8z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
    >
      {/* Sticky Header with back arrow and centered title - Enhanced */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-md px-4 py-4 border-b border-gray-200/50 shadow-sm">
        <div className="relative max-w-7xl mx-auto">
          <h1 className="text-xl font-bold text-gray-900 text-center tracking-tight">
            My courses
          </h1>
        </div>
      </div>

      {/* Body Content - Enhanced */}
      <div className="py-20 px-4">
        <Suspense fallback={<Loading />}>
          <CurriculumContent />
        </Suspense>
      </div>
    </main>
  );
};

export default CurriculumPage;

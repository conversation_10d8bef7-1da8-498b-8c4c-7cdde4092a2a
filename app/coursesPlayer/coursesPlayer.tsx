"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
// Removed getCourseByCurriculum import because this is now handled in the server component
import {
  ArrowLeft,
  BookOpen,
  HelpCircle,
  Play,
  ChevronDown,
  CheckCircle,
  PlayCircle,
  ClipboardEdit, // Added new icon for extra practice
} from "lucide-react";

// Import the UstadTutor component
import UstadTutor from "../UstadTutor/UstadTutor";
// NEW: Import DeepDive (adjust the path if your folder differs)
import DeepDive from "../deepDive/deepDive";

interface CoursesPlayerPageProps {
  searchParams: { curriculumId?: string };
  serverCourse?: {
    id: number;
    englishTitle: string;
    arabicTitle: string;
    description: string;
    curriculumId: number | null;
    videoUrl?: string;
    curriculum: {
      id: number;
      name: string;
      description: string | null;
    } | null;
  } | null;
}

// Add interfaces for chapter data
interface Square {
  id: number;
  squareNumber: number;
  content: string;
  transliteration: string;
  audioUrl?: string;
  group: "basic" | "intermediate" | "advanced";
  overlayContent?: string;
}

interface ChapterData {
  id: number;
  title: string;
  squares: Square[];
  order?: number;
}

// Define a more specific type for subsections to include targetLearnUnitId and new types
interface Subsection {
  id: string;
  type: "video" | "lesson" | "quiz" | "extra_practice" | "deep_dive"; // NEW: added 'deep_dive'
  title: string;
  duration: string;
  icon: "Video" | "BookOpen" | "HelpCircle" | "ClipboardEdit"; // Using existing icons for consistency
  completed: boolean;
  targetLearnUnitId?: number; // For linking quizzes to specific learn units (not used by deep_dive but harmless to keep)
}

interface Chapter {
  id: number;
  title: string;
  icon: "BookOpen"; // Assuming all chapter icons are BookOpen for now
  expanded: boolean;
  completed: boolean;
  subsections: Subsection[];
}

// Updated lesson structure with standardized section names and structure
const initialChapters: Chapter[] = [
  {
    id: 1,
    title: "Lesson 1: Individual Alphabet Letters",
    icon: "BookOpen",
    expanded: true,
    completed: false,
    subsections: [
      {
        id: "1-1",
        type: "video",
        title: "Video: Introduction to Arabic Alphabets",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "1-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "1-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      // RENAMED: Test Your Knowledge -> Dive Deeper; quiz -> deep_dive
      {
        id: "1-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "3 min",
        icon: "HelpCircle",
        completed: false,
        // targetLearnUnitId intentionally left as-is if present; ignored for deep_dive
        targetLearnUnitId: 1,
      },
    ],
  },
  {
    id: 2,
    title: "Lesson 2: Combined Alphabet Letters",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "2-1",
        type: "video",
        title: "Video: Combining Arabic Letters",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "2-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "2-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "2-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "5 min",
        icon: "HelpCircle",
        completed: false,
      },
    ],
  },
  {
    id: 3,
    title: "Lesson 3: Disconnected Letters",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "3-1",
        type: "video",
        title: "Video: Understanding Disconnected Letters",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "3-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "3-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "3-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "5 min",
        icon: "HelpCircle",
        completed: false,
      },
    ],
  },
  {
    id: 4,
    title: "Lesson 4: Letters with Vowel Marks",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "4-1",
        type: "video",
        title: "Video: Introduction to Harakat",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "4-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "4-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "4-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "3 min",
        icon: "HelpCircle",
        completed: false,
        targetLearnUnitId: 100001,
      },
    ],
  },
  {
    id: 5,
    title: "Lesson 5: Letters with Tanween",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "5-1",
        type: "video",
        title: "Video: Understanding Tanween",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "5-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "5-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "5-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "3 min",
        icon: "HelpCircle",
        completed: false,
        targetLearnUnitId: 300001,
      },
    ],
  },
  {
    id: 6,
    title: "Lesson 6: Exercises on Vowel Marks and Tanween",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "6-1",
        type: "video",
        title: "Video: Practical Examples",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "6-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "6-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "6-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "3 min",
        icon: "HelpCircle",
        completed: false,
        targetLearnUnitId: 400001,
      },
    ],
  },
  {
    id: 7,
    title: "Lesson 7: Small Alif, Small Ya, and Small Waw",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "7-1",
        type: "video",
        title: "Video: Small Letters in Arabic",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "7-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "7-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "7-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "5 min",
        icon: "HelpCircle",
        completed: false,
      },
    ],
  },
  {
    id: 8,
    title: "Lesson 8: Letters of Elongation and Softening",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "8-1",
        type: "video",
        title: "Video: Madd and Leen Introduction",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "8-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "8-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "8-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "3 min",
        icon: "HelpCircle",
        completed: false,
        targetLearnUnitId: 200001,
      },
    ],
  },
  {
    id: 9,
    title: "Lesson 9: Exercises on Madd Leen",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "9-1",
        type: "video",
        title: "Video: Reviewing Madd Leen",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "9-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "9-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "9-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "5 min",
        icon: "HelpCircle",
        completed: false,
      },
    ],
  },
  {
    id: 10,
    title: "Lesson 10: Exercises on Tanween and Madd Letters",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "10-1",
        type: "video",
        title: "Video: Reviewing Tanween and Madd",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "10-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "10-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "10-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "5 min",
        icon: "HelpCircle",
        completed: false,
      },
    ],
  },
  {
    id: 11,
    title: "Lesson 11: Sukoon Introduction",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "11-1",
        type: "video",
        title: "Video: Introduction to Sukoon",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "11-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "11-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "11-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "3 min",
        icon: "HelpCircle",
        completed: false,
        targetLearnUnitId: 500001,
      },
    ],
  },
  {
    id: 12,
    title: "Lesson 12: Sukoon Exercises",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "12-1",
        type: "video",
        title: "Video: Practicing Sukoon",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "12-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "12-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "12-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "3 min",
        icon: "HelpCircle",
        completed: false,
        targetLearnUnitId: 600001,
      },
    ],
  },
  {
    id: 13,
    title: "Lesson 13: Shaddah Introduction",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "13-1",
        type: "video",
        title: "Video: Understanding Tashdeed",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "13-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "13-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "13-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "3 min",
        icon: "HelpCircle",
        completed: false,
        targetLearnUnitId: 700001,
      },
    ],
  },
  {
    id: 14,
    title: "Lesson 14: Shaddah Exercises",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "14-1",
        type: "video",
        title: "Video: Practicing Tashdeed",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "14-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "14-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "14-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "3 min",
        icon: "HelpCircle",
        completed: false,
        targetLearnUnitId: 800001,
      },
    ],
  },
  {
    id: 15,
    title: "Lesson 15: Combined Shaddah and Sukoon",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "15-1",
        type: "video",
        title: "Video: Combined Concepts",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "15-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "15-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "15-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "5 min",
        icon: "HelpCircle",
        completed: false,
      },
    ],
  },
  {
    id: 16,
    title: "Lesson 16: Double Shaddah Exercises",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "16-1",
        type: "video",
        title: "Video: Double Shaddah",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "16-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "16-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "16-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "5 min",
        icon: "HelpCircle",
        completed: false,
      },
    ],
  },
  {
    id: 17,
    title: "Lesson 17: Madd letters followed by a Shaddah",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "17-1",
        type: "video",
        title: "Video: Advanced Combinations",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "17-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "17-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "17-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "5 min",
        icon: "HelpCircle",
        completed: false,
      },
    ],
  },
  {
    id: 18,
    title: "Lesson 18: Final Review Exercises",
    icon: "BookOpen",
    expanded: false,
    completed: false,
    subsections: [
      {
        id: "18-1",
        type: "video",
        title: "Video: Comprehensive Review",
        duration: "5 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "18-crash",
        type: "video",
        title: "Crash course",
        duration: "7 min",
        icon: "Video",
        completed: false,
      },
      {
        id: "18-2",
        type: "lesson",
        title: "Practice section",
        duration: "10 min",
        icon: "BookOpen",
        completed: false,
      },
      {
        id: "18-3",
        type: "deep_dive",
        title: "Dive Deeper",
        duration: "5 min",
        icon: "HelpCircle",
        completed: false,
      },
    ],
  },
];

export default function CoursesPlayerPage({
  searchParams,
  serverCourse,
}: CoursesPlayerPageProps) {
  const { curriculumId } = searchParams;

  const [chapters, setChapters] = useState(initialChapters);
  const [activeChapterId, setActiveChapterId] = useState<number>(1);
  const [activeSubsectionId, setActiveSubsectionId] = useState<string>("1-1");
  const [activeSubsectionType, setActiveSubsectionType] =
    useState<string>("video");
  const [currentLessonTitle, setCurrentLessonTitle] = useState<string>(
    "Video: Introduction to Arabic Alphabets"
  );
  const [currentChapterData, setCurrentChapterData] =
    useState<ChapterData | null>(null);
  const [isLoadingChapter, setIsLoadingChapter] = useState<boolean>(false);
  const [isQuizLoading, setIsQuizLoading] = useState<boolean>(false);
  const [chapterError, setChapterError] = useState<string | null>(null);
  const [activeQuizTargetUnitId, setActiveQuizTargetUnitId] = useState<
    number | null | undefined
  >(null);
  const [nextBtnText, setNextBtnText] = useState("Continue to Next Lesson");
  const [nextBtnDisabled, setNextBtnDisabled] = useState(false);
  const [prevBtnText, setPrevBtnText] = useState("Previous Lesson");
  const [prevBtnDisabled, setPrevBtnDisabled] = useState(false);

  const [sidebarHovered, setSidebarHovered] = useState(false);

  // A) Add a one-time “intro open” state for the sidebar
  const [isIntroOpen, setIsIntroOpen] = useState(true);

  const fetchChapter = useCallback(async (order: number) => {
    try {
      setIsLoadingChapter(true);
      setChapterError(null);
      const timestamp = Date.now();
      const res = await fetch(`/api/chapters/${order}?t=${timestamp}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });
      if (!res.ok) {
        throw new Error(
          `Error fetching chapter with order=${order}: ${res.status}`
        );
      }
      const data = await res.json();
      console.log(
        `[fetchChapter] Fetched fresh data for order ${order}:`,
        data
      );
      return data;
    } catch (err) {
      console.error("fetchChapter error:", err);
      setChapterError(
        err instanceof Error ? err.message : "Failed to fetch chapter data"
      );
      return null;
    } finally {
      setIsLoadingChapter(false);
    }
  }, []);

  const getChapterOrderFromLessonId = useCallback(
    (chapterId: number): number => {
      return chapterId;
    },
    []
  );

  const loadChapterDataForLesson = useCallback(
    async (chapterId: number) => {
      const chapterOrder = getChapterOrderFromLessonId(chapterId);
      const fetchedChapter = await fetchChapter(chapterOrder);
      if (fetchedChapter) {
        setCurrentChapterData(fetchedChapter);
      }
    },
    [fetchChapter, getChapterOrderFromLessonId]
  );

  const findNextSubsection = useCallback(() => {
    const currentChapterIndex = chapters.findIndex(
      (c) => c.id === activeChapterId
    );
    if (currentChapterIndex === -1) return null;
    const currentChapter = chapters[currentChapterIndex];

    const currentSubsectionIndex = currentChapter.subsections.findIndex(
      (s) => s.id === activeSubsectionId
    );
    if (currentSubsectionIndex === -1) return null;

    if (currentSubsectionIndex < currentChapter.subsections.length - 1) {
      const nextSub = currentChapter.subsections[currentSubsectionIndex + 1];
      return {
        chapterId: currentChapter.id,
        subsectionId: nextSub.id,
        subsectionType: nextSub.type,
        subsectionTitle: nextSub.title,
        chapterTitle: currentChapter.title,
        targetLearnUnitId: nextSub.targetLearnUnitId,
      };
    }

    if (currentChapterIndex < chapters.length - 1) {
      const nextChapter = chapters[currentChapterIndex + 1];
      if (nextChapter && nextChapter.subsections.length > 0) {
        const nextSub = nextChapter.subsections[0];
        return {
          chapterId: nextChapter.id,
          subsectionId: nextSub.id,
          subsectionType: nextSub.type,
          subsectionTitle: nextSub.title,
          chapterTitle: nextChapter.title,
          targetLearnUnitId: nextSub.targetLearnUnitId,
        };
      }
    }
    return null;
  }, [chapters, activeChapterId, activeSubsectionId]);

  const findPreviousSubsection = useCallback(() => {
    const currentChapterIndex = chapters.findIndex(
      (c) => c.id === activeChapterId
    );
    if (currentChapterIndex === -1) return null;
    const currentChapter = chapters[currentChapterIndex];

    const currentSubsectionIndex = currentChapter.subsections.findIndex(
      (s) => s.id === activeSubsectionId
    );
    if (currentSubsectionIndex === -1) return null;

    if (currentSubsectionIndex > 0) {
      const prevSub = currentChapter.subsections[currentSubsectionIndex - 1];
      return {
        chapterId: currentChapter.id,
        subsectionId: prevSub.id,
        subsectionType: prevSub.type,
        subsectionTitle: prevSub.title,
        chapterTitle: currentChapter.title,
        targetLearnUnitId: prevSub.targetLearnUnitId,
      };
    }

    if (currentChapterIndex > 0) {
      const prevChapter = chapters[currentChapterIndex - 1];
      if (prevChapter && prevChapter.subsections.length > 0) {
        const prevSub =
          prevChapter.subsections[prevChapter.subsections.length - 1];
        return {
          chapterId: prevChapter.id,
          subsectionId: prevSub.id,
          subsectionType: prevSub.type,
          subsectionTitle: prevSub.title,
          chapterTitle: prevChapter.title,
          targetLearnUnitId: prevSub.targetLearnUnitId,
        };
      }
    }
    return null;
  }, [chapters, activeChapterId, activeSubsectionId]);

  useEffect(() => {
    const next = findNextSubsection();
    if (!next) {
      setNextBtnDisabled(true);
      setNextBtnText("Course Completed");
    } else {
      setNextBtnDisabled(false);
      setNextBtnText("Continue to Next Lesson");
    }

    const prev = findPreviousSubsection();
    if (!prev) {
      setPrevBtnDisabled(true);
    } else {
      setPrevBtnDisabled(false);
    }
  }, [
    activeChapterId,
    activeSubsectionId,
    chapters,
    findNextSubsection,
    findPreviousSubsection,
  ]);

  if (!curriculumId) {
    return (
      <main className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto flex items-center justify-center">
            <BookOpen className="h-8 w-8 text-gray-400" />
          </div>
          <p className="text-gray-600 text-lg">No curriculum selected.</p>
          <Link href="/curriculum">
            <button className="px-6 py-3 bg-[#1a1a18] text-white rounded-lg hover:bg-neutral-900 transition-all duration-200 font-medium">
              Browse Courses
            </button>
          </Link>
        </div>
      </main>
    );
  }

  const course = serverCourse;
  // This videoSource will be used for all video types, including the newly named one.
  const videoSource = course?.videoUrl || "/default-video.mp4";

  const handleToggleChapter = (chapterId: number) => {
    setChapters((prev) =>
      prev.map((ch) => {
        if (ch.id === chapterId) {
          return { ...ch, expanded: !ch.expanded };
        }
        return ch;
      })
    );
  };

  const handleSubsectionClick = async (
    chapterId: number,
    subsectionId: string,
    subsectionType: string,
    subsectionTitle: string,
    chapterTitle: string,
    targetLearnUnitId?: number
  ) => {
    setActiveChapterId(chapterId);
    setActiveSubsectionId(subsectionId);
    setActiveSubsectionType(subsectionType);
    setCurrentLessonTitle(subsectionTitle);

    if (subsectionType === "quiz") {
      setIsQuizLoading(true);
      setActiveQuizTargetUnitId(targetLearnUnitId);
      setCurrentChapterData(null);
      setChapterError(null);
    } else if (subsectionType === "lesson") {
      setIsQuizLoading(false);
      setActiveQuizTargetUnitId(null);
      await loadChapterDataForLesson(chapterId);
    } else if (subsectionType === "deep_dive") {
      // NEW: Deep Dive branch (no quiz/lesson data to fetch)
      setIsQuizLoading(false);
      setActiveQuizTargetUnitId(null);
      setCurrentChapterData(null);
      setChapterError(null);
    } else {
      // This 'else' block now handles 'video' and any other types that don't need chapter data or quiz iframe
      // Since "Introduction to Arabic Alphabets Part 2" is now 'video' type, it falls here.
      setIsQuizLoading(false);
      setActiveQuizTargetUnitId(null);
      setCurrentChapterData(null);
      setChapterError(null);
    }
  };

  const handleCollapsedChapterClick = (chapterId: number) => {
    const targetChapter = chapters.find((c) => c.id === chapterId);
    if (targetChapter && targetChapter.subsections.length > 0) {
      const firstSubsection = targetChapter.subsections[0];
      navigateToSubsection(
        targetChapter.id,
        firstSubsection.id,
        firstSubsection.type,
        firstSubsection.title,
        targetChapter.title,
        firstSubsection.targetLearnUnitId
      );
    }
  };

  const navigateToSubsection = async (
    targetChapterId: number,
    targetSubsectionId: string,
    targetSubsectionType: string,
    targetSubsectionTitle: string,
    targetChapterTitle: string,
    targetLearnUnitId?: number
  ) => {
    setChapters((prev) =>
      prev.map((ch) => {
        if (ch.id === targetChapterId) {
          return { ...ch, expanded: true };
        }
        return ch;
      })
    );

    setTimeout(() => {
      const subsectionElement = document.querySelector(
        `[data-subsection-id="${targetSubsectionId}"]`
      ) as HTMLElement | null;
      const sidebar = document.getElementById("course-sidebar");
      if (subsectionElement && sidebar) {
        const sidebarRect = sidebar.getBoundingClientRect();
        const elementRect = subsectionElement.getBoundingClientRect();
        const offset =
          elementRect.top - sidebarRect.top - sidebar.clientHeight / 4;
        sidebar.scrollTop += offset;
      }
    }, 100);

    await handleSubsectionClick(
      targetChapterId,
      targetSubsectionId,
      targetSubsectionType,
      targetSubsectionTitle,
      targetChapterTitle,
      targetLearnUnitId
    );
  };

  const goToNextSubsection = async () => {
    const next = findNextSubsection();
    if (!next) {
      return;
    }
    await navigateToSubsection(
      next.chapterId,
      next.subsectionId,
      next.subsectionType,
      next.subsectionTitle,
      next.chapterTitle,
      next.targetLearnUnitId
    );
  };

  const goToPreviousSubsection = async () => {
    const prev = findPreviousSubsection();
    if (!prev) {
      return;
    }
    await navigateToSubsection(
      prev.chapterId,
      prev.subsectionId,
      prev.subsectionType,
      prev.subsectionTitle,
      prev.chapterTitle,
      prev.targetLearnUnitId
    );
  };

  // C) Add a click-away handler on the main content side
  const handleClickAwayFromSidebar = () => {
    if (isIntroOpen) {
      setIsIntroOpen(false);
    }
  };

  const renderLessonContent = () => {
    // Branching logic ensures ChapterPage unmounts when switching to non-lesson types
    if (activeSubsectionType === "video") {
      return (
        <>
          <video
            src={videoSource}
            controls
            className="w-full h-full object-contain"
            autoPlay
            playsInline
          >
            Your browser does not support the video tag.
          </video>
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-0 hover:opacity-100 transition-opacity duration-300">
            <div className="bg-[#1a1a18]/20 rounded-full p-6 backdrop-blur-sm">
              <Play className="h-16 w-16 text-white" />
            </div>
          </div>
        </>
      );
    }

    if (activeSubsectionType === "extra_practice") {
      return (
        <div className="w-full h-full flex items-center justify-center p-8">
          <div className="text-center space-y-4">
            <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto flex items-center justify-center mb-6">
              <ClipboardEdit className="h-10 w-10 text-gray-500" />
            </div>
            <h3 className="text-2xl font-bold text-[#1a1a18]">
              {currentLessonTitle}
            </h3>
            <p className="text-gray-600 max-w-md mx-auto">
              This section is ready for you to build. Interactive practice
              content will be displayed here.
            </p>
          </div>
        </div>
      );
    }

    if (activeSubsectionType === "lesson") {
      if (isLoadingChapter) {
        return (
          <div className="w-full h-full flex items-center justify-center p-8">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a1a18] mx-auto"></div>
              <p className="text-gray-700">Loading lesson content...</p>
            </div>
          </div>
        );
      }

      if (chapterError) {
        return (
          <div className="w-full h-full flex items-center justify-center p-8">
            <div className="text-center space-y-4">
              <div className="w-20 h-20 bg-red-100 rounded-full mx-auto flex items-center justify-center mb-6">
                <BookOpen className="h-10 w-10 text-red-500" />
              </div>
              <h3 className="text-2xl font-bold text-red-700">
                Error Loading Lesson
              </h3>
              <p className="text-gray-600 max-w-md mx-auto">{chapterError}</p>
              <button
                onClick={() => loadChapterDataForLesson(activeChapterId)}
                className="px-4 py-2 bg-[#1a1a18] text-white hover:bg-neutral-900 rounded-lg transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        );
      }

      if (currentChapterData) {
        const activeChapterForContext = chapters.find(
          (c) => c.id === activeChapterId
        );

        const lessonContext = activeChapterForContext
          ? {
              chapter: activeChapterId,
              chapterTitle: activeChapterForContext.title,
              subsection: activeSubsectionId,
              subsectionTitle: currentLessonTitle,
              lessonType: activeSubsectionType,
            }
          : undefined;

        return (
          <div
            className="w-full h-full"
            style={{
              transform: "scale(0.85)",
              transformOrigin: "top left",
              width: "117.6%",
              height: "117.6%",
            }}
          >
            {/*
              By providing a unique key that includes the activeChapterId, we ensure
              that React unmounts the old ChapterPage and mounts a new one whenever the
              lesson changes. This triggers the audio preload logic correctly.
            */}
            <UstadTutor
              key={`ustad-tutor-lesson-${activeChapterId}`}
              chapterData={currentChapterData}
              showHeader={false}
              restrictNavigation={true}
              lessonContext={lessonContext}
            />
          </div>
        );
      }

      return (
        <div className="w-full h-full flex items-center justify-center p-8">
          <div className="text-center space-y-4">
            <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto flex items-center justify-center mb-6">
              <BookOpen className="h-10 w-10 text-gray-500" />
            </div>
            <h3 className="text-2xl font-bold text-[#1a1a18]">
              No Lesson Content Available
            </h3>
            <p className="text-gray-600 max-w-md mx-auto">
              This lesson content is not yet available. Please try another
              lesson or contact support.
            </p>
          </div>
        </div>
      );
    }

    // NEW: Deep Dive view
    if (activeSubsectionType === "deep_dive") {
      return (
        <div className="w-full h-full bg-white">
          <DeepDive lessonNumber={activeChapterId} />
        </div>
      );
    }

    if (activeSubsectionType === "quiz") {
      if (!curriculumId) {
        return (
          <div className="w-full h-full flex items-center justify-center p-4">
            <div className="text-center space-y-3">
              <HelpCircle className="h-12 w-12 text-yellow-500 mx-auto" />
              <h3 className="text-xl font-semibold text-[#1a1a18]">
                Quiz Unavailable
              </h3>
              <p className="text-gray-600">
                Curriculum ID is missing. Cannot load the quiz.
              </p>
            </div>
          </div>
        );
      }

      if (activeQuizTargetUnitId) {
        const learnExerciseUrl = `/learn?curriculumId=${encodeURIComponent(
          curriculumId
        )}&targetUnitId=${activeQuizTargetUnitId}&embedded=true`;
        console.log(
          `[CoursesPlayerPage] Loading quiz iframe with URL: ${learnExerciseUrl}`
        );
        return (
          <div className="w-full h-full relative">
            {isQuizLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
                <div className="text-center space-y-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a1a18] mx-auto"></div>
                  <p className="text-gray-700">Loading quiz...</p>
                </div>
              </div>
            )}
            <iframe
              src={learnExerciseUrl}
              title={currentLessonTitle || "Learn Exercise"}
              className={`w-full h-full border-0 rounded-2xl transition-opacity duration-300 ${
                isQuizLoading ? "opacity-0" : "opacity-100"
              }`}
              allowFullScreen
              onLoad={() => setIsQuizLoading(false)}
            >
              <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-700 p-4 rounded-2xl">
                <p>
                  Your browser does not support iframes. Please update your
                  browser or use a different one to access the quiz.
                </p>
              </div>
            </iframe>
          </div>
        );
      } else {
        return (
          <div className="w-full h-full flex items-center justify-center p-8">
            <div className="text-center space-y-4">
              <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto flex items-center justify-center mb-6">
                <HelpCircle className="h-10 w-10 text-gray-500" />
              </div>
              <h3 className="text-2xl font-bold text-[#1a1a18]">
                {currentLessonTitle}
              </h3>
              <p className="text-gray-600 max-w-md mx-auto">
                This quiz section is currently under development or does not
                have specific linked exercises.
              </p>
            </div>
          </div>
        );
      }
    }

    return (
      <div className="w-full h-full flex items-center justify-center p-8">
        <div className="text-center space-y-4">
          <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto flex items-center justify-center mb-6">
            <BookOpen className="h-10 w-10 text-gray-500" />
          </div>
          <h3 className="text-2xl font-bold text-[#1a1a18]">
            {currentLessonTitle.replace(/^(Video: |Lesson: |Quiz: )/, "")}
          </h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Interactive content will be displayed here.
          </p>
        </div>
      </div>
    );
  };

  const isCollapsibleSectionActive = [
    "lesson",
    "video",
    "quiz",
    "deep_dive",
  ].includes(activeSubsectionType);
  // B) Update the collapse logic to respect “intro open”
  const isSidebarCollapsed =
    !isIntroOpen && isCollapsibleSectionActive && !sidebarHovered;

  return (
    <>
      <main
        className="h-screen flex flex-col overflow-hidden"
        style={{
          backgroundColor: "#ffffff",
          backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
          backgroundRepeat: "repeat",
        }}
      >
        {/* --- MODIFIED HEADER --- */}
        <div className="bg-white px-6 py-4 border-b border-gray-200 flex items-center shadow-sm">
          {/* Left-side content (unchanged) */}
          <div className="flex items-center space-x-4">
            <Link href="/curriculum">
              <button className="p-2 rounded-xl hover:bg-gray-100 transition-all duration-200 group">
                <ArrowLeft className="h-5 w-5 text-gray-600 group-hover:text-[#1a1a18]" />
              </button>
            </Link>
            <div className="h-6 w-px bg-gray-300"></div>
            <div>
              <h1 className="text-lg font-bold text-[#1a1a18]">
                {course?.curriculum?.name || course?.englishTitle || "Course"}
              </h1>
              <p className="text-sm text-gray-500 mt-0.5">
                Introduction to Quranic Arabic
              </p>
            </div>
          </div>

          {/* This spacer div will grow to push the progress bar to the right */}
          <div className="flex-grow" />

          {/* Progress bar content with a right margin to pull it from the edge */}
          <div className="flex items-center space-x-4 mr-12">
            <div className="text-right">
              <div className="text-base font-medium text-[#1a1a18]">
                Progress
              </div>
              <div className="text-sm text-gray-500 proportional-nums">
                Lesson {activeChapterId} of {chapters.length}
              </div>
            </div>
            <div className="w-24 h-3 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-emerald-300 transition-all duration-300"
                style={{
                  width: `${(activeChapterId / chapters.length) * 100}%`,
                }}
              ></div>
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-hidden">
          {/* --- FIX APPLIED HERE --- */}
          {/* Changed p-8 to px-8 py-4 to reduce vertical padding */}
          <div className="flex gap-8 h-full px-8 py-4">
            <aside
              id="course-sidebar"
              className={`${
                isSidebarCollapsed ? "w-16" : "w-[420px]"
              } flex-shrink-0 border border-[#eeedf2] bg-white overflow-hidden rounded-2xl shadow-sm transition-all duration-300 ease-in-out`}
              onMouseEnter={() => setSidebarHovered(true)}
              onMouseLeave={() => setSidebarHovered(false)}
            >
              {isSidebarCollapsed ? (
                <div className="flex flex-col items-center py-4 space-y-2 overflow-y-auto h-full">
                  {chapters.map((chapter) => (
                    <div
                      key={`collapsed-${chapter.id}`}
                      onClick={() => handleCollapsedChapterClick(chapter.id)}
                      className={`
                        w-10 h-10 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-200
                        ${
                          activeChapterId === chapter.id
                            ? "bg-[#1a1a18] text-white ring-2 ring-offset-2 ring-[#1a1a18]"
                            : "bg-[#eeedf2] text-gray-700 hover:bg-[#eeedf2]"
                        }
                      `}
                      title={chapter.title}
                    >
                      <span className="text-base font-[510] tabular-nums">
                        {" "}
                        {chapter.id}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <>
                  {/* --- ENHANCED: Course Content Header --- */}
                  <div
                    className="px-8 py-6 border-b border-gray-100 bg-gradient-to-b from-gray-50 to-white rounded-t-2xl"
                    style={{
                      scrollbarWidth: "thin",
                      scrollbarColor: "#d1d5db #f9fafb",
                    }}
                  >
                    <h2 className="text-2xl font-bold text-gray-900 mb-2 whitespace-nowrap">
                      Course Content
                    </h2>
                    <p className="text-sm text-gray-600 whitespace-nowrap proportional-nums">
                      {chapters.length} lessons • 4.5 hours total
                    </p>
                  </div>
                  {/* --- ENHANCED: Sidebar padding changed to px-6 --- */}
                  <div className="px-6 py-3 overflow-y-auto h-[calc(100%-116px)]">
                    {chapters.map((chapter) => (
                      <div key={chapter.id} className="mt-2 first:mt-0">
                        {/* --- ENHANCED: Chapter Items --- */}
                        <div
                          className={`
                            px-6 py-5 cursor-pointer flex justify-between items-center group rounded-xl mx-2
                            transition-all duration-200 ease-out
                            ${
                              activeChapterId === chapter.id
                                ? "bg-[#1a1a18] text-white"
                                : "hover:bg-gray-100 hover:shadow-sm hover:-translate-y-px"
                            }`}
                          onClick={() => handleToggleChapter(chapter.id)}
                        >
                          <div className="flex items-center space-x-3 flex-1">
                            {/* --- ENHANCED: Chapter Number Badge --- */}
                            <div
                              className={`
                                w-10 h-10 rounded-xl flex items-center justify-center
                                transition-transform duration-300 origin-center ease-out delay-200 group-hover:rotate-45
                                ${
                                  activeChapterId === chapter.id
                                    ? "bg-white/20"
                                    : "bg-gray-100 group-hover:bg-[#1a1a18]"
                                }`}
                            >
                              <span
                                className={`
                                  text-sm font-semibold tabular-nums
                                  transition-transform duration-300 origin-center ease-out delay-200 group-hover:-rotate-45
                                  ${
                                    activeChapterId === chapter.id
                                      ? "text-white"
                                      : "text-gray-700 group-hover:text-white"
                                  }`}
                              >
                                {chapter.id}
                              </span>
                            </div>
                            <div className="flex-1">
                              <span
                                className={`font-semibold text-sm block leading-tight proportional-nums ${
                                  activeChapterId === chapter.id
                                    ? "text-white"
                                    : "text-[#1a1a18]"
                                }`}
                              >
                                {chapter.title}
                              </span>
                            </div>
                          </div>
                          <ChevronDown
                            className={`h-5 w-5 transition-all duration-200 ${
                              chapter.expanded ? "transform rotate-180" : ""
                            } ${
                              activeChapterId === chapter.id
                                ? "text-gray-300"
                                : "text-gray-400 group-hover:text-gray-600"
                            }`}
                          />
                        </div>

                        <div
                          className={`
                            pl-6 pr-4 bg-white
                            transition-all duration-500 ease-in-out overflow-hidden
                            ${
                              chapter.expanded
                                ? "max-h-96 opacity-100 pb-2"
                                : "max-h-0 opacity-0"
                            }`}
                        >
                          {/* --- ENHANCED: Subsection spacing container --- */}
                          <div className="space-y-2 py-2">
                            {chapter.subsections.map((subsection) => (
                              <div
                                key={subsection.id}
                                // --- ENHANCED: Subsection Items ---
                                className={`
                                  py-4 px-4 cursor-pointer flex items-center rounded-xl group
                                  transition-all duration-200 ease-out
                                  ${
                                    activeSubsectionId === subsection.id
                                      ? "bg-neutral-300 text-[#1a1a18] hover:bg-neutral-400"
                                      : "hover:bg-gray-100 hover:-translate-y-px"
                                  }`}
                                data-subsection-id={subsection.id}
                                onClick={() =>
                                  handleSubsectionClick(
                                    chapter.id,
                                    subsection.id,
                                    subsection.type,
                                    subsection.title,
                                    chapter.title,
                                    subsection.targetLearnUnitId
                                  )
                                }
                              >
                                <div className="mr-3 flex items-center justify-center">
                                  {/* --- ENHANCED: Icon size --- */}
                                  {subsection.type === "video" && (
                                    <PlayCircle
                                      className={`h-5 w-5 ${
                                        activeSubsectionId === subsection.id
                                          ? "text-gray-700"
                                          : "text-gray-500"
                                      }`}
                                    />
                                  )}
                                  {subsection.type === "lesson" && (
                                    <BookOpen
                                      className={`h-5 w-5 ${
                                        activeSubsectionId === subsection.id
                                          ? "text-gray-700"
                                          : "text-gray-500"
                                      }`}
                                    />
                                  )}
                                  {subsection.type === "quiz" && (
                                    <HelpCircle
                                      className={`h-5 w-5 ${
                                        activeSubsectionId === subsection.id
                                          ? "text-gray-700"
                                          : "text-gray-500"
                                      }`}
                                    />
                                  )}
                                  {subsection.type === "extra_practice" && (
                                    <ClipboardEdit
                                      className={`h-5 w-5 ${
                                        activeSubsectionId === subsection.id
                                          ? "text-gray-700"
                                          : "text-gray-500"
                                      }`}
                                    />
                                  )}
                                  {/* NEW: Deep Dive icon */}
                                  {subsection.type === "deep_dive" && (
                                    <HelpCircle
                                      className={`h-5 w-5 ${
                                        activeSubsectionId === subsection.id
                                          ? "text-gray-700"
                                          : "text-gray-500"
                                      }`}
                                    />
                                  )}
                                </div>
                                <div className="flex-1 pr-2">
                                  <p
                                    className={`text-sm font-medium leading-tight proportional-nums ${
                                      activeSubsectionId === subsection.id
                                        ? "text-[#1a1a18]"
                                        : "text-[#1a1a18] group-hover:text-[#1a1a18]"
                                    }`}
                                  >
                                    {subsection.title
                                      .replace("Video: ", "")
                                      .replace("Lesson: ", "")
                                      .replace("Quiz: ", "")}
                                  </p>
                                  <div className="flex items-center mt-1 space-x-2">
                                    <p
                                      className={`text-xs proportional-nums ${
                                        activeSubsectionId === subsection.id
                                          ? "text-gray-600"
                                          : "text-gray-500"
                                      }`}
                                    >
                                      {subsection.duration}
                                    </p>
                                    {subsection.completed && (
                                      <CheckCircle className="h-3 w-3 text-green-500" />
                                    )}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </aside>

            <div
              className="flex-1 min-w-0 bg-white rounded-2xl overflow-hidden shadow-lg flex flex-col"
              onClick={handleClickAwayFromSidebar}
            >
              <div
                className={`flex-1 relative rounded-2xl overflow-hidden transition-colors duration-300 ${
                  activeSubsectionType === "video"
                    ? "bg-[#1a1a18]"
                    : "bg-gray-50"
                }`}
              >
                {renderLessonContent()}
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}

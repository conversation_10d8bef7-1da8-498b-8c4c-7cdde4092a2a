// challenge.tsx

import { useEffect } from "react";
import Image from "next/image"; // ESLINT FIX: Import the next/image component
import { challengeOptions } from "@/db/schema";
import { ImageAudioSelectComponent } from "./ImageAudioSelect";
import { Select } from "./Select";
import { Assist } from "./Assist";
import { DragAndDrop } from "./DragAndDrop";
import { FillInTheBlank } from "./FillInTheBlank";
import { SpeakThis } from "./speakThis";
import { Card } from "./card";
import SpeakThisAdvanced from "./advancedChallenges/speakThisAdvanced";
// NEW: Speak Sequence (continuous chunks -> next letter/word)
import SpeakSequence from "./advancedChallenges/SpeakSequence";

type ChallengeType =
  | "SELECT"
  | "ASSIST"
  | "MATCHING"
  | "TAP_WHAT_YOU_HEAR"
  | "IMAGE_AUDIO_SELECT"
  | "DRAG_AND_DROP"
  | "SPEAK_THIS"
  | "FILL_IN_THE_BLANK"
  | "SPEAK_THIS_ADVANCED"
  | "EXPLAINER"
  // NEW: continuous audio challenge
  | "SPEAK_SEQUENCE";

type Props = {
  options: (typeof challengeOptions.$inferSelect)[];
  onSelect: (
    id: number | null,
    side: "left" | "right" | "drag-and-drop" | "fill-in-the-blank"
  ) => void;
  status: "correct" | "wrong" | "none" | "submitting";
  selectedOption: number | null;
  selectedPairOption?: number | null;
  disabled?: boolean;
  type: ChallengeType;
  mediaType?: string | null;
  mediaUrl?: string | null;
  audioSrc?: string | null;
  question: string;
  sentence?: string | null;
  // TS FIX: Add the callback prop to be passed down
  onSpeakThisAdvancedResult?: (isCorrect: boolean) => void;

  // NEW: optional completion callback for Speak Sequence
  // Front-end parent (quiz) can use this to call the server action to finalize scoring.
  onSpeakSequenceComplete?: (payload: {
    complete: boolean;
    matchedOptionIds?: number[]; // the 10 matched ids (or fewer in dev)
  }) => void;
};

export const Challenge = ({
  options,
  onSelect,
  status,
  selectedOption,
  selectedPairOption,
  disabled = false,
  type,
  mediaType,
  mediaUrl,
  audioSrc,
  question,
  sentence,
  onSpeakThisAdvancedResult, // TS FIX: Destructure the new prop
  onSpeakSequenceComplete, // NEW: Speak Sequence completion callback
}: Props) => {
  const handleClick = (id: number) => {
    console.log("Option clicked:", id);
    onSelect(id, "left");
  };

  useEffect(() => {
    if (type === "TAP_WHAT_YOU_HEAR" && options[0]?.audioSrc) {
      const audio = new Audio(options[0].audioSrc);
      audio.play();

      return () => {
        audio.pause();
        audio.currentTime = 0;
      };
    }
  }, [type, options]);

  const renderComponent = () => {
    if (type === "SELECT") {
      return (
        <Select
          options={options}
          onSelect={onSelect}
          status={status}
          selectedOption={selectedOption}
          disabled={disabled}
          type={type}
        />
      );
    } else if (type === "ASSIST") {
      return (
        <Assist
          options={options}
          onSelect={onSelect}
          status={status}
          selectedOption={selectedOption}
          disabled={disabled}
          type={type}
          question={question}
          audioSrc={audioSrc}
          sentence={sentence}
        />
      );
    } else if (type === "DRAG_AND_DROP") {
      return (
        <DragAndDrop
          options={options}
          onSelect={(result) => {
            if (Array.isArray(result)) {
              onSelect(null, "drag-and-drop"); // Clear previous selections
              result.forEach((id) => onSelect(id, "drag-and-drop"));
            }
          }}
          status={status}
          disabled={disabled}
          question={{ audioSrc: audioSrc || "", text: question }}
          type={type}
        />
      );
    } else if (type === "IMAGE_AUDIO_SELECT") {
      return (
        <ImageAudioSelectComponent
          options={options}
          onSelect={(id) => onSelect(id, "left")}
          status={status}
          selectedOption={selectedOption}
          disabled={disabled}
          type={type}
          question={{ audioSrc: audioSrc || "", text: question }}
          mediaType={mediaType}
          mediaUrl={mediaUrl}
        />
      );
    } else if (type === "FILL_IN_THE_BLANK") {
      return (
        <FillInTheBlank
          options={options}
          onSelect={(id) => onSelect(id, "fill-in-the-blank")}
          status={status}
          selectedOptions={selectedOption ? [selectedOption] : []}
          disabled={disabled}
          sentence={sentence || ""}
          question={question}
          mediaUrl={mediaUrl}
          mediaType={mediaType}
        />
      );
    } else if (type === "SPEAK_THIS") {
      return (
        <SpeakThis
          options={options}
          onSelect={onSelect}
          status={status}
          selectedOption={selectedOption}
          disabled={disabled}
          type={type}
          question={question}
          audioSrc={audioSrc || ""}
          sentence={sentence || ""}
          mediaType={mediaType}
          mediaUrl={mediaUrl}
        />
      );
    } else if (type === "SPEAK_THIS_ADVANCED") {
      // TS FIX: Check if the required callback exists and pass only the correct props.
      if (!onSpeakThisAdvancedResult) {
        console.error(
          "onSpeakThisAdvancedResult is required for SPEAK_THIS_ADVANCED challenges but was not provided."
        );
        return <div>Error: Challenge configuration is invalid.</div>;
      }
      return (
        <SpeakThisAdvanced
          // --- Already existing props ---
          question={question}
          audioSrc={audioSrc || ""}
          sentence={sentence || ""}
          onSpeakThisAdvancedResult={onSpeakThisAdvancedResult}
          // --- ADD these missing props to match the old interface ---
          options={options}
          onSelect={onSelect}
          status={status}
          selectedOption={selectedOption}
          type="SPEAK_THIS_ADVANCED"
          disabled={disabled}
          mediaType={mediaType}
          mediaUrl={mediaUrl}
        />
      );
    } else if (type === "SPEAK_SEQUENCE") {
      // NEW: Continuous “Speak Sequence” flow
      return (
        <SpeakSequence
          // Core data
          options={options} // expected 10 items (letters/words)
          question={question}
          sentence={sentence || ""}
          audioSrc={audioSrc || ""}
          mediaType={mediaType}
          mediaUrl={mediaUrl}
          // Status + control
          status={status}
          disabled={disabled}
          // Interop with quiz parent & server action
          onComplete={(matchedOptionIds?: number[]) => {
            // Let parent decide when to call server (`updateSpeakSequenceProgress`)
            // We pass up the matched ids so the parent can finalize grading & advance.
            onSpeakSequenceComplete?.({
              complete: true,
              matchedOptionIds,
            });
          }}
        />
      );
    } else if (type === "EXPLAINER") {
      return (
        <div
          className="w-full flex flex-col items-center gap-6 rounded-xl border border-neutral-200 p-6 bg-white"
          aria-live="polite"
          aria-label="Lesson explainer"
        >
          {mediaUrl && mediaType === "image" && (
            // ESLINT FIX: Replaced <img> with <Image> for optimization
            <Image
              src={mediaUrl}
              alt={question || "Explainer image"}
              width={500}
              height={500}
              className="max-h-72 w-auto object-contain rounded-lg"
            />
          )}

          {audioSrc && (
            <audio
              controls
              preload="none"
              className="w-full max-w-md"
              aria-label="Explainer audio"
            >
              <source src={audioSrc} />
              Your browser does not support the audio element.
            </audio>
          )}

          <p className="text-lg lg:text-xl text-neutral-800 text-center whitespace-pre-wrap">
            {question}
          </p>

          {sentence && (
            <p className="text-base lg:text-lg text-neutral-600 text-center whitespace-pre-wrap">
              {sentence}
            </p>
          )}
        </div>
      );
    }

    console.warn("Unsupported challenge type:", type);
    return null;
  };

  return (
    <div>
      {renderComponent()}
      {type !== "SELECT" &&
        type !== "ASSIST" &&
        type !== "DRAG_AND_DROP" &&
        type !== "FILL_IN_THE_BLANK" &&
        type !== "SPEAK_THIS" &&
        type !== "SPEAK_THIS_ADVANCED" &&
        type !== "EXPLAINER" &&
        // NEW: avoid rendering cards for speak-sequence UI (it drives its own flow)
        type !== "SPEAK_SEQUENCE" &&
        options.map((option) => (
          <Card
            key={option.id}
            id={option.id}
            text={option.text}
            imageSrc={option.imageSrc}
            audioSrc={option.audioSrc}
            onClick={() => handleClick(option.id)}
            selected={
              selectedOption === option.id || selectedPairOption === option.id
            }
            status={status}
            disabled={disabled}
            type={type}
          />
        ))}
    </div>
  );
};

// explainer.tsx

import { memo } from "react";
import { AudioButton } from "./AudioButton";

type Props = {
  // parity with other challenge components (some props may be unused here)
  options?: {
    id: number;
    text: string;
    imageSrc?: string | null;
    audioSrc?: string | null;
    correct?: boolean;
  }[];
  onSelect?: (id: number | null) => void;
  selectedOption?: number | null;
  status?: "correct" | "wrong" | "none" | "submitting";
  disabled?: boolean;

  // primary explainer payload
  question: string;
  sentence?: string | null;
  mediaUrl?: string | null;
  mediaType?: string | null; // "image" | "video" | "audio" (defaults treated as image)
  audioSrc?: string | null; // optional narration
  topCardText?: string | null; // optional supporting text above media
  topCardAudio?: string | null; // optional audio for the supporting text
  type?: string;
};

function ExplainerComponent({
  question,
  sentence,
  mediaUrl,
  mediaType = "image",
  audioSrc,
  topCardText,
  topCardAudio,
}: Props) {
  return (
    <div className="w-full flex flex-col gap-6 items-center">
      {/* Optional narration button */}
      {audioSrc ? (
        <div className="w-full flex justify-center">
          <AudioButton src={audioSrc} />
        </div>
      ) : null}

      {/* Main statement */}
      <h1 className="text-xl lg:text-2xl font-bold text-neutral-700 text-center lg:text-start w-full">
        {question}
      </h1>

      {/* Optional supporting line */}
      {sentence ? (
        <p className="text-base lg:text-lg text-neutral-600 text-center lg:text-start w-full whitespace-pre-wrap">
          {sentence}
        </p>
      ) : null}

      {/* Optional top helper card content */}
      {topCardText ? (
        <div className="w-full max-w-3xl text-neutral-700 bg-neutral-50 border border-neutral-200 rounded-xl p-4 shadow-sm">
          <p className="text-center lg:text-start">{topCardText}</p>
          {topCardAudio ? (
            <audio
              controls
              preload="none"
              className="w-full mt-3"
              aria-label="Explainer helper audio"
            >
              <source src={topCardAudio} />
              Your browser does not support the audio element.
            </audio>
          ) : null}
        </div>
      ) : null}

      {/* Media area */}
      <div className="w-full flex items-center justify-center">
        {mediaUrl && mediaType === "image" && (
          // eslint-disable-next-line @next/next/no-img-element
          <img
            src={mediaUrl}
            alt={question}
            className="w-full max-w-4xl max-h-[560px] object-contain rounded-xl border border-neutral-200 shadow-sm"
          />
        )}

        {mediaUrl && mediaType === "video" && (
          <video
            controls
            className="w-full max-w-4xl max-h-[560px] rounded-xl border border-neutral-200 shadow-sm"
          >
            <source src={mediaUrl} />
            Your browser does not support the video tag.
          </video>
        )}

        {mediaUrl && mediaType === "audio" && (
          <audio
            controls
            className="w-full max-w-2xl"
            aria-label="Explainer audio"
          >
            <source src={mediaUrl} />
            Your browser does not support the audio element.
          </audio>
        )}

        {!mediaUrl && (
          <div className="w-full max-w-4xl h-64 md:h-80 rounded-xl border-2 border-dashed border-neutral-200 bg-neutral-50 flex items-center justify-center text-neutral-400">
            No media provided
          </div>
        )}
      </div>
    </div>
  );
}

const Explainer = memo(ExplainerComponent);

export { Explainer };
export default Explainer;

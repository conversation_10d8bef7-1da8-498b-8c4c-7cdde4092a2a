/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  memo,
} from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/shadcn-ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Check,
  X,
  RotateCcw,
  Volume2,
  PauseCircle,
  Play,
  Flame,
} from "lucide-react";

// ===== Types =====
export type SpeedItemKind = "spot_mistake" | "match_audio";

export type SpeedItem = {
  id: number;
  challengeId: number;
  kind: SpeedItemKind;
  audioSrc: string;
  audioText?: string | null;
  displayText?: string | null;
  isMatch?: boolean | null;
  sequence?: number | null;
  active?: boolean | null;
};

export type RoundItem = {
  item: SpeedItem;
};

type AttemptPayload = {
  sessionId?: number | null;
  speedItemId: number;
  chosen: "match" | "mismatch";
  isCorrect: boolean;
  latencyMs: number;
};

type EndSummary = {
  sessionId?: number | null;
  challengeId: number;
  durationMs: number;
  score: number;
  itemsSeen: number;
  itemsCorrect: number;
  streakMax: number;
  avgLatencyMs: number | null;
};

// NEW: Track incorrect answers for the scoreboard
type WrongAttempt = {
  speedItemId: number;
  displayText?: string | null;
  audioText?: string | null;
  audioSrc: string;
  correctAnswer: "match" | "mismatch";
  chosen: "match" | "mismatch";
  latencyMs: number;
  isMatch?: boolean | null;
};

// ===== Helpers =====
const clamp = (n: number, min: number, max: number) =>
  Math.max(min, Math.min(max, n));
const fmtTime = (s: number) => {
  const m = Math.floor(s / 60);
  const r = s % 60;
  return `${m}:${r < 10 ? "0" + r : r}`;
};
const isTouchDevice = () =>
  typeof window !== "undefined" &&
  ("ontouchstart" in window || navigator.maxTouchPoints > 0);

// ===== API (best-effort; backend optional) =====
async function apiFetchItems(
  challengeId: number,
  limit: number,
  kind: SpeedItemKind = "spot_mistake"
): Promise<RoundItem[]> {
  try {
    const res = await fetch(
      `/api/speed-round/items?challengeId=${challengeId}&limit=${limit}&kind=${kind}`,
      { method: "GET", cache: "no-store" }
    );
    if (!res.ok) throw new Error(`HTTP ${res.status}`);

    const raw = await res.json();
    const rows = Array.isArray(raw?.items) ? raw.items : [];

    // Normalize both shapes:
    // 1) { items: [{ item: {...}, options: [] }, ...] }
    // 2) { items: [ SpeedItem, ... ] }
    const normalized: RoundItem[] = rows.map((entry: any) => {
      if (entry?.item) {
        // already RoundItem-like
        return { item: entry.item as SpeedItem };
      }
      // bare SpeedItem
      return { item: entry as SpeedItem };
    });

    return normalized;
  } catch {
    return [];
  }
}

async function apiStartSession(challengeId: number): Promise<number | null> {
  try {
    const res = await fetch(`/api/speed-round/session/start`, {
      method: "POST",
      body: JSON.stringify({ challengeId }),
      headers: { "Content-Type": "application/json" },
    });
    if (!res.ok) return null;
    const data = await res.json();
    return data?.sessionId ?? null;
  } catch {
    return null;
  }
}

async function apiLogAttempt(p: AttemptPayload) {
  try {
    await fetch(`/api/speed-round/attempt`, {
      method: "POST",
      body: JSON.stringify(p),
      headers: { "Content-Type": "application/json" },
    });
  } catch {
    // ignore
  }
}

// NEW: include optional mistakes payload for analytics
async function apiEndSession(
  summary: EndSummary & { deviceType?: string; mistakes?: WrongAttempt[] }
) {
  try {
    await fetch(`/api/speed-round/session/end`, {
      method: "POST",
      body: JSON.stringify(summary),
      headers: { "Content-Type": "application/json" },
    });
  } catch {
    // ignore
  }
}

// ===== Component =====
type Props = {
  challengeId: number;
  timeLimitSeconds?: number; // default 60
  itemCount?: number; // default 20
  initialItems?: RoundItem[];
  onEnd?: (summary: EndSummary) => void;
  className?: string;
};

const preloadAhead = 3;

const SpeedSpotMistake: React.FC<Props> = memo(
  ({
    challengeId,
    timeLimitSeconds = 60,
    itemCount = 20,
    initialItems,
    onEnd,
    className,
  }) => {
    // --- load state ---
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [items, setItems] = useState<RoundItem[]>([]);

    // --- session state ---
    const [sessionId, setSessionId] = useState<number | null>(null);
    const [started, setStarted] = useState(false);
    const [finished, setFinished] = useState(false);
    const [showSummary, setShowSummary] = useState(false);

    // --- progress, timer, stats ---
    const [idx, setIdx] = useState(0);
    const [secondsLeft, setSecondsLeft] = useState(timeLimitSeconds);
    const [score, setScore] = useState(0);
    const [itemsSeen, setItemsSeen] = useState(0);
    const [itemsCorrect, setItemsCorrect] = useState(0);
    const [streak, setStreak] = useState(0);
    const [streakMax, setStreakMax] = useState(0);
    const [answerLock, setAnswerLock] = useState(false);
    const [lastAnswer, setLastAnswer] = useState<"match" | "mismatch" | null>(
      null
    );
    const [latencies, setLatencies] = useState<number[]>([]);

    // NEW: store wrong attempts for the scoreboard
    const [wrongAttempts, setWrongAttempts] = useState<WrongAttempt[]>([]);

    const timerRef = useRef<number | null>(null);
    const roundStartMsRef = useRef<number>(0);

    // --- audio ---
    const audioRef = useRef<HTMLAudioElement | null>(null);
    const [audioReady, setAudioReady] = useState(false);
    const [audioPlaying, setAudioPlaying] = useState(false);
    const [audioError, setAudioError] = useState<string | null>(null);
    const audioStartStampRef = useRef<number>(0);
    const replayCooldownRef = useRef<number>(0);

    // --- gestures ---
    const dragStartX = useRef<number | null>(null);
    const dragDx = useRef<number>(0);
    const dragThreshold = 60; // px

    // derived / current item
    const current = items[idx]?.item ?? null;
    const total = items.length;

    // device type (best-effort)
    const deviceType = useMemo(
      () => (isTouchDevice() ? "touch" : "desktop"),
      []
    );

    // shuffle util
    const shuffle = useCallback<<T>(arr: T[]) => T[]>((arr) => {
      const a = arr.slice();
      for (let i = a.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [a[i], a[j]] = [a[j], a[i]];
      }
      return a;
    }, []);

    // ==== Data boot ====
    useEffect(() => {
      let cancelled = false;
      (async () => {
        setLoading(true);
        setError(null);
        try {
          let source: RoundItem[] = [];
          if (initialItems && initialItems.length) {
            source = initialItems;
          } else {
            source = await apiFetchItems(
              challengeId,
              itemCount,
              "spot_mistake"
            );
          }
          // validate / filter
          const cleaned = source
            .filter(
              (r) =>
                !!r?.item?.audioSrc &&
                typeof r?.item?.isMatch !== "undefined" &&
                r?.item?.displayText
            )
            .slice(0, itemCount);

          const randomized = shuffle(cleaned);
          if (!cancelled) {
            setItems(randomized);
          }
        } catch (e: any) {
          if (!cancelled) setError(e?.message ?? "Failed to load items.");
        } finally {
          if (!cancelled) setLoading(false);
        }
      })();
      return () => {
        cancelled = true;
      };
    }, [challengeId, itemCount, initialItems, shuffle]);

    // ==== Timer end / flow end ====
    useEffect(() => {
      if (!started) return;
      if (secondsLeft <= 0 || idx >= total) {
        endRound();
      }
    }, [secondsLeft, idx, total, started]);

    const endRound = useCallback(async () => {
      if (finished) return;
      setFinished(true);
      if (timerRef.current) {
        window.clearInterval(timerRef.current);
        timerRef.current = null;
      }
      const durationMs = Math.max(
        0,
        performance.now() - roundStartMsRef.current
      );
      const avgLatency =
        latencies.length > 0
          ? Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length)
          : null;

      const summary: EndSummary = {
        sessionId,
        challengeId,
        durationMs: Math.round(durationMs),
        score,
        itemsSeen,
        itemsCorrect,
        streakMax,
        avgLatencyMs: avgLatency,
      };

      // best-effort server finalize (now includes mistakes for analytics)
      void apiEndSession({ ...summary, deviceType, mistakes: wrongAttempts });

      setShowSummary(true);
      onEnd?.(summary);
    }, [
      finished,
      sessionId,
      challengeId,
      score,
      itemsSeen,
      itemsCorrect,
      streakMax,
      latencies,
      deviceType,
      onEnd,
      wrongAttempts,
    ]);

    // ==== Audio handling ====
    const attachAudioHandlers = useCallback((el: HTMLAudioElement | null) => {
      if (!el) return;
      const onCanPlay = () => setAudioReady(true);
      const onPlay = () => {
        setAudioPlaying(true);
        setAudioReady(true);
        audioStartStampRef.current = performance.now();
      };
      const onPause = () => setAudioPlaying(false);
      const onError = () => {
        setAudioError("Audio failed to load.");
        setAudioReady(false);
      };
      el.addEventListener("canplaythrough", onCanPlay);
      el.addEventListener("play", onPlay);
      el.addEventListener("pause", onPause);
      el.addEventListener("error", onError);
      return () => {
        el.removeEventListener("canplaythrough", onCanPlay);
        el.removeEventListener("play", onPlay);
        el.removeEventListener("pause", onPause);
        el.removeEventListener("error", onError);
      };
    }, []);

    useEffect(() => {
      const el = audioRef.current;
      const cleanup = attachAudioHandlers(el);
      return () => {
        cleanup && cleanup();
      };
    }, [attachAudioHandlers]);

    // 🔓 One-time "unlock" on first pointer (helps iOS/Safari)
    useEffect(() => {
      const unlock = async () => {
        const el = audioRef.current;
        if (!el) return;
        try {
          el.muted = true;
          el.setAttribute("playsinline", "");
          await el.play();
          el.pause();
          el.currentTime = 0;
        } catch {}
        el.muted = false;
      };
      window.addEventListener("pointerdown", unlock, { once: true });
      return () => window.removeEventListener("pointerdown", unlock);
    }, []);

    const playCurrentAudio = useCallback(async () => {
      const el = audioRef.current;
      if (!el || !current?.audioSrc) return;
      try {
        setAudioError(null);
        setAudioReady(false);
        el.setAttribute("playsinline", "");
        // ensure fresh assignment (avoid stale promise errors)
        if (el.src !== current.audioSrc) {
          el.src = current.audioSrc;
          try {
            el.load();
          } catch {}
        } else {
          // reset to start
          try {
            el.pause();
            el.currentTime = 0;
          } catch {}
        }
        await el.play(); // MUST be called from user gesture chain
      } catch {
        setAudioError("Autoplay blocked. Tap play.");
      }
    }, [current?.audioSrc]);

    const replayAudio = useCallback(async () => {
      const now = performance.now();
      if (now < replayCooldownRef.current) return;
      replayCooldownRef.current = now + 500; // 0.5s cooldown
      const el = audioRef.current;
      if (!el) return;
      try {
        el.setAttribute("playsinline", "");
        el.currentTime = 0;
        await el.play();
      } catch {
        setAudioError("Autoplay blocked. Tap play.");
      }
    }, []);

    // preload next few audios
    useEffect(() => {
      if (!items.length) return;
      const toPreload: string[] = [];
      for (let k = 1; k <= preloadAhead; k++) {
        if (idx + k < total) {
          const nxt = items[idx + k]?.item?.audioSrc;
          if (nxt) toPreload.push(nxt);
        }
      }
      toPreload.forEach((src) => {
        const a = new Audio();
        a.preload = "auto";
        a.src = src;
      });
    }, [idx, items, total]);

    // 🎵 Auto-play audio when idx changes (after first item)
    useEffect(() => {
      if (!started || finished || !current) return;
      if (idx === 0) return; // startRound() handles first play directly

      // Small delay to ensure state is settled
      const timer = setTimeout(() => {
        playCurrentAudio();
      }, 50);

      return () => clearTimeout(timer);
    }, [idx, started, finished, current, playCurrentAudio]);

    // ==== Answer / scoring ====
    const submitChoice = useCallback(
      async (choice: "match" | "mismatch") => {
        if (!current || answerLock || finished) return;
        setAnswerLock(true);
        setLastAnswer(choice);

        const latency =
          audioStartStampRef.current > 0
            ? Math.max(0, performance.now() - audioStartStampRef.current)
            : 0;

        const correct = Boolean(current.isMatch) === (choice === "match");

        // stats
        setItemsSeen((n) => n + 1);
        if (correct) {
          setItemsCorrect((n) => n + 1);
          setScore((s) => s + 10);
          setStreak((st) => {
            const ns = st + 1;
            setStreakMax((mx) => Math.max(mx, ns));
            return ns;
          });
        } else {
          setStreak(0);
        }
        setLatencies((arr) => (latency ? [...arr, latency] : arr));

        // NEW: record wrong attempts for scoreboard
        if (!correct) {
          setWrongAttempts((prev) => [
            ...prev,
            {
              speedItemId: current.id,
              displayText: current.displayText,
              audioText: current.audioText ?? null,
              audioSrc: current.audioSrc,
              correctAnswer: current.isMatch ? "match" : "mismatch",
              chosen: choice,
              latencyMs: Math.round(latency),
              isMatch: current.isMatch,
            },
          ]);
        }

        // best-effort log
        void apiLogAttempt({
          sessionId,
          speedItemId: current.id,
          chosen: choice,
          isCorrect: correct,
          latencyMs: Math.round(latency),
        });

        // short feedback delay, then advance
        setTimeout(
          () => {
            setAnswerLock(false);
            setLastAnswer(null);
            setIdx((i) => i + 1);
            // Audio will auto-play via useEffect
          },
          correct ? 140 : 220
        );
      },
      [answerLock, current, finished, sessionId]
    );

    // keyboard shortcuts
    useEffect(() => {
      const onKey = (e: KeyboardEvent) => {
        if (finished || !started) return;
        if (answerLock) return;
        if (e.key === "ArrowLeft" || e.key.toLowerCase() === "x") {
          e.preventDefault();
          submitChoice("mismatch");
        } else if (
          e.key === "ArrowRight" ||
          e.key.toLowerCase() === "v" ||
          e.key === "Enter"
        ) {
          e.preventDefault();
          submitChoice("match");
        } else if (e.key === " " || e.code === "Space") {
          e.preventDefault();
          replayAudio();
        }
      };
      window.addEventListener("keydown", onKey);
      return () => window.removeEventListener("keydown", onKey);
    }, [submitChoice, replayAudio, finished, started, answerLock]);

    // pointer / swipe gestures
    const onPointerDown = useCallback(
      (e: React.PointerEvent) => {
        if (finished || answerLock) return;
        dragStartX.current = e.clientX;
        dragDx.current = 0;
        (e.currentTarget as HTMLElement).setPointerCapture(e.pointerId);
      },
      [finished, answerLock]
    );

    const onPointerMove = useCallback((e: React.PointerEvent) => {
      if (dragStartX.current == null) return;
      dragDx.current = e.clientX - dragStartX.current;
      const card = document.getElementById("ssm-card");
      if (card) {
        card.style.transform = `translateX(${clamp(
          dragDx.current,
          -120,
          120
        )}px) rotate(${clamp(dragDx.current / 12, -6, 6)}deg)`;
      }
    }, []);

    const onPointerUp = useCallback(
      (e: React.PointerEvent) => {
        const dx = dragDx.current;
        dragStartX.current = null;
        dragDx.current = 0;
        const card = document.getElementById("ssm-card");
        if (card) {
          card.style.transform = "";
        }
        if (finished || answerLock) return;
        if (Math.abs(dx) > dragThreshold) {
          if (dx > 0) submitChoice("match");
          else submitChoice("mismatch");
        }
      },
      [submitChoice, finished, answerLock]
    );

    // ==== UI helpers ====
    const pctTime = useMemo(
      () =>
        total > 0
          ? clamp(
              ((timeLimitSeconds - secondsLeft) /
                Math.max(1, timeLimitSeconds)) *
                100,
              0,
              100
            )
          : 0,
      [secondsLeft, timeLimitSeconds, total]
    );

    // ==== Start Round (User gesture) ====
    const startRound = useCallback(async () => {
      if (!items.length) return;

      setStarted(true);
      setFinished(false);
      setShowSummary(false);
      setIdx(0);
      setSecondsLeft(timeLimitSeconds);
      setScore(0);
      setItemsSeen(0);
      setItemsCorrect(0);
      setStreak(0);
      setStreakMax(0);
      setAnswerLock(false);
      setLastAnswer(null);
      setAudioError(null);
      setLatencies([]);
      setWrongAttempts([]); // NEW: reset mistakes on (re)start
      roundStartMsRef.current = performance.now();

      const sid = await apiStartSession(challengeId);
      setSessionId(sid ?? null);

      // start timer
      if (timerRef.current) {
        window.clearInterval(timerRef.current);
      }
      timerRef.current = window.setInterval(() => {
        setSecondsLeft((s) => {
          if (s <= 1) {
            window.clearInterval(timerRef.current!);
            return 0;
          }
          return s - 1;
        });
      }, 1000) as any;

      // IMPORTANT: Play first audio immediately (user gesture chain - unlocks iOS audio)
      await playCurrentAudio();
    }, [items, timeLimitSeconds, challengeId, playCurrentAudio]);

    // ==== Render ====
    if (loading) {
      return (
        <Card className={`w-full max-w-4xl mx-auto p-6 ${className || ""}`}>
          <div className="flex items-center justify-center h-48">
            <span className="animate-pulse text-sm text-gray-500">
              Loading speed round…
            </span>
          </div>
        </Card>
      );
    }

    if (error || !items.length) {
      return (
        <Card className={`w-full max-w-4xl mx-auto p-6 ${className || ""}`}>
          <div className="flex flex-col items-center justify-center gap-4">
            <p className="text-red-600 text-sm">
              {error || "No items available for this challenge."}
            </p>
            <Button
              variant="outline"
              onClick={() => {
                // refetch
                setStarted(false);
                setFinished(false);
                setShowSummary(false);
                setLoading(true);
                setError(null);
                setItems([]);
                // trigger effect re-run:
                setTimeout(() => setLoading(false), 0);
              }}
            >
              Retry
            </Button>
          </div>
        </Card>
      );
    }

    if (showSummary) {
      const durationMs = Math.max(
        0,
        Math.round(performance.now() - roundStartMsRef.current)
      );
      const avgLatency =
        latencies.length > 0
          ? Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length)
          : null;

      return (
        <Card className={`w-full max-w-3xl mx-auto p-6 ${className || ""}`}>
          <div className="space-y-4">
            <h2 className="text-2xl font-semibold">Speed Round Summary</h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="p-3 rounded-lg bg-gray-50">
                <div className="text-gray-500">Score</div>
                <div className="text-xl font-medium">{score}</div>
              </div>
              <div className="p-3 rounded-lg bg-gray-50">
                <div className="text-gray-500">Correct</div>
                <div className="text-xl font-medium">
                  {itemsCorrect}/{itemsSeen}
                </div>
              </div>
              <div className="p-3 rounded-lg bg-gray-50">
                <div className="text-gray-500">Max Streak</div>
                <div className="text-xl font-medium">{streakMax}</div>
              </div>
              <div className="p-3 rounded-lg bg-gray-50">
                <div className="text-gray-500">Time</div>
                <div className="text-xl font-medium">
                  {(durationMs / 1000).toFixed(1)}s
                </div>
              </div>
              <div className="p-3 rounded-lg bg-gray-50 col-span-2">
                <div className="text-gray-500">Avg Latency</div>
                <div className="text-xl font-medium">
                  {avgLatency != null ? `${avgLatency} ms` : "—"}
                </div>
              </div>
            </div>

            {/* NEW: Mistakes list */}
            {wrongAttempts.length > 0 && (
              <div className="pt-2">
                <h3 className="text-lg font-semibold">
                  Mistakes ({wrongAttempts.length})
                </h3>
                <div className="mt-2 rounded-lg border bg-gray-50 p-3 max-h-64 overflow-y-auto">
                  <ul className="space-y-2">
                    {wrongAttempts.map((w, i) => (
                      <li
                        key={`${w.speedItemId}-${i}`}
                        className="flex items-start justify-between gap-3 rounded-md bg-white px-3 py-2 border"
                      >
                        <div className="flex-1">
                          <div className="text-2xl md:text-3xl font-arabic leading-snug">
                            {w.displayText ?? "—"}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            Expected:{" "}
                            <span className="font-medium">
                              {w.correctAnswer === "match"
                                ? "Match"
                                : "Mismatch"}
                            </span>{" "}
                            · You chose:{" "}
                            <span className="font-medium">
                              {w.chosen === "match" ? "Match" : "Mismatch"}
                            </span>
                            {typeof w.latencyMs === "number" ? (
                              <> · {w.latencyMs}ms</>
                            ) : null}
                          </div>
                          {w.audioText ? (
                            <div className="text-xs text-gray-400 mt-0.5">
                              Audio hint: {w.audioText}
                            </div>
                          ) : null}
                        </div>
                        {/* (Optional) icon to hint mismatch */}
                        <div className="shrink-0 pt-1">
                          <X className="w-5 h-5 text-rose-600" />
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            <div className="flex gap-3 pt-2">
              <Button
                onClick={() => {
                  // restart from same list
                  setStarted(false);
                  setFinished(false);
                  setShowSummary(false);
                  startRound();
                }}
                className="gap-2"
              >
                <RotateCcw className="w-4 h-4" />
                Try again
              </Button>
            </div>
          </div>
        </Card>
      );
    }

    // Always-mounted audio element (so it's present when Start is clicked)
    // Hidden but interactive; playsInline helps iOS.
    const AlwaysAudio = (
      <audio ref={audioRef} preload="auto" playsInline className="hidden" />
    );

    if (!started) {
      return (
        <Card className={`w-full max-w-4xl mx-auto p-6 ${className || ""}`}>
          {AlwaysAudio}
          <div className="py-10 flex flex-col items-center text-center gap-4">
            <h2 className="text-2xl font-semibold">
              Speed Round — Spot the Mistake
            </h2>
            <p className="text-gray-600 max-w-md">
              Listen to the clip and decide if the text{" "}
              <span className="font-medium">matches</span> the audio. Tap{" "}
              <span className="font-semibold">Match</span> (✓) or{" "}
              <span className="font-semibold">Mismatch</span> (✕).
            </p>
            <Button
              onClick={startRound}
              className="px-6 py-5 text-base rounded-xl gap-2"
              disabled={!items.length}
            >
              <Play className="w-4 h-4" />
              Start
            </Button>
          </div>
        </Card>
      );
    }

    const feedbackTint =
      lastAnswer == null
        ? ""
        : lastAnswer === "match"
        ? "ring-2 ring-emerald-500"
        : "ring-2 ring-rose-500";

    return (
      <Card className={`w-full max-w-5xl mx-auto p-4 ${className || ""}`}>
        {/* Header */}
        {AlwaysAudio}
        <div className="flex items-center justify-between gap-3 mb-4">
          <div className="text-sm text-gray-600">
            Time: <span className="font-medium">{fmtTime(secondsLeft)}</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-600">
              Score: <span className="font-medium">{score}</span>
            </div>
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Flame className="w-4 h-4" />
              <span className="font-medium">{streak}</span>
            </div>
          </div>
        </div>

        {/* Progress */}
        <Progress value={pctTime} className="h-1.5 mb-4" />

        {/* Main Card */}
        <div
          id="ssm-card"
          className={`relative bg-white rounded-2xl shadow-sm border p-6 md:p-8 transition-transform ${feedbackTint}`}
          onPointerDown={onPointerDown}
          onPointerMove={onPointerMove}
          onPointerUp={onPointerUp}
          role="group"
          aria-label="Answer area; swipe right for match, left for mismatch"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="text-xs text-gray-500">
              Item {idx + 1} / {total}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={replayAudio}
                disabled={answerLock || !audioReady}
                aria-label="Replay audio"
              >
                <Volume2 className="w-4 h-4" />
              </Button>
              {audioPlaying ? (
                <PauseCircle className="w-5 h-5 text-gray-700" />
              ) : (
                <Play className="w-5 h-5 text-gray-700" />
              )}
            </div>
          </div>

          <div className="min-h-[140px] md:min-h-[180px] flex items-center justify-center">
            <div className="text-5xl md:text-6xl font-semibold font-arabic text-center leading-tight select-none">
              {current?.displayText ?? "—"}
            </div>
          </div>

          {audioError && (
            <div className="mt-3 text-xs text-amber-600">
              {audioError} — tap the play icon to start audio.
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="grid grid-cols-2 gap-3 mt-6">
          <Button
            onClick={() => submitChoice("mismatch")}
            disabled={answerLock || finished}
            variant="outline"
            className="w-full py-6 border-rose-200 hover:bg-rose-50 gap-2"
            aria-label="Mark as mismatch"
          >
            <X className="w-5 h-5 text-rose-600" />
            <span className="text-rose-700 font-medium">Mismatch</span>
          </Button>
          <Button
            onClick={() => submitChoice("match")}
            disabled={answerLock || finished}
            className="w-full py-6 bg-emerald-600 hover:bg-emerald-700 gap-2"
            aria-label="Mark as match"
          >
            <Check className="w-5 h-5" />
            <span>Match</span>
          </Button>
        </div>

        {/* Footer */}
        <div className="mt-4 text-xs text-gray-500 flex items-center justify-between">
          <div>
            Correct:{" "}
            <span className="font-medium">
              {itemsCorrect}/{itemsSeen}
            </span>
          </div>
          <div className="hidden md:block">
            Tip: swipe right = Match, swipe left = Mismatch. Space = replay.
          </div>
        </div>
      </Card>
    );
  }
);

SpeedSpotMistake.displayName = "SpeedSpotMistake";

export default SpeedSpotMistake;

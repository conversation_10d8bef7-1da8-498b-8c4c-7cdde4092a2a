// app/lesson/quiz.tsx

"use client";

import { toast } from "sonner";
import Image from "next/image";
import Confetti from "react-confetti";
import { useRouter } from "next/navigation";
import { useCallback, useState, useTransition, useEffect, useRef } from "react";
import { useAudio, useWindowSize, useMount } from "react-use";

import { reduceHearts } from "@/actions/user-progress";
import { useHeartsModal } from "@/store/use-hearts-modal";
import { challengeOptions, challenges, userSubscription } from "@/db/schema";
import { usePracticeModal } from "@/store/use-practice-modal";
import { upsertChallengeProgress } from "@/actions/challenge-progress";

import Background from "./bg";
import { Header } from "./header";
import { Footer } from "./footer";
import { Challenge } from "./challenge";
import { Matching } from "./matching";
import { TapWhatYouHear } from "./TapWhatYouHear";
import { ResultCard } from "./result-card";
import { ImageAudioSelectComponent } from "./ImageAudioSelect";
import { DragAndDrop } from "./DragAndDrop";
import { FillInTheBlank } from "./FillInTheBlank";
import { Assist } from "./Assist";
import { SpeakThis } from "./speakThis";
import SpeakThisAdvanced from "./advancedChallenges/speakThisAdvanced";
import { isSequenceInOrder } from "@/lib/utils";
import { Explainer } from "./explainer";
import SpeakSequence from "./advancedChallenges/SpeakSequence";
import SpeedMatchAudio from "./speedMatchAudio";
import SpeedSpotMistake from "./speedSpotMistake";

type ReferrerInfo = {
  from: "coursesplayer" | "learn" | "unknown";
  curriculumId?: string;
} | null;

type Props = {
  initialPercentage: number;
  initialHearts: number;
  initialLessonId: number;
  initialLessonChallenges: (typeof challenges.$inferSelect & {
    completed: boolean;
    isSkippable?: boolean | null;
    isNonGraded?: boolean | null;
    challengeOptions: (typeof challengeOptions.$inferSelect & {
      side: "left" | "right";
    })[];
  })[];
  userSubscription:
    | (typeof userSubscription.$inferSelect & {
        isActive: boolean;
      })
    | null;
  referrerInfo?: ReferrerInfo;
};

export const Quiz = ({
  initialPercentage,
  initialHearts,
  initialLessonId,
  initialLessonChallenges,
  userSubscription,
  referrerInfo = null,
}: Props) => {
  const didLogRef = useRef(false);
  useEffect(() => {
    if (didLogRef.current) return;
    didLogRef.current = true;
    console.log("Quiz component mounted");
    console.log("DEBUG: Using full viewport height for quiz container");
    console.log("Referrer info received:", referrerInfo);
  }, [referrerInfo]);

  const { open: openHeartsModal } = useHeartsModal();
  const { open: openPracticeModal } = usePracticeModal();
  const router = useRouter();
  const { width, height } = useWindowSize();

  const [finishAudio, , finishControls] = useAudio({
    src: "/finish.mp3",
  });
  const [correctAudio, , correctControls] = useAudio({
    src: "/correct.wav",
  });
  const [incorrectAudio, , incorrectControls] = useAudio({
    src: "/incorrect.wav",
  });

  const [pending, startTransition] = useTransition();

  const isSubmittingRef = useRef(false);
  const hasHandledWrongRef = useRef(false);
  const speedEndHandledRef = useRef(false);

  // --- FIX 1: make withSubmitGuard stable & include setStatus in deps
  const [status, setStatus] = useState<
    "correct" | "wrong" | "none" | "submitting"
  >("none");

  const withSubmitGuard = useCallback(
    async (fn: () => Promise<void> | void) => {
      if (isSubmittingRef.current) {
        console.log("withSubmitGuard: already submitting, ignoring.");
        return;
      }
      isSubmittingRef.current = true;
      setStatus("submitting");
      try {
        await fn();
      } finally {
        isSubmittingRef.current = false;
        setStatus((prev) => (prev === "submitting" ? "none" : prev));
      }
    },
    [setStatus]
  );

  const [lessonId] = useState<number>(initialLessonId);
  const [hearts, setHearts] = useState<number>(initialHearts);
  const [percentage, setPercentage] = useState<number>(() => {
    return initialPercentage === 100 ? 0 : initialPercentage;
  });
  const [challenges] = useState(initialLessonChallenges);
  const [activeIndex, setActiveIndex] = useState<number>(() => {
    const uncompletedIndex = challenges.findIndex((ch) => !ch.completed);
    return uncompletedIndex === -1 ? 0 : uncompletedIndex;
  });
  const [quizCompleted, setQuizCompleted] = useState<boolean>(false);

  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [selectedPairOption, setSelectedPairOption] = useState<number | null>(
    null
  );
  const [matchedPairs, setMatchedPairs] = useState<Set<number>>(new Set());
  const [dragAndDropSelections, setDragAndDropSelections] = useState<number[]>(
    []
  );
  const [dragAndDropAttempt, setDragAndDropAttempt] = useState(0);

  // NEW: Parent-level "awaiting finish" gate for speed rounds
  const [speedAwaitingFinish, setSpeedAwaitingFinish] =
    useState<boolean>(false);
  const [speedEndSummary, setSpeedEndSummary] = useState<any | null>(null);

  const handleFallbackNavigation = useCallback(() => {
    console.log("Executing fallback navigation to /learn");
    try {
      router.push("/learn");
    } catch (error) {
      console.error("Fallback navigation failed:", error);
      (window as any).location.href = "/learn";
    }
  }, [router]);

  const handleSmartNavigation = useCallback(() => {
    console.log("Attempting smart navigation with referrer:", referrerInfo);

    if (!referrerInfo) {
      console.log("No referrer info, using fallback navigation");
      handleFallbackNavigation();
      return;
    }

    try {
      switch (referrerInfo.from) {
        case "coursesplayer": {
          const coursesPlayerUrl = referrerInfo.curriculumId
            ? `/coursesPlayer?curriculumId=${referrerInfo.curriculumId}`
            : "/coursesPlayer";
          console.log("Navigating back to coursesplayer:", coursesPlayerUrl);
          router.push(coursesPlayerUrl);
          break;
        }
        case "learn":
          console.log("Navigating back to learn page");
          router.push("/learn");
          break;
        case "unknown":
        default:
          console.log("Unknown referrer, using fallback navigation");
          handleFallbackNavigation();
          break;
      }
    } catch (error) {
      console.error("Smart navigation failed:", error);
      handleFallbackNavigation();
    }
  }, [referrerInfo, router, handleFallbackNavigation]);

  useMount(() => {
    if (initialPercentage === 100) {
      openPracticeModal();
    }
  });

  const handleCorrectAnswer = useCallback(() => {
    console.log("handleCorrectAnswer called");
    hasHandledWrongRef.current = false;
    setStatus("correct");
    setPercentage((prev) => prev + 100 / challenges.length);
    correctControls.play();
  }, [challenges.length, correctControls, setStatus]);

  const handleIncorrectAnswer = useCallback(() => {
    console.log("handleIncorrectAnswer called");
    setStatus("wrong");
    incorrectControls.play();
    setSelectedOption(null);
    setSelectedPairOption(null);
    setDragAndDropSelections([]);

    startTransition(() => {
      const currentChallenge = challenges[activeIndex];
      reduceHearts(currentChallenge.id)
        .then((response) => {
          console.log("reduceHearts response:", response);
          if (response?.error === "hearts") {
            openHeartsModal();
            return;
          }
          if (!response?.error) {
            setHearts((prev) => Math.max(prev - 1, 0));
          }
        })
        .catch(() => toast.error("Something went wrong. Please try again."));
    });
  }, [
    activeIndex,
    challenges,
    incorrectControls,
    openHeartsModal,
    startTransition,
    setStatus,
  ]);

  const handleMatching = useCallback(() => {
    console.log(
      "Matching challenge detected:",
      selectedOption,
      selectedPairOption
    );
    if (isSubmittingRef.current) {
      console.log("Already submitting, aborting duplicate submission.");
      return;
    }
    const challenge = challenges[activeIndex];
    const options = challenge?.challengeOptions ?? [];

    if (selectedOption === null || selectedPairOption === null) {
      console.log("One or both options are not selected.");
      return;
    }

    const selectedOptionObj = options.find(
      (option) => option.id === selectedOption
    );
    const selectedPairOptionObj = options.find(
      (option) => option.id === selectedPairOption
    );

    if (!selectedOptionObj || !selectedPairOptionObj) {
      console.log("Option objects not found.");
      return;
    }

    const isCorrectMatch =
      selectedOptionObj.matchPairId === selectedPairOptionObj.matchPairId;

    if (isCorrectMatch) {
      console.log("Correct match found.");
      const newMatchedPairs = new Set(matchedPairs);
      newMatchedPairs.add(selectedOptionObj.id);
      newMatchedPairs.add(selectedPairOptionObj.id);

      setMatchedPairs(newMatchedPairs);
      setSelectedOption(null);
      setSelectedPairOption(null);

      if (newMatchedPairs.size === options.length) {
        console.log("All pairs matched, submitting progress...");
        withSubmitGuard(() => {
          startTransition(() => {
            upsertChallengeProgress(
              challenge.id,
              selectedOptionObj.id,
              selectedPairOptionObj.id
            )
              .then((response) => {
                if (response?.error === "hearts") {
                  openHeartsModal();
                  return;
                }
                console.log("Challenge progress submitted successfully.");
                handleCorrectAnswer();
              })
              .catch((error) => {
                console.error("Error in upserting challenge progress:", error);
                toast.error("Something went wrong. Please try again.");
                setStatus("none");
              });
          });
        });
      } else {
        console.log("Not all pairs matched yet. Waiting for remaining pairs.");
        setStatus("none");
      }
    } else {
      console.log("Match incorrect.");
      setStatus("wrong");
      setTimeout(() => {
        setStatus("none");
      }, 1000);
      handleIncorrectAnswer();
    }
  }, [
    activeIndex,
    challenges,
    handleCorrectAnswer,
    handleIncorrectAnswer,
    matchedPairs,
    openHeartsModal,
    selectedOption,
    selectedPairOption,
    startTransition,
    withSubmitGuard,
    setStatus,
  ]);

  useEffect(() => {
    if (selectedOption !== null && selectedPairOption !== null) {
      handleMatching();
    }
  }, [selectedOption, selectedPairOption, handleMatching]);

  useEffect(() => {
    if (quizCompleted) {
      finishControls.play();
    }
  }, [quizCompleted, finishControls]);

  const onNext = useCallback(() => {
    console.log("onNext called");
    hasHandledWrongRef.current = false;
    speedEndHandledRef.current = false;

    // Reset speed summary/awaiting state on any move
    setSpeedAwaitingFinish(false);
    setSpeedEndSummary(null);

    if (activeIndex + 1 >= challenges.length) {
      console.log("All challenges completed, marking quiz as completed.");
      setQuizCompleted(true);
    } else {
      setActiveIndex((current) => current + 1);
      setMatchedPairs(new Set());
      setSelectedOption(null);
      setSelectedPairOption(null);
      setDragAndDropSelections([]);
      setStatus("none");
      console.log("Moved to next challenge. Reset matched pairs.");
    }
  }, [activeIndex, challenges.length, setStatus]);

  const onSelect = (
    id: number | null,
    side: "left" | "right" | "drag-and-drop" | "fill-in-the-blank"
  ) => {
    console.log("onSelect called with id:", id, "and side:", side);
    if (status !== "none") return;

    if (side === "left") {
      setSelectedOption((prev) => (prev === id ? null : id));
    } else if (side === "right") {
      setSelectedPairOption((prev) => (prev === id ? null : id));
    } else if (side === "drag-and-drop") {
      if (id === null) return;
      setDragAndDropSelections((prevSelections) => {
        console.log("Previous dragAndDropSelections:", prevSelections);
        let newSelections: number[];
        if (prevSelections.includes(id)) {
          newSelections = prevSelections.filter(
            (selection) => selection !== id
          );
        } else {
          newSelections = [...prevSelections, id];
        }
        console.log("Updated dragAndDropSelections:", newSelections);
        return newSelections;
      });
    } else if (side === "fill-in-the-blank") {
      setSelectedOption((prev) => (prev === id ? null : id));
    }
  };

  const onContinue = () => {
    console.log("onContinue called");
    console.log("Current state before onContinue:", {
      selectedOption,
      selectedPairOption,
      status,
      hearts,
      matchedPairs,
      speedAwaitingFinish,
      activeIndex,
    });

    if (isSubmittingRef.current) {
      console.log("onContinue: submission in progress, ignoring.");
      return;
    }

    const challenge = challenges[activeIndex];
    const isExplainerType = challenge.type === "EXPLAINER";
    const isSkippableFlag = !!challenge.isSkippable || isExplainerType;

    if (isSkippableFlag) {
      console.log("Skippable challenge detected (EXPLAINER or flagged).");
      withSubmitGuard(async () => {
        startTransition(() => {
          upsertChallengeProgress(challenge.id, 1)
            .then((response) => {
              if (response?.error === "hearts") {
                openHeartsModal();
                return;
              }
              setPercentage((prev) => prev + 100 / challenges.length);
              onNext();
            })
            .catch((error) => {
              console.error(
                "Error upserting progress for skippable challenge:",
                error
              );
              toast.error("Something went wrong. Please try again.");
              setStatus("none");
            });
        });
      });
      return;
    }

    if (status === "wrong") {
      console.log("Status was wrong, resetting state.");
      hasHandledWrongRef.current = false;
      resetState();
      return;
    }

    // SPECIAL: Speed rounds are child-driven during play; once they finish,
    // we wait here until the student presses the footer button.
    if (
      challenge.type === "SPEED_MATCH_AUDIO" ||
      challenge.type === "SPEED_SPOT_MISTAKE"
    ) {
      if (!speedAwaitingFinish) {
        console.log(
          "Speed round not awaiting finish yet; footer press is a no-op."
        );
        return;
      }
      console.log(
        "Speed round awaiting finish; footer press will advance or finish."
      );

      // If this was the last challenge, pressing the footer triggers confetti.
      const isLast = activeIndex + 1 >= challenges.length;
      // Clear gate and move on
      setSpeedAwaitingFinish(false);
      setSpeedEndSummary(null);

      if (isLast) {
        console.log("Last challenge: setting quizCompleted = true");
        setQuizCompleted(true);
      } else {
        onNext();
      }
      return;
    }

    if (status === "correct") {
      console.log("Status was correct, moving to next.");
      onNext();
      return;
    }

    switch (challenge.type) {
      case "MATCHING":
        handleMatching();
        break;
      case "TAP_WHAT_YOU_HEAR":
        handleTapWhatYouHear();
        break;
      case "ASSIST":
        handleAssist();
        break;
      case "SELECT":
        handleSelect();
        break;
      case "IMAGE_AUDIO_SELECT":
        handleImageAudioSelect();
        break;
      case "DRAG_AND_DROP":
        handleDragAndDrop();
        break;
      case "FILL_IN_THE_BLANK":
        handleFillInTheBlank();
        break;
      case "SPEAK_THIS":
        handleSpeakThis();
        break;
      case "SPEAK_THIS_ADVANCED":
        console.log(
          "onContinue called for SPEAK_THIS_ADVANCED, but logic is handled by child callback."
        );
        break;
      case "SPEAK_SEQUENCE":
        console.log(
          "onContinue called for SPEAK_SEQUENCE — child component controls flow (no button action)."
        );
        break;
      // SPEED_* handled above via speedAwaitingFinish gate
      default:
        console.log("Unknown challenge type:", challenge.type);
    }
  };

  const resetState = () => {
    console.log("resetState called");
    const currentChallenge = challenges[activeIndex];
    if (currentChallenge.type === "DRAG_AND_DROP") {
      setDragAndDropAttempt((prev) => prev + 1);
    }
    setStatus("none");
    setSelectedOption(null);
    setSelectedPairOption(null);
    setDragAndDropSelections([]);
    setMatchedPairs(new Set());
    hasHandledWrongRef.current = false;
    speedEndHandledRef.current = false;

    // Reset speed gate if user is retrying
    setSpeedAwaitingFinish(false);
    setSpeedEndSummary(null);

    console.log("State reset for retry");
  };

  const handleTapWhatYouHear = () => {
    console.log("Tap What You Hear challenge detected");
    const challenge = challenges[activeIndex];
    const correctOption =
      challenge?.challengeOptions.find((option) => option.correct) ?? null;
    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      withSubmitGuard(async () => {
        startTransition(() => {
          upsertChallengeProgress(challenge.id, selectedOption as number)
            .then((response) => {
              if (response?.error === "hearts") {
                openHeartsModal();
                return;
              }
              handleCorrectAnswer();
            })
            .catch((error) => {
              console.error("Error in upsertChallengeProgress:", error);
              toast.error("Something went wrong. Please try again.");
              setStatus("none");
            });
        });
      });
    } else {
      handleIncorrectAnswer();
    }
  };

  const handleAssist = () => {
    console.log("Assist challenge detected");
    const challenge = challenges[activeIndex];
    const correctOption =
      challenge?.challengeOptions.find((option) => option.correct) ?? null;
    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      withSubmitGuard(async () => {
        startTransition(() => {
          upsertChallengeProgress(challenge.id, selectedOption as number)
            .then((response) => {
              if (response?.error === "hearts") {
                openHeartsModal();
                return;
              }
              handleCorrectAnswer();
            })
            .catch((error) => {
              console.error("Error in upsertChallengeProgress:", error);
              toast.error("Something went wrong. Please try again.");
              setStatus("none");
            });
        });
      });
    } else {
      handleIncorrectAnswer();
    }
  };

  const handleSelect = () => {
    console.log("Select challenge detected");
    const challenge = challenges[activeIndex];
    const correctOption =
      challenge?.challengeOptions.find((option) => option.correct) ?? null;
    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      withSubmitGuard(async () => {
        startTransition(() => {
          upsertChallengeProgress(challenge.id, selectedOption as number)
            .then((response) => {
              if (response?.error === "hearts") {
                openHeartsModal();
                return;
              }
              handleCorrectAnswer();
            })
            .catch((error) => {
              console.error("Error in upsertChallengeProgress:", error);
              toast.error("Something went wrong. Please try again.");
              setStatus("none");
            });
        });
      });
    } else {
      handleIncorrectAnswer();
    }
  };

  const handleImageAudioSelect = () => {
    console.log("Image and Audio challenge detected");
    const challenge = challenges[activeIndex];
    const correctOption =
      challenge?.challengeOptions.find((option) => option.correct) ?? null;
    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      withSubmitGuard(async () => {
        startTransition(() => {
          upsertChallengeProgress(challenge.id, selectedOption as number)
            .then((response) => {
              if (response?.error === "hearts") {
                openHeartsModal();
                return;
              }
              handleCorrectAnswer();
            })
            .catch((error) => {
              console.error("Error in upsertChallengeProgress:", error);
              toast.error("Something went wrong. Please try again.");
              setStatus("none");
            });
        });
      });
    } else {
      handleIncorrectAnswer();
    }
  };

  const handleDragAndDrop = () => {
    console.log("Drag and Drop challenge detected");
    const challenge = challenges[activeIndex];
    const options = challenge?.challengeOptions ?? [];
    const sequences = dragAndDropSelections
      .map((id) => options.find((option) => option.id === id)?.sequence ?? 0)
      .filter((seq) => seq > 0);

    const challengeZoneSequences = options
      .filter((option) => !dragAndDropSelections.includes(option.id))
      .map((option) => option.sequence)
      .filter((seq): seq is number => seq !== null);

    const isCorrectOrder = isSequenceInOrder(sequences, challengeZoneSequences);
    const hasIncorrectOption = challengeZoneSequences.some((seq) => seq !== 99);

    if (hasIncorrectOption) {
      handleIncorrectAnswer();
      return;
    }

    if (isCorrectOrder && !hasIncorrectOption) {
      withSubmitGuard(async () => {
        startTransition(() => {
          upsertChallengeProgress(challenge.id, dragAndDropSelections.join(","))
            .then((response) => {
              if (response?.error === "hearts") {
                openHeartsModal();
                return;
              }
              handleCorrectAnswer();
            })
            .catch((error) => {
              console.error("Error updating challenge progress:", error);
              toast.error("Something went wrong. Please try again.");
              setStatus("none");
            });
        });
      });
    } else {
      handleIncorrectAnswer();
    }
  };

  const handleFillInTheBlank = () => {
    console.log("Fill In The Blank challenge detected");
    const challenge = challenges[activeIndex];
    const correctOption =
      challenge?.challengeOptions.find((option) => option.correct) ?? null;
    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      withSubmitGuard(async () => {
        startTransition(() => {
          upsertChallengeProgress(challenge.id, selectedOption as number)
            .then((response) => {
              if (response?.error === "hearts") {
                openHeartsModal();
                return;
              }
              handleCorrectAnswer();
            })
            .catch((error) => {
              console.error("Error in upsertChallengeProgress:", error);
              toast.error("Something went wrong. Please try again.");
              setStatus("none");
            });
        });
      });
    } else {
      handleIncorrectAnswer();
    }
  };

  const handleSpeakThis = () => {
    console.log("Speak This challenge detected");
    if (selectedOption == null) {
      console.log("SpeakThis not ready (no selectedOption).");
      return;
    }

    const challenge = challenges[activeIndex];
    withSubmitGuard(async () => {
      startTransition(() => {
        upsertChallengeProgress(challenge.id, 1)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            handleCorrectAnswer();
          })
          .catch((error) => {
            console.error("Error in upsertChallengeProgress:", error);
            toast.error("Something went wrong. Please try again.");
            setStatus("none");
          });
      });
    });
  };

  const handleSpeakThisAdvancedResult = (isCorrect: boolean) => {
    console.log("[Quiz] handleSpeakThisAdvancedResult called", {
      isCorrect,
      hasHandledWrong: hasHandledWrongRef.current,
      timestamp: Date.now(),
    });

    if (isCorrect) {
      console.log("SpeakThisAdvanced: user is correct, upserting progress...");
      withSubmitGuard(async () => {
        const challenge = challenges[activeIndex];
        startTransition(() => {
          upsertChallengeProgress(challenge.id, 1)
            .then((response) => {
              if (response?.error === "hearts") {
                openHeartsModal();
                return;
              }
              console.log("SpeakThisAdvanced progress submitted successfully.");
              handleCorrectAnswer();
            })
            .catch((error) => {
              console.error("Error in upsertChallengeProgress:", error);
              toast.error("Something went wrong. Please try again.");
              setStatus("none");
            });
        });
      });
    } else {
      if (hasHandledWrongRef.current) {
        console.log(
          "SpeakThisAdvanced: duplicate incorrect result ignored (already handled)."
        );
        return;
      }
      hasHandledWrongRef.current = true;
      console.log("SpeakThisAdvanced: user is incorrect, reducing hearts...");
      handleIncorrectAnswer();
    }
  };

  const handleSpeakSequenceComplete = useCallback(
    (matchedOptionIds?: number[]) => {
      const challenge = challenges[activeIndex];
      const totalTargets = Math.min(
        10,
        challenge?.challengeOptions?.length || 0
      );
      const matchedCount = matchedOptionIds?.length ?? 0;

      console.log("[Quiz] SpeakSequence completed", {
        matchedCount,
        totalTargets,
        challengeId: challenge.id,
      });

      if (totalTargets > 0 && matchedCount >= totalTargets) {
        withSubmitGuard(async () => {
          startTransition(() => {
            upsertChallengeProgress(challenge.id, 1)
              .then((response) => {
                if (response?.error === "hearts") {
                  openHeartsModal();
                  return;
                }
                handleCorrectAnswer();
              })
              .catch((error) => {
                console.error("Error in upsertChallengeProgress:", error);
                toast.error("Something went wrong. Please try again.");
                setStatus("none");
              });
          });
        });
      } else {
        handleIncorrectAnswer();
      }
    },
    [
      activeIndex,
      challenges,
      handleCorrectAnswer,
      handleIncorrectAnswer,
      openHeartsModal,
      startTransition,
      withSubmitGuard,
      setStatus,
    ]
  );

  const handleSpeedRoundEnd = useCallback(
    (round: "SPEED_MATCH_AUDIO" | "SPEED_SPOT_MISTAKE", summary: any) => {
      if (speedEndHandledRef.current) {
        console.log("[Quiz] Speed round end already handled; ignoring.");
        return;
      }
      speedEndHandledRef.current = true;

      const challenge = challenges[activeIndex];
      console.log("[Quiz] Speed round finished", {
        type: round,
        challengeId: challenge?.id,
        summary,
      });

      withSubmitGuard(async () => {
        startTransition(() => {
          upsertChallengeProgress(challenge.id, 1)
            .then((response) => {
              if (response?.error === "hearts") {
                openHeartsModal();
                return;
              }
              // Mark correct (points, SFX) but DO NOT auto-advance.
              handleCorrectAnswer();

              // Gate on the footer: wait for the student to press it.
              setSpeedEndSummary(summary ?? null);
              setSpeedAwaitingFinish(true);

              // IMPORTANT: removed auto-advance:
              // setTimeout(onNext, 350);  <-- deleted to honor scoreboard pause
            })
            .catch((error) => {
              console.error("Error finalizing speed round:", error);
              toast.error("Something went wrong. Please try again.");
              setStatus("none");
              speedEndHandledRef.current = false;
            });
        });
      });
    },
    [
      activeIndex,
      challenges,
      handleCorrectAnswer,
      openHeartsModal,
      startTransition,
      withSubmitGuard,
      setStatus,
    ]
  );

  if (quizCompleted) {
    return (
      <Background>
        <div className="h-screen flex flex-col overflow-hidden">
          {finishAudio ? <>{finishAudio}</> : null}
          <Confetti
            width={width}
            height={height}
            recycle={false}
            numberOfPieces={500}
            tweenDuration={10000}
          />
          <div className="flex-1 overflow-y-auto flex flex-col gap-y-4 lg:gap-y-8 items-center justify-center text-center">
            <Image
              src="/finish.svg"
              alt="Finish"
              className="hidden lg:block"
              height={100}
              width={100}
            />
            <Image
              src="/finish.svg"
              alt="Finish"
              className="block lg:hidden"
              height={50}
              width={50}
            />
            <h1 className="text-xl lg:text-3xl font-bold text-neutral-700">
              Great job! <br /> You&apos;ve completed the lesson.
            </h1>
            <div className="flex items-center gap-x-4 w-full max-w-sm mx-auto">
              <ResultCard variant="points" value={challenges.length * 10} />
              <ResultCard variant="hearts" value={hearts} />
            </div>
          </div>
          <Footer
            lessonId={lessonId}
            status="completed"
            onCheck={handleSmartNavigation}
            referrerInfo={referrerInfo}
            onSmartNavigate={handleSmartNavigation}
          />
        </div>
      </Background>
    );
  }

  const currentChallenge = challenges[activeIndex];
  const title =
    currentChallenge.type === "ASSIST"
      ? "Which letter is missing from this word in the audio?"
      : `Q: ${currentChallenge.question}`;

  const isExplainerType = currentChallenge.type === "EXPLAINER";
  const isSkippable = !!currentChallenge.isSkippable || isExplainerType;

  // For speed rounds: once we’re awaiting finish (scoreboard shown),
  // set footer status to "correct" so the button is enabled with "Next".
  // Otherwise use existing behavior.
  const isSpeedType =
    currentChallenge.type === "SPEED_MATCH_AUDIO" ||
    currentChallenge.type === "SPEED_SPOT_MISTAKE";

  const footerStatus:
    | "correct"
    | "wrong"
    | "none"
    | "submitting"
    | "completed" = isSkippable
    ? "correct"
    : isSpeedType && speedAwaitingFinish
    ? "correct"
    : status;

  const footerDisabled =
    pending ||
    (!isSkippable &&
      (currentChallenge.type === "DRAG_AND_DROP"
        ? dragAndDropSelections.length === 0
        : currentChallenge.type === "SPEAK_THIS_ADVANCED"
        ? false
        : currentChallenge.type === "SPEAK_SEQUENCE"
        ? true
        : currentChallenge.type === "SPEED_MATCH_AUDIO" ||
          currentChallenge.type === "SPEED_SPOT_MISTAKE"
        ? !speedAwaitingFinish // DISABLE during play, ENABLE when awaiting finish
        : !selectedOption));

  // STEP 1 & 2: Enhanced speed config extraction with validation and logging
  const speedItemCountRaw =
    // @ts-ignore - optional column added by migration
    (currentChallenge as any).speedItemCount ||
    // @ts-ignore - snake case just in case
    (currentChallenge as any).speed_item_count;

  const speedTimeLimitSecondsRaw =
    // @ts-ignore
    (currentChallenge as any).speedTimeLimitSeconds ||
    // @ts-ignore
    (currentChallenge as any).speed_time_limit_seconds;

  // Ensure valid values with proper defaults and minimums (catches 0, null, undefined, NaN)
  const speedItemCount = Math.max(1, Number(speedItemCountRaw) || 20);
  const speedTimeLimitSeconds = Math.max(
    10,
    Number(speedTimeLimitSecondsRaw) || 60
  );

  // Log speed config for debugging when rendering speed challenges
  if (
    currentChallenge.type === "SPEED_MATCH_AUDIO" ||
    currentChallenge.type === "SPEED_SPOT_MISTAKE"
  ) {
    console.log("[Quiz] Speed round config validation:", {
      challengeId: currentChallenge.id,
      type: currentChallenge.type,
      rawItemCount: speedItemCountRaw,
      rawTimeLimit: speedTimeLimitSecondsRaw,
      finalItemCount: speedItemCount,
      finalTimeLimit: speedTimeLimitSeconds,
      passedToChild: {
        itemCount: speedItemCount,
        timeLimitSeconds: speedTimeLimitSeconds,
      },
      speedAwaitingFinish,
    });
  }

  return (
    <Background>
      {incorrectAudio ? <>{incorrectAudio}</> : null}
      {correctAudio ? <>{correctAudio}</> : null}

      <div className="h-screen flex flex-col overflow-hidden relative">
        <Header
          hearts={hearts}
          percentage={percentage}
          hasActiveSubscription={!!userSubscription?.isActive}
          referrerInfo={referrerInfo}
          onNavigateBack={handleSmartNavigation}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            width: "100%",
            zIndex: 50,
          }}
        />

        <div
          className="flex-1 flex items-center justify-center overflow-y-auto bg-transparent"
          style={{ marginTop: "80px", marginBottom: "80px" }}
        >
          <div className="flex items-center justify-center px-6 lg:px-0 py-6 w-full max-w-[900px] flex-col gap-y-12 bg-transparent">
            {currentChallenge.type !== "DRAG_AND_DROP" &&
              !isExplainerType &&
              currentChallenge.type !== "SPEAK_SEQUENCE" &&
              currentChallenge.type !== "SPEED_MATCH_AUDIO" &&
              currentChallenge.type !== "SPEED_SPOT_MISTAKE" && (
                <h1 className="text-xl lg:text-2xl lg:text-start font-bold text-neutral-700">
                  {title}
                </h1>
              )}

            <div className="w-full flex flex-col pb-8 lg:pb-12">
              {currentChallenge.type === "FILL_IN_THE_BLANK" ? (
                <FillInTheBlank
                  key={currentChallenge.id}
                  options={currentChallenge.challengeOptions}
                  onSelect={(id) => onSelect(id, "fill-in-the-blank")}
                  status={status}
                  selectedOptions={selectedOption ? [selectedOption] : []}
                  disabled={pending}
                  sentence={currentChallenge.sentence || ""}
                  question={currentChallenge.question}
                  mediaUrl={currentChallenge.mediaUrl}
                  audioSrc={currentChallenge.audioSrc}
                  mediaType={currentChallenge.mediaType}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                  optionsStacked={!!currentChallenge.optionsStacked}
                />
              ) : currentChallenge.type === "ASSIST" ? (
                <Assist
                  key={currentChallenge.id}
                  options={currentChallenge.challengeOptions}
                  onSelect={onSelect}
                  status={status}
                  selectedOption={selectedOption}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={currentChallenge.question}
                  audioSrc={currentChallenge.audioSrc}
                  sentence={currentChallenge.sentence}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                />
              ) : currentChallenge.type === "MATCHING" ? (
                <Matching
                  key={currentChallenge.id}
                  options={currentChallenge.challengeOptions}
                  onSelect={onSelect}
                  status={status}
                  selectedOption={selectedOption}
                  selectedPairOption={selectedPairOption}
                  disabled={pending}
                  matchedPairs={matchedPairs}
                  question={currentChallenge.question}
                  audioSrc={currentChallenge.audioSrc}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                  sentence={currentChallenge.sentence}
                />
              ) : currentChallenge.type === "TAP_WHAT_YOU_HEAR" ? (
                <TapWhatYouHear
                  key={currentChallenge.id}
                  options={currentChallenge.challengeOptions}
                  onSelect={(id) => onSelect(id, "left")}
                  selectedOption={selectedOption}
                  status={status}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={currentChallenge.question}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  audioSrc={currentChallenge.audioSrc}
                  sentence={currentChallenge.sentence || ""}
                />
              ) : currentChallenge.type === "IMAGE_AUDIO_SELECT" ? (
                <ImageAudioSelectComponent
                  key={currentChallenge.id}
                  options={currentChallenge.challengeOptions}
                  onSelect={(id) => onSelect(id, "left")}
                  status={status}
                  selectedOption={selectedOption}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={{
                    audioSrc: currentChallenge.audioSrc || "",
                    text: currentChallenge.question,
                  }}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                  sentence={currentChallenge.sentence}
                />
              ) : currentChallenge.type === "DRAG_AND_DROP" ? (
                <DragAndDrop
                  key={currentChallenge.id}
                  options={currentChallenge.challengeOptions}
                  onSelect={(result) => {
                    if (Array.isArray(result)) {
                      setDragAndDropSelections(result);
                    }
                  }}
                  status={status}
                  disabled={pending}
                  question={{
                    audioSrc: currentChallenge.audioSrc || "",
                    text: currentChallenge.question,
                  }}
                  type={currentChallenge.type}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                  sentence={currentChallenge.sentence}
                  attempt={dragAndDropAttempt}
                />
              ) : currentChallenge.type === "SPEAK_THIS" ? (
                <SpeakThis
                  key={currentChallenge.id}
                  options={currentChallenge.challengeOptions}
                  onSelect={onSelect}
                  status={status}
                  selectedOption={selectedOption}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={currentChallenge.question}
                  audioSrc={currentChallenge.audioSrc || ""}
                  sentence={currentChallenge.sentence || ""}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                />
              ) : currentChallenge.type === "SPEAK_THIS_ADVANCED" ? (
                (() => {
                  console.log("[Quiz] Rendering SpeakThisAdvanced challenge", {
                    challengeId: currentChallenge.id,
                    question: currentChallenge.question,
                    hasAudioSrc: !!currentChallenge.audioSrc,
                    hasSentence: !!currentChallenge.sentence,
                  });
                  return (
                    <SpeakThisAdvanced
                      key={currentChallenge.id}
                      question={currentChallenge.question}
                      audioSrc={currentChallenge.audioSrc || ""}
                      sentence={currentChallenge.sentence || ""}
                      onSpeakThisAdvancedResult={handleSpeakThisAdvancedResult}
                      options={currentChallenge.challengeOptions}
                      onSelect={onSelect}
                      status={status}
                      selectedOption={selectedOption}
                      type="SPEAK_THIS_ADVANCED"
                      disabled={pending}
                      mediaType={currentChallenge.mediaType}
                      mediaUrl={currentChallenge.mediaUrl}
                      topCardText={currentChallenge.topCardText}
                      topCardAudio={currentChallenge.topCardAudio}
                    />
                  );
                })()
              ) : currentChallenge.type === "SPEAK_SEQUENCE" ? (
                <SpeakSequence
                  key={currentChallenge.id}
                  options={currentChallenge.challengeOptions}
                  question={currentChallenge.question}
                  sentence={currentChallenge.sentence || ""}
                  audioSrc={currentChallenge.audioSrc || ""}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  onComplete={handleSpeakSequenceComplete}
                />
              ) : currentChallenge.type === "SPEED_MATCH_AUDIO" ? (
                <SpeedMatchAudio
                  key={currentChallenge.id}
                  challengeId={currentChallenge.id}
                  itemCount={speedItemCount}
                  timeLimitSeconds={speedTimeLimitSeconds}
                  onEnd={(summary) =>
                    handleSpeedRoundEnd("SPEED_MATCH_AUDIO", summary)
                  }
                />
              ) : currentChallenge.type === "SPEED_SPOT_MISTAKE" ? (
                <SpeedSpotMistake
                  key={currentChallenge.id}
                  challengeId={currentChallenge.id}
                  itemCount={speedItemCount}
                  timeLimitSeconds={speedTimeLimitSeconds}
                  onEnd={(summary) =>
                    handleSpeedRoundEnd("SPEED_SPOT_MISTAKE", summary)
                  }
                />
              ) : isExplainerType ? (
                <Explainer
                  key={currentChallenge.id}
                  question={currentChallenge.question}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  audioSrc={currentChallenge.audioSrc || undefined}
                  topCardText={currentChallenge.topCardText || undefined}
                  topCardAudio={currentChallenge.topCardAudio || undefined}
                  sentence={currentChallenge.sentence || undefined}
                />
              ) : (
                <Challenge
                  key={currentChallenge.id}
                  options={currentChallenge.challengeOptions}
                  onSelect={(id) => onSelect(id, "left")}
                  status={status}
                  selectedOption={selectedOption}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={currentChallenge.question}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  audioSrc={currentChallenge.audioSrc}
                  sentence={currentChallenge.sentence}
                />
              )}
            </div>
          </div>
        </div>

        <Footer
          disabled={footerDisabled}
          status={footerStatus}
          onCheck={onContinue}
          lessonId={lessonId}
          referrerInfo={referrerInfo}
          onSmartNavigate={handleSmartNavigation}
          style={{
            position: "fixed",
            bottom: 0,
            left: 0,
            right: 0,
            width: "100%",
            zIndex: 50,
          }}
        />
      </div>
    </Background>
  );
};

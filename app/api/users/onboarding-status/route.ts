// app/api/users/onboarding-status/route.ts

import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { users, teacherStudents } from "@/db/schema";
import { eq } from "drizzle-orm";

/**
 * This API route checks if the currently authenticated user is fully onboarded.
 * - For a 'teacher', it checks if they have school credentials set.
 * - For a 'student', it checks if they are linked to a teacher.
 * It returns a simple boolean: { isOnboarded: boolean }.
 */
export async function GET() {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userRecord = await db.query.users.findFirst({
      where: eq(users.userId, userId),
    });

    // If the user isn't even in our DB yet, they are definitely not onboarded.
    if (!userRecord) {
      return NextResponse.json({ isOnboarded: false });
    }

    // Logic for TEACHER onboarding check
    if (userRecord.role === "teacher") {
      const isTeacherOnboarded = !!(
        userRecord.schoolUsername && userRecord.schoolPassword
      );
      return NextResponse.json({ isOnboarded: isTeacherOnboarded });
    }

    // Logic for STUDENT onboarding check
    if (userRecord.role === "student") {
      const pivotRow = await db.query.teacherStudents.findFirst({
        where: eq(teacherStudents.studentId, userRecord.userId),
      });
      // The student is onboarded if a link to a teacher exists.
      return NextResponse.json({ isOnboarded: !!pivotRow });
    }

    // Default case: If the role is unknown or not set, they are not onboarded.
    return NextResponse.json({ isOnboarded: false });
  } catch (error) {
    console.error("[API /onboarding-status] Error:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

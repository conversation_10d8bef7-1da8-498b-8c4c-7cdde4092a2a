/* app/api/speed-round/[...parts]/route.ts
   One file. Handles:
     - GET  /api/speed-round/items?challengeId=...&limit=...&kind=match_audio|spot_mistake
     - POST /api/speed-round/session/start
     - POST /api/speed-round/attempt
     - POST /api/speed-round/session/end
*/

import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { sql } from "drizzle-orm";

/** Small helper to normalize db.execute() return shape across drivers */
function rowsOf<T = any>(res: any): T[] {
  // drizzle on pg: res.rows; on sqlite/libsql: res (array)
  if (res && Array.isArray(res)) return res as T[];
  if (res && "rows" in res && Array.isArray(res.rows)) return res.rows as T[];
  return [];
}

/** Build JSON error */
function badRequest(message: string, extra?: Record<string, any>) {
  return NextResponse.json({ error: message, ...extra }, { status: 400 });
}

export async function GET(req: Request, ctx: { params: { parts?: string[] } }) {
  // Define the RoundItem interface once at the top of the function for clarity
  interface RoundItem {
    item: {
      id: number;
      challengeId: number;
      kind: "match_audio" | "spot_mistake";
      audioSrc: string;
      audioText?: string | null;
      displayText?: string | null;
      isMatch?: boolean | null;
      sequence?: number | null;
      active?: boolean | null;
    };
    options: {
      id: number;
      speedItemId: number;
      text: string;
      correct: boolean;
    }[];
  }

  try {
    const url = new URL(req.url);
    const parts = ctx?.params?.parts ?? [];
    const seg0 = (parts[0] || "").toLowerCase();

    // Only endpoint we support via GET is "items"
    if (seg0 !== "items") {
      return badRequest("Unknown GET endpoint.", { path: parts });
    }

    // STEP 12: Enhanced parameter validation
    const challengeIdRaw = url.searchParams.get("challengeId") || "";
    const challengeId = Number(challengeIdRaw);

    if (
      !challengeIdRaw ||
      !challengeId ||
      Number.isNaN(challengeId) ||
      challengeId <= 0
    ) {
      console.error("[speed-round][GET] Invalid challengeId", {
        raw: challengeIdRaw,
        parsed: challengeId,
      });
      return badRequest(
        "Missing or invalid challengeId. Must be a positive integer."
      );
    }

    const limitRaw = url.searchParams.get("limit") || "20";
    const limitParsed = Number(limitRaw);
    const limit = Math.min(100, Math.max(1, limitParsed));

    // STEP 12: Log warning if limit was corrected
    if (limitParsed !== limit) {
      console.warn("[speed-round][GET] Invalid limit corrected", {
        raw: limitRaw,
        parsed: limitParsed,
        corrected: limit,
      });
    }

    const kind = (url.searchParams.get("kind") || "match_audio").toLowerCase();

    // Ensure `kind` is one of the allowed values
    if (kind !== "match_audio" && kind !== "spot_mistake") {
      return badRequest(
        "Invalid 'kind' parameter. Must be 'match_audio' or 'spot_mistake'."
      );
    }

    console.log("[speed-round][GET] Fetching items", {
      challengeId,
      limit,
      kind,
    });

    // Fetch speed items + their options if any.
    const res = await db.execute(sql`
      SELECT
        si.id                AS item_id,
        si.challenge_id      AS challenge_id,
        si.kind              AS kind,
        si.audio_src         AS audio_src,
        si.audio_text        AS audio_text,
        si.display_text      AS display_text,
        si.is_match          AS is_match,
        si.sequence          AS sequence,
        si.active            AS active,
        co.id                AS option_id,
        co.text              AS option_text,
        co.correct           AS option_correct
      FROM speed_items si
      LEFT JOIN challenge_options co
        ON co.speed_item_id = si.id
      WHERE
        si.challenge_id = ${challengeId}
        AND si.kind = ${kind}
        AND (si.active IS NULL OR si.active = TRUE)
      ORDER BY si.sequence NULLS LAST, si.id ASC, co.id ASC
      LIMIT ${limit * 6}
    `);

    const flat = rowsOf(res);

    console.log("[speed-round][GET] Raw database results", {
      challengeId,
      rowsFetched: flat.length,
    });

    const byItem = new Map<number, RoundItem>();
    for (const r of flat) {
      if (!r.item_id) continue;

      if (!byItem.has(r.item_id)) {
        byItem.set(r.item_id, {
          item: {
            id: r.item_id,
            challengeId: r.challenge_id,
            kind: (r.kind || "match_audio") as "match_audio" | "spot_mistake",
            audioSrc: r.audio_src || "",
            audioText: r.audio_text ?? null,
            displayText: r.display_text ?? null,
            isMatch: typeof r.is_match === "boolean" ? r.is_match : null,
            sequence: r.sequence ?? null,
            active: r.active ?? null,
          },
          options: [],
        });
      }
      if (r.option_id) {
        byItem.get(r.item_id)!.options.push({
          id: r.option_id,
          speedItemId: r.item_id,
          text: r.option_text ?? "",
          correct: !!r.option_correct,
        });
      }
    }

    // ================== START: TYPESCRIPT-SAFE VALIDATION ==================
    let roundItems: RoundItem[];

    if (kind === "spot_mistake") {
      roundItems = Array.from(byItem.values())
        .map((ri): RoundItem | null => {
          const { item } = ri;
          if (
            !item.audioSrc ||
            !item.displayText ||
            typeof item.isMatch !== "boolean"
          ) {
            return null;
          }
          return { item, options: [] };
        })
        .filter((i): i is RoundItem => i !== null)
        .slice(0, limit);
    } else {
      roundItems = Array.from(byItem.values())
        .map((ri): RoundItem | null => {
          const correctCount = ri.options.filter((o) => o.correct).length;
          if (correctCount !== 1 || ri.options.length < 4) {
            return null;
          }
          return {
            ...ri,
            options: ri.options.slice(0, 4),
          };
        })
        .filter((i): i is RoundItem => i !== null)
        .slice(0, limit);
    }
    // =================== END: TYPESCRIPT-SAFE VALIDATION ===================

    const totalFetched = byItem.size;
    const totalValid = roundItems.length;

    console.log("[speed-round][GET] Items validation", {
      challengeId,
      totalFetched,
      totalValid,
      limit,
      hasValidItems: totalValid > 0,
    });

    if (totalValid === 0) {
      console.error("[speed-round][GET] No valid items found", {
        challengeId,
        totalFetched,
        kind,
      });
      return NextResponse.json(
        {
          error: "No valid items found for this challenge.",
          metadata: {
            challengeId,
            totalFetched,
            totalValid: 0,
            reason:
              totalFetched === 0
                ? "No items in database for this kind"
                : "Items exist but failed validation for this challenge type",
          },
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        items: roundItems,
        metadata: {
          totalFetched,
          totalValid,
          limit,
          challengeId,
        },
      },
      { status: 200 }
    );
  } catch (err: any) {
    console.error("[speed-round][GET] error:", err);
    return NextResponse.json(
      { error: "Failed to load items." },
      { status: 500 }
    );
  }
}

export async function POST(
  req: Request,
  ctx: { params: { parts?: string[] } }
) {
  const parts = ctx?.params?.parts ?? [];
  const seg0 = (parts[0] || "").toLowerCase();
  const seg1 = (parts[1] || "").toLowerCase();

  try {
    if (seg0 === "session" && seg1 === "start") {
      const body = await safeJson(req);
      const challengeId = Number(body?.challengeId || "");

      if (!challengeId || Number.isNaN(challengeId) || challengeId <= 0) {
        console.error("[speed-round][POST session/start] Invalid challengeId", {
          body,
          challengeId,
        });
        return badRequest(
          "Missing or invalid challengeId. Must be a positive integer."
        );
      }

      const { userId } = await auth();
      let sessionId: number | null = null;

      console.log("[speed-round][POST session/start] Starting session", {
        userId,
        challengeId,
      });

      try {
        const res = await db.execute(sql`
          INSERT INTO speed_sessions (user_id, challenge_id, started_at)
          VALUES (${userId || null}, ${challengeId}, NOW())
          RETURNING id
        `);
        const r = rowsOf(res)[0];
        sessionId = r?.id ?? null;
        console.log("[speed-round][POST session/start] Session created", {
          sessionId,
        });
      } catch (e) {
        console.warn("[speed-round] session.start: no sessions table?", e);
      }

      return NextResponse.json({ sessionId }, { status: 200 });
    }

    if (seg0 === "attempt") {
      const body = await safeJson(req);
      const sessionId = body?.sessionId ?? null;
      const speedItemId = Number(body?.speedItemId || "");
      const chosen = String(body?.chosen ?? "");
      const isCorrect = !!body?.isCorrect;
      const latencyMs = Math.max(0, Number(body?.latencyMs || 0));

      if (!speedItemId || Number.isNaN(speedItemId) || speedItemId <= 0) {
        console.error("[speed-round][POST attempt] Invalid speedItemId", {
          body,
          speedItemId,
        });
        return badRequest(
          "Missing or invalid speedItemId. Must be a positive integer."
        );
      }

      try {
        await db.execute(sql`
          INSERT INTO speed_attempts (session_id, speed_item_id, chosen, is_correct, latency_ms, created_at)
          VALUES (${sessionId}, ${speedItemId}, ${chosen}, ${isCorrect}, ${latencyMs}, NOW())
        `);
      } catch (e) {
        console.warn("[speed-round] attempt: no attempts table?", e);
      }

      return NextResponse.json({ ok: true }, { status: 200 });
    }

    if (seg0 === "session" && seg1 === "end") {
      const body = await safeJson(req);
      const sessionId = body?.sessionId ?? null;
      const challengeId = Number(body?.challengeId || "");
      const durationMs = Math.max(0, Number(body?.durationMs || 0));
      const score = Math.max(0, Number(body?.score || 0));
      const itemsSeen = Math.max(0, Number(body?.itemsSeen || 0));
      const itemsCorrect = Math.max(0, Number(body?.itemsCorrect || 0));
      const streakMax = Math.max(0, Number(body?.streakMax || 0));
      const deviceType = String(body?.deviceType || "unknown");

      if (!challengeId || Number.isNaN(challengeId) || challengeId <= 0) {
        console.error("[speed-round][POST session/end] Invalid challengeId", {
          body,
          challengeId,
        });
        return badRequest(
          "Missing or invalid challengeId. Must be a positive integer."
        );
      }

      console.log("[speed-round][POST session/end] Ending session", {
        sessionId,
        challengeId,
        score,
        itemsSeen,
        itemsCorrect,
        streakMax,
        durationMs,
        deviceType,
      });

      try {
        if (sessionId) {
          await db.execute(sql`
            UPDATE speed_sessions
              SET ended_at = NOW(),
                  duration_ms = ${durationMs},
                  score = ${score},
                  items_seen = ${itemsSeen},
                  items_correct = ${itemsCorrect},
                  streak_max = ${streakMax},
                  device_type = ${deviceType}
            WHERE id = ${sessionId}
          `);
          console.log("[speed-round][POST session/end] Session updated", {
            sessionId,
          });
        } else {
          await db.execute(sql`
            INSERT INTO speed_sessions
              (user_id, challenge_id, started_at, ended_at, duration_ms, score, items_seen, items_correct, streak_max, device_type)
            VALUES
              (NULL, ${challengeId}, NOW(), NOW(), ${durationMs}, ${score}, ${itemsSeen}, ${itemsCorrect}, ${streakMax}, ${deviceType})
          `);
          console.log(
            "[speed-round][POST session/end] Summary-only session created"
          );
        }
      } catch (e) {
        console.warn("[speed-round] session.end: no sessions table?", e);
      }

      return NextResponse.json({ ok: true }, { status: 200 });
    }

    return badRequest("Unknown POST endpoint.", { path: parts });
  } catch (err: any) {
    console.error("[speed-round][POST] error:", err);
    return NextResponse.json({ error: "Request failed." }, { status: 500 });
  }
}

/** Safe JSON reader (returns {} if no/invalid body) */
async function safeJson(req: Request): Promise<any> {
  try {
    const text = await req.text();
    if (!text) return {};
    return JSON.parse(text);
  } catch {
    return {};
  }
}

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

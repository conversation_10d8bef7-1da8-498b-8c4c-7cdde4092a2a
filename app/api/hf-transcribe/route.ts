// app/api/hf-transcribe/route.ts
import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";
import { randomUUID } from "crypto";
import { exec as execCb } from "child_process";
import { promisify } from "util";

export const runtime = "nodejs";

const exec = promisify(execCb);
const TMP_DIR = "/tmp";
const HF_TOKEN = process.env.HF_TOKEN || "";
const HF_MODEL_URL =
  process.env.HF_MODEL_URL ||
  "https://eia0srs00ma9qiy.eu-west-1.aws.endpoints.huggingface.cloud";
const HF_TIMEOUT_MS = Number(process.env.HF_TIMEOUT_MS ?? 120_000);

const PCM_MIME = new Set(["audio/wav", "audio/x-wav", "audio/flac"]);
const SUPPORTED_AUDIO_MIME = new Set([
  "audio/x-flac",
  "audio/flac",
  "audio/mpeg",
  "audio/x-mpeg-3",
  "audio/wave",
  "audio/wav",
  "audio/x-wav",
  "audio/ogg",
  "audio/x-audio",
  "audio/webm",
  "audio/webm;codecs=opus",
  "audio/mp3",
  "application/octet-stream",
]);

// ------------- LOGGING HELPERS -------------
function nowNs() {
  return typeof process !== "undefined" && (process as any).hrtime?.bigint
    ? ((process as any).hrtime.bigint() as bigint)
    : BigInt(Date.now()) * BigInt(1e6);
}
function durMs(startNs: bigint) {
  const end = nowNs();
  return Number(end - startNs) / 1e6;
}
function redactToken(tok: string) {
  if (!tok) return "(missing)";
  if (tok.length <= 8) return "***";
  return tok.slice(0, 4) + "***" + tok.slice(-4);
}
function logKV(prefix: string, obj: Record<string, unknown>) {
  const pairs = Object.entries(obj)
    .map(([k, v]) => `${k}=${typeof v === "string" ? v : JSON.stringify(v)}`)
    .join(" | ");
  console.log(`${prefix} ${pairs}`);
}
// -------------------------------------------

function tfile(ext: string) {
  return path.join(
    TMP_DIR,
    `aud-${randomUUID()}${ext.startsWith(".") ? ext : "." + ext}`
  );
}
function safeUnlink(p?: string | null) {
  try {
    if (p && fs.existsSync(p)) fs.unlinkSync(p);
  } catch {}
}
async function hasFfmpeg() {
  try {
    await exec("ffmpeg -version");
    return true;
  } catch {
    return false;
  }
}
async function convertToWav16kMono(inPath: string): Promise<string> {
  const outPath = tfile(".wav");
  await exec(
    `ffmpeg -y -i "${inPath}" -acodec pcm_s16le -ac 1 -ar 16000 "${outPath}"`
  );
  return outPath;
}

function extractText(result: any): string | null {
  if (!result) return null;
  if (typeof result === "string") return result;
  if (typeof result.text === "string") return result.text;
  if (Array.isArray(result)) {
    const t = result
      .map((r) => r?.text)
      .filter(Boolean)
      .join(" ")
      .trim();
    if (t) return t;
  }
  if (Array.isArray(result?.segments)) {
    const t = result.segments
      .map((s: any) => s?.text)
      .filter(Boolean)
      .join(" ")
      .trim();
    if (t) return t;
  }
  return null;
}

export async function POST(req: NextRequest) {
  const reqId = randomUUID();
  const startAll = nowNs();

  // Request summary (safe)
  try {
    logKV("[hf] ⇢ START", {
      reqId,
      ua: req.headers.get("user-agent") || "(none)",
      ct: req.headers.get("content-type") || "(none)",
      modelUrlHost: (() => {
        try {
          return new URL(HF_MODEL_URL).host;
        } catch {
          return "(bad-url)";
        }
      })(),
      hfToken: redactToken(HF_TOKEN),
      timeoutMs: HF_TIMEOUT_MS,
    });
  } catch {}

  if (!HF_TOKEN || !HF_MODEL_URL) {
    console.error("[hf]", reqId, "ENV MISSING", {
      hasToken: !!HF_TOKEN,
      hasUrl: !!HF_MODEL_URL,
    });
    return NextResponse.json({ error: "HF env vars missing" }, { status: 500 });
  }

  let saved: string | null = null;
  let converted: string | null = null;

  try {
    const tFD = nowNs();
    const fd = await req.formData();
    const file = (fd.get("file") as File) || (fd.get("audio") as File);

    if (!file) {
      console.warn("[hf]", reqId, "no file field in form-data");
      return NextResponse.json({ error: "No audio provided" }, { status: 400 });
    }

    let mime = (file.type || "application/octet-stream").toLowerCase();
    const buf = Buffer.from(await file.arrayBuffer());

    logKV("[hf] form-data", {
      reqId,
      parseMs: durMs(tFD).toFixed(1),
      fieldName: file ? (file as any).name || "(unnamed)" : "(none)",
      mime,
      bytesIn: buf.byteLength,
    });

    if (buf.byteLength < 300) {
      console.warn("[hf]", reqId, "short upload");
      return NextResponse.json(
        { error: "Recording too short" },
        { status: 400 }
      );
    }

    // Persist upload to disk
    let ext = ".bin";
    if (mime.includes("wav")) ext = ".wav";
    else if (mime.includes("flac")) ext = ".flac";
    else if (mime.includes("webm")) {
      ext = ".webm";
      mime = "audio/webm"; // Normalize for HF
    } else if (mime.includes("ogg")) ext = ".ogg";
    else if (mime.includes("mp3")) ext = ".mp3";

    saved = tfile(ext);
    fs.writeFileSync(saved, buf);
    logKV("[hf] saved", { reqId, saved });

    // Optional conversion to WAV if ffmpeg available and mime not directly supported or non-PCM
    let sendPath = saved;
    let sendMime = mime;
    const canSendBinary =
      SUPPORTED_AUDIO_MIME.has(sendMime) || PCM_MIME.has(sendMime);
    let useConversion = false;

    if (!canSendBinary || !PCM_MIME.has(sendMime)) {
      const haveFfmpeg = await hasFfmpeg();
      logKV("[hf] ffmpeg-check", {
        reqId,
        haveFfmpeg,
        mime,
        canSendBinary,
        isPcm: PCM_MIME.has(sendMime),
      });

      if (haveFfmpeg) {
        useConversion = true;
        const tConv = nowNs();
        try {
          converted = await convertToWav16kMono(saved);
          sendPath = converted;
          sendMime = "audio/wav";
          logKV("[hf] ffmpeg-converted", {
            reqId,
            ms: durMs(tConv).toFixed(1),
            converted,
            sendMime,
          });
        } catch (e) {
          console.warn(
            "[hf]",
            reqId,
            "ffmpeg convert failed, fallback to JSON",
            String(e).slice(0, 200)
          );
          useConversion = false; // Proceed to JSON fallback
        }
      } else {
        logKV("[hf] ffmpeg-missing", { reqId, note: "will try JSON fallback" });
      }
    }

    const controller = new AbortController();
    const tAbort = setTimeout(() => controller.abort(), HF_TIMEOUT_MS);

    const bytes = fs.readFileSync(sendPath);
    logKV("[hf] outbound", {
      reqId,
      sendMime,
      bytes: bytes.byteLength,
      converted: Boolean(converted),
      mode:
        canSendBinary && !useConversion
          ? "binary"
          : useConversion
          ? "binary(converted)"
          : "json",
    });

    let res: Response;
    const tHF = nowNs();

    if (canSendBinary || useConversion) {
      // Send raw binary with audio/* Content-Type
      res = await fetch(HF_MODEL_URL, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${HF_TOKEN}`,
          "Content-Type": sendMime,
          Accept: "application/json",
        },
        body: bytes,
        signal: controller.signal,
      }).finally(() => clearTimeout(tAbort));
    } else {
      // Fallback to JSON base64 (simple shape)
      res = await fetch(HF_MODEL_URL, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${HF_TOKEN}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          inputs: bytes.toString("base64"),
          parameters: {},
        }),
        signal: controller.signal,
      }).finally(() => clearTimeout(tAbort));
    }

    logKV("[hf] inbound", {
      reqId,
      status: res.status,
      ok: res.ok,
      hfMs: durMs(tHF).toFixed(1),
      ct: res.headers.get("content-type") || "(none)",
    });

    if (!res.ok) {
      const txt = await res.text();
      console.warn("[hf]", reqId, "HF error body:", txt.slice(0, 500));
      return NextResponse.json(
        { error: `HF error ${res.status}: ${txt.slice(0, 500)}` },
        { status: res.status }
      );
    }

    let data: any;
    try {
      data = await res.json();
      const preview =
        typeof data === "string"
          ? data.slice(0, 80)
          : JSON.stringify(data).slice(0, 160);
      logKV("[hf] json", { reqId, preview });
    } catch {
      const txt = await res.text();
      data = { text: txt };
      logKV("[hf] text-body", { reqId, preview: txt.slice(0, 160) });
    }

    const text = extractText(data) ?? "";
    console.log(
      "[hf-transcribe]",
      reqId,
      "text.len:",
      text.length,
      "text.preview:",
      text.slice(0, 80)
    );

    const response = {
      text,
      meta: {
        inputMime: mime,
        sentMime: sendMime,
        bytes: bytes.byteLength,
        converted: Boolean(converted),
        mode:
          canSendBinary && !useConversion
            ? "binary"
            : useConversion
            ? "binary (converted)"
            : "json",
        reqId,
        totalMs: Number(durMs(startAll).toFixed(1)),
      },
    };

    logKV("[hf] ⇠ END", response.meta);
    return NextResponse.json(response);
  } catch (e: any) {
    console.error("[hf]", reqId, "route error:", e);
    const msg =
      e?.name === "AbortError"
        ? "HF request timed out"
        : e?.message || "Server error";
    logKV("[hf] ⇠ END_ERROR", {
      reqId,
      msg,
      totalMs: durMs(startAll).toFixed(1),
    });
    return NextResponse.json(
      { error: msg, meta: { reqId } },
      { status: e?.name === "AbortError" ? 504 : 500 }
    );
  } finally {
    safeUnlink(saved);
    safeUnlink(converted);
  }
}

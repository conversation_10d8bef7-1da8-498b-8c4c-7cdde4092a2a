// app/virtualClassroom/page.tsx
import { auth } from "@clerk/nextjs/server";
import { isTeacher } from "@/lib/users"; // <-- getOrCreateUser is no longer imported
import Link from "next/link";
import nextDynamic from "next/dynamic";
import {
  getChapterWithSquaresByOrder,
  getChapterWithSquares,
} from "@/db/queries";

// ADDED: We import the EventProvider
import { EventProvider } from "@/app/(main)/schedule/EventContext";

// ADJUSTED: We no longer always import MeetingEventsList at the top-level
import MeetingEventsList from "./components/MeetingEventsList";

export const dynamic = "force-dynamic";

const VirtualClassroomClient = nextDynamic(
  () => import("./components/VirtualClassroomClient"),
  {
    ssr: false,
  }
);

export default async function VirtualClassroomPage({
  searchParams,
}: {
  searchParams: { chapter?: string; room?: string; title?: string };
}) {
  const { userId } = auth();
  // REMOVED the blocking getOrCreateUser() call:
  // if (userId) {
  //   await getOrCreateUser();
  // }

  const teacher = await isTeacher();
  const chapterOrder = searchParams.chapter
    ? parseInt(searchParams.chapter, 10)
    : 1;

  // Keep this line (not removing it as per instructions)
  let chapterData = await getChapterWithSquares(chapterOrder);
  // Now fetch by order-based approach
  const chapterDataByOrder = await getChapterWithSquaresByOrder(chapterOrder);

  // Overwrite chapterData with order-based result
  chapterData = chapterDataByOrder;

  const transformedChapterData = chapterData
    ? {
        ...chapterData,
        squares: chapterData.squares.map((sq) => ({
          ...sq,
          transliteration: sq.transliteration ?? "",
          group: "basic" as "basic",
          audioUrl: "",
          overlayContent: "",
        })),
        // Ensure we have 'order' field available
        order: chapterData.order || chapterOrder,
      }
    : null;

  // Check for a 'room' param to decide which UI to show
  console.log(
    "[VirtualClassroomPage] Checking searchParams.room:",
    searchParams.room
  );

  // If no room chosen, show the MeetingEventsList
  if (!searchParams.room) {
    return (
      <div
        style={{
          position: "relative",
          height: "100vh",
          width: "100vw",
          background: "#fff",
        }}
      >
        <Link
          href="/home"
          style={{
            position: "absolute",
            top: "10px",
            left: "10px",
            fontSize: "24px",
            color: "#000",
            textDecoration: "none",
            background: "#eee",
            borderRadius: "50%",
            width: "40px",
            height: "40px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            lineHeight: "40px",
          }}
          title="Return to home"
        >
          ×
        </Link>

        <EventProvider>
          {/* Show the list of upcoming meetings */}
          <MeetingEventsList
            isTeacher={teacher}
            // We'll rely on its internal router.push() call if the user clicks "Join"
          />
        </EventProvider>
      </div>
    );
  }

  // Extract title from searchParams to pass as jitsiSubject
  const title = searchParams.title;

  // If a room param is found, render the VirtualClassroom
  return (
    <div
      style={{
        position: "relative",
        height: "100vh",
        width: "100vw",
        background: "#fff",
      }}
    >
      <Link
        href="/home"
        style={{
          position: "absolute",
          top: "10px",
          left: "10px",
          fontSize: "24px",
          color: "#000",
          textDecoration: "none",
          background: "#eee",
          borderRadius: "50%",
          width: "40px",
          height: "40px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          lineHeight: "40px",
        }}
        title="Return to home"
      >
        ×
      </Link>

      <EventProvider>
        {/* Now we show the actual VirtualClassroomClient */}
        <VirtualClassroomClient
          isTeacher={teacher}
          userId={userId || ""}
          chapterData={transformedChapterData}
          // We pass the chosen room and the title as jitsiSubject
          roomName={searchParams.room}
          jitsiSubject={title || undefined}
        />
      </EventProvider>
    </div>
  );
}

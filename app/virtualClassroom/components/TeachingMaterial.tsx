// TeachingMaterial.tsx

"use client";

import React from "react";
import Image from "next/image";

interface TeachingMaterialProps {
  onSelectCategory: (category: string) => void;
}

interface Card {
  id: string;
  title: string;
  category: string;
  image?: string;
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface Section {
  title: string;
  cards: Card[];
}

const teachingSections: Section[] = [
  {
    title: "Foundational Learning",
    cards: [
      {
        id: "nq-1",
        title: "Noorani Qaida",
        image: "/NooraniQaida.png",
        category: "Noorani Qaida",
      },
      {
        id: "nq-2",
        title: "Iqro Books",
        image: "/Iqro.png",
        category: "Noorani Qaida",
      },
    ],
  },
  {
    title: "Advanced Studies",
    cards: [
      {
        id: "q-1",
        title: "Quran",
        image: "/Quran.png",
        category: "Quran",
      },
    ],
  },
];

const TeachingMaterial: React.FC<TeachingMaterialProps> = ({
  onSelectCategory,
}) => {
  const customBlack = "#1a1a18";

  // --- MODIFICATION: Flatten all cards into a single array ---
  const allCards = teachingSections.flatMap((section) => section.cards);

  const handleCardClick = (card: Card) => {
    if (card.disabled) return;
    onSelectCategory(card.category);
  };

  return (
    <div className="flex flex-col h-full overflow-y-auto bg-gray-50 p-6 md:p-8 lg:p-12">
      <div className="max-w-7xl mx-auto w-full">
        <header className="text-center mb-16">
          <h1
            style={{ color: customBlack }}
            className="text-2xl md:text-2xl font-bold tracking-tight mb-3"
          >
            Teaching Material
          </h1>
          <p className="text-lg text-gray-500 max-w-2xl mx-auto">
            Select a resource from the library to begin the lesson.
          </p>
        </header>

        {/* --- MODIFICATION: Render a single main grid instead of mapping sections --- */}
        <main>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {allCards.map((card) => (
              <div
                key={card.id}
                onClick={() => handleCardClick(card)}
                className={`group relative rounded-xl overflow-hidden transition-all duration-300 ease-in-out ${
                  card.disabled
                    ? "opacity-50 grayscale cursor-not-allowed bg-white"
                    : "cursor-pointer bg-white shadow-sm border border-gray-200/80 hover:shadow-xl hover:border-transparent hover:-translate-y-1.5"
                }`}
              >
                <div className="aspect-video w-full flex items-center justify-center bg-gray-100/70 p-4">
                  {card.image && (
                    <Image
                      src={card.image}
                      alt={card.title}
                      width={160}
                      height={110}
                      style={{ objectFit: "contain" }}
                    />
                  )}
                  {card.icon && card.icon}
                </div>
                <div className="p-5">
                  <h3
                    style={{ color: customBlack }}
                    className="text-lg font-semibold truncate"
                  >
                    {card.title}
                  </h3>
                  {!card.disabled && (
                    <div className="text-sm text-gray-400 mt-1">
                      Select to start
                    </div>
                  )}
                  {card.disabled && (
                    <div className="text-sm text-gray-400 mt-1">
                      Coming soon
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </main>
      </div>
    </div>
  );
};

export default TeachingMaterial;

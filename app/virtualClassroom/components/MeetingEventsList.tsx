"use client";

import React, { useContext, useMemo } from "react";
import { EventContext } from "@/app/(main)/schedule/EventContext";
import { getSocket } from "../../../lib/socketClient";
import { useRouter } from "next/navigation";
import { Video, Clock, ChevronRight, CheckCircle } from "lucide-react";
import { Button } from "@/components/shadcn-ui/button";
import { Card, CardContent } from "@/components/shadcn-ui/card";
import { motion } from "framer-motion";

interface MeetingEventsListProps {
  isTeacher: boolean;
  onJoin?: (roomName: string) => void;
}

interface CalendarEvent {
  id?: number;
  title: string;
  description?: string;
  startTime?: string;
  endTime?: string;
  isMeeting?: boolean;
  jitsiRoomName?: string;
}

// --- NEW HELPER COMPONENT FOR STATUS BADGES ---
const MeetingStatusBadge = ({
  status,
}: {
  status: "in-progress" | "completed" | "upcoming";
}) => {
  if (status === "in-progress") {
    return (
      <div className="flex items-center space-x-2 px-3 py-1 bg-gray-200 rounded-full">
        <span className="relative flex h-2 w-2">
          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-gray-600 opacity-75"></span>
          <span className="relative inline-flex rounded-full h-2 w-2 bg-gray-700"></span>
        </span>
        <span className="text-xs font-medium text-gray-800 capitalize">
          In Progress
        </span>
      </div>
    );
  }

  if (status === "completed") {
    return (
      <div className="flex items-center space-x-1.5 px-3 py-1 bg-gray-100 rounded-full">
        <CheckCircle className="w-3.5 h-3.5 text-gray-500" />
        <span className="text-xs font-medium text-gray-600 capitalize">
          Completed
        </span>
      </div>
    );
  }

  // Default to upcoming
  return (
    <div className="flex items-center space-x-1.5 px-3 py-1 bg-gray-50 rounded-full">
      <span className="text-xs font-medium text-gray-500 capitalize">
        Upcoming
      </span>
    </div>
  );
};

export default function MeetingEventsList({
  isTeacher,
  onJoin,
}: MeetingEventsListProps) {
  const { events } = useContext(EventContext);
  const socket = getSocket();
  const router = useRouter();

  const meetingEvents = useMemo(
    () => events.filter((evt) => evt.isMeeting),
    [events]
  );

  const getMeetingStatus = (
    startTime?: string,
    endTime?: string
  ): "in-progress" | "completed" | "upcoming" => {
    if (!startTime || !endTime) return "upcoming";
    const now = new Date();
    const start = new Date(startTime);
    const end = new Date(endTime);

    if (now < start) return "upcoming";
    if (now > end) return "completed";
    return "in-progress";
  };

  const handleJoinMeeting = (roomName: string, title: string) => {
    console.log("[MeetingEventsList] Joining meeting:", { roomName, title });

    if (onJoin) {
      onJoin(roomName);
      return;
    }

    if (isTeacher) {
      socket.emit("meeting-started", { roomName });
    }

    console.log(
      `[MeetingEventsList] Navigating to /virtualClassroom?room=${roomName}&title=${encodeURIComponent(
        title
      )}`
    );
    router.push(
      `/virtualClassroom?room=${roomName}&title=${encodeURIComponent(title)}`
    );
  };

  if (meetingEvents.length === 0) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold text-[#1a1a18]">
            Upcoming Meetings
          </h2>
          <Video className="w-6 h-6 text-gray-400" />
        </div>
        <div className="flex flex-col items-center justify-center h-48 bg-gray-50 rounded-2xl border border-gray-200">
          <p className="text-gray-600">No meetings scheduled.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto w-full">
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-3xl font-bold text-[#1a1a18]">Upcoming Meetings</h2>
        <Video className="w-6 h-6 text-gray-400" />
      </div>

      <div className="space-y-3">
        {meetingEvents.map((evt, index) => {
          const status = getMeetingStatus(evt.startTime, evt.endTime);

          return (
            <motion.div
              key={evt.id || index}
              initial={{ opacity: 0, scale: 0.98, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
            >
              <Card className="bg-white rounded-2xl border border-gray-200 shadow-sm hover:border-gray-300 hover:shadow-md transition-all duration-300 group">
                <CardContent className="p-5 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex flex-col">
                      <h3 className="text-lg font-semibold text-[#1a1a18]">
                        {evt.title}
                      </h3>
                      <div className="flex items-center text-sm text-gray-500 mt-1.5 space-x-4">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1.5 text-gray-400" />
                          <span>
                            {evt.startTime
                              ? new Date(evt.startTime).toLocaleTimeString([], {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                })
                              : "N/A"}
                            {" - "}
                            {evt.endTime
                              ? new Date(evt.endTime).toLocaleTimeString([], {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                })
                              : "N/A"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <MeetingStatusBadge status={status} />
                    <Button
                      onClick={() =>
                        handleJoinMeeting(
                          evt.jitsiRoomName || "NoRoomName",
                          evt.title
                        )
                      }
                      className="bg-[#1a1a18] text-white rounded-xl px-4 py-2 hover:bg-neutral-800 transition-all duration-300 transform group-hover:scale-105"
                    >
                      <Video className="w-4 h-4 mr-2" />
                      Join Meeting
                    </Button>
                    <ChevronRight className="w-5 h-5 text-gray-400 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}

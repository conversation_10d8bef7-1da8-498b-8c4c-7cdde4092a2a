"use client";

import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
} from "react";
import { useRouter } from "next/navigation";
import { getSocket } from "../../../lib/socketClient";
import JitsiMeet from "./JitsiMeet";
import ChapterPage from "./ChapterPage";
import TeachingMaterial from "./TeachingMaterial";
import { ArrowLeft } from "lucide-react";
import {
  layoutContainer,
  mainContentWrapper,
  panelClass,
} from "@/lib/responsiveUtilities";
import Draw from "./draw";

// --- FIXED IMPORT PATHS ---
import SurahGrid from "@/app/(main)/surahGrid/SurahGrid";
import SurahDisplay from "@/app/memorization/SurahDisplay";
import type { Surah } from "@/app/(main)/surahGrid/page"; // Fixed type import from the correct file
// --- END FIXED IMPORTS ---

// --- NEW TYPE DEFINITIONS ---
type Verse = {
  surahNumber: number;
  verseNumber: number;
  text: string;
};

// --- RANGE SELECTION CONSTANTS ---
const DYNAMIC_FETCH_THRESHOLD = 50; // If Surah is longer, we show a range picker
const VERSES_PER_RANGE = 50; // The chunk size for each selectable range

type VerseRange = {
  start: number;
  end: number;
};

interface AnnotationData {
  userId: string;
  positions: { x: number; y: number }[];
}

interface Square {
  id: number;
  squareNumber: number;
  content: string;
  transliteration: string;
  audioUrl?: string;
  group: "basic" | "intermediate" | "advanced";
  overlayContent?: string;
}

interface ChapterData {
  id: number;
  title: string;
  squares: Square[];
  order?: number;
}

interface VirtualClassroomClientProps {
  isTeacher: boolean;
  userId: string;
  chapterData: ChapterData | null;
  canAnnotate?: boolean;
  annotations?: AnnotationData[];
  roomName?: string;
  jitsiSubject?: string;
}

export default function VirtualClassroomClient({
  isTeacher,
  userId,
  chapterData,
  canAnnotate = false,
  annotations = [],
  roomName,
  jitsiSubject,
}: VirtualClassroomClientProps) {
  const router = useRouter();

  const [selectedComponent, setSelectedComponent] =
    useState("TeachingMaterial");
  const [localCanAnnotate, setLocalCanAnnotate] = useState(canAnnotate);
  const [localAnnotations, setLocalAnnotations] = useState(annotations);
  const [currentChapterData, setCurrentChapterData] = useState(chapterData);
  const [isLoadingChapter, setIsLoadingChapter] = useState(false);

  // --- NEW STATE VARIABLES for SURAH GRID & DISPLAY ---
  const [isRightPanelLoading, setIsRightPanelLoading] =
    useState<boolean>(false);
  const [allSurahs, setAllSurahs] = useState<Surah[]>([]);
  const [selectedSurah, setSelectedSurah] = useState<Surah | null>(null);
  const [displayedWords, setDisplayedWords] = useState<any | null>(null);

  // --- NEW RANGE SELECTION STATE VARIABLES ---
  const [selectedRange, setSelectedRange] = useState<VerseRange | null>(null);
  const [isLoadingRange, setIsLoadingRange] = useState(false);
  const [totalAyahs, setTotalAyahs] = useState(0);
  const [activeVerses, setActiveVerses] = useState<Verse[]>([]);
  // --- END NEW STATE VARIABLES ---

  // --- SCROLL-BASED MODE SWITCHING STATE ---
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const rightPanelScrollRef = useRef<HTMLDivElement>(null); // This should point to the actual scrollable element
  // --- END SCROLL STATE ---

  useEffect(() => {
    setCurrentChapterData(chapterData);
  }, [chapterData]);

  const fetchChapter = useCallback(async (order: number) => {
    try {
      setIsLoadingChapter(true);
      const timestamp = Date.now();
      const res = await fetch(`/api/chapters/${order}?t=${timestamp}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });

      if (!res.ok) {
        throw new Error(`Error fetching chapter with order=${order}`);
      }
      const data = await res.json();
      return data;
    } catch (err) {
      console.error("fetchChapter error:", err);
      return null;
    } finally {
      setIsLoadingChapter(false);
    }
  }, []);

  const [mode, setMode] = useState<"lesson" | "draw">("lesson");
  const [studentCursorAllowed, setStudentCursorAllowed] = useState(false);
  const [teacherCursor, setTeacherCursor] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [studentCursor, setStudentCursor] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [highlightedSquareId, setHighlightedSquareId] = useState<number | null>(
    null
  );

  // --- FIX: Corrected the useState declarations below ---
  const [remoteHoveredSquareId, setRemoteHoveredSquareId] = useState<
    number | null
  >(null);
  const [remoteSelectedSquareId, setRemoteSelectedSquareId] = useState<
    number | null
  >(null);
  // --- END FIX ---

  const socket = getSocket();
  const containerRef = useRef<HTMLDivElement>(null);
  const [tool, setTool] = useState<"pen" | "eraser">("pen");
  const [color, setColor] = useState("#000000");
  const [lineWidth, setLineWidth] = useState(3);
  const [showTooltip, setShowTooltip] = useState<string | null>(null);

  const colors = [
    "#000000",
    "#FF3B30",
    "#FF9500",
    "#FFCC00",
    "#34C759",
    "#007AFF",
    "#AF52DE",
    "#FF2D55",
  ];

  const drawRef = useRef<any>(null);

  // --- SCROLL DETECTION HANDLERS ---
  const handleScrollStart = useCallback(() => {
    if (mode === "draw") {
      setIsScrolling(true);
    }
  }, [mode]);

  const handleScrollEnd = useCallback(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 800); // Return to draw mode after 800ms of no scrolling
  }, []);

  const handleScroll = useCallback(() => {
    handleScrollStart();
    handleScrollEnd();
  }, [handleScrollStart, handleScrollEnd]);

  const handleWheel = useCallback(
    (_e: WheelEvent) => {
      handleScrollStart();
      handleScrollEnd();
    },
    [handleScrollStart, handleScrollEnd]
  );

  const handleTouchStart = useCallback(() => {
    handleScrollStart();
    handleScrollEnd();
  }, [handleScrollStart, handleScrollEnd]);

  // Add scroll event listeners to the **actual** scrollable right panel
  useEffect(() => {
    const scroller = rightPanelScrollRef.current;
    if (!scroller) return;

    // wheel/touchstart will bubble; scroll must be on the scroller itself
    scroller.addEventListener("scroll", handleScroll, { passive: true });
    scroller.addEventListener("wheel", handleWheel, { passive: true });
    scroller.addEventListener("touchstart", handleTouchStart, {
      passive: true,
    });

    return () => {
      scroller.removeEventListener("scroll", handleScroll);
      scroller.removeEventListener("wheel", handleWheel);
      scroller.removeEventListener("touchstart", handleTouchStart);
    };
  }, [handleScroll, handleWheel, handleTouchStart]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);
  // --- END SCROLL DETECTION ---

  const handleSetMode = (newMode: "lesson" | "draw") => {
    setMode(newMode);
    // Clear any active scrolling state when manually changing modes
    setIsScrolling(false);
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    if (isTeacher) {
      socket.emit("mode-changed", { mode: newMode });
    }
  };

  // --- ENHANCEMENT: Automatically set mode when changing materials ---
  const handleCategorySelect = (category: string) => {
    if (category === "Quran") {
      setSelectedComponent("SurahGrid");
      // Mode will be set to "draw" upon Surah selection
    } else if (category === "Noorani Qaida") {
      setSelectedComponent("ChapterPage");
      handleSetMode("lesson"); // Revert to lesson mode for other materials
    } else {
      console.log("Selected category:", category);
    }
  };

  useEffect(() => {
    const fetchSurahList = async () => {
      setIsRightPanelLoading(true);
      try {
        const response = await fetch("https://api.alquran.cloud/v1/surah");
        if (!response.ok) {
          throw new Error("Failed to fetch Surah metadata");
        }
        const apiData = await response.json();
        const apiSurahs = (apiData?.data || []).map((s: any) => ({
          number: s.number,
          name: s.name,
          englishName: s.englishName,
          englishNameTranslation: s.englishNameTranslation,
          revelationPlace: s.revelationType,
          numberOfAyahs: s.numberOfAyahs,
          status: "notStarted",
        }));
        setAllSurahs(apiSurahs);
      } catch (error) {
        console.error("Failed to fetch surah list:", error);
      } finally {
        setIsRightPanelLoading(false);
      }
    };

    if (selectedComponent === "SurahGrid" && allSurahs.length === 0) {
      fetchSurahList();
    }
  }, [selectedComponent, allSurahs]);

  // --- NEW: Calculate available ranges based on surah size ---
  const availableRanges = useMemo(() => {
    if (totalAyahs <= DYNAMIC_FETCH_THRESHOLD) return [];
    const arr: VerseRange[] = [];
    let start = 1;
    while (start <= totalAyahs) {
      const end = Math.min(start + VERSES_PER_RANGE - 1, totalAyahs);
      arr.push({ start, end });
      start += VERSES_PER_RANGE;
    }
    return arr;
  }, [totalAyahs]);

  // --- NEW: Range selection request handler ---
  const handleRangeSelectionRequest = useCallback(
    async (range: VerseRange) => {
      if (!selectedSurah) {
        console.error("No surah selected for range request");
        return;
      }

      setIsLoadingRange(true);
      setSelectedRange(range);
      setDisplayedWords(null);

      try {
        console.log(
          `Fetching range: ${range.start}-${range.end} for surah ${selectedSurah.number}`
        );
        const response = await fetch(
          `/api/verses?surah=${selectedSurah.number}&start=${range.start}&end=${range.end}`
        );
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.error || `API error. Status: ${response.status}`
          );
        }
        const fetchedVerses: Verse[] = await response.json();
        console.log(
          "API response verse numbers:",
          fetchedVerses.map((v) => v.verseNumber).sort((a, b) => a - b)
        );
        if (!fetchedVerses || fetchedVerses.length === 0) {
          console.error(
            `Could not load verses for range ${range.start}-${range.end}.`
          );
          setActiveVerses([]);
          setSelectedRange(null);
        } else {
          setActiveVerses(fetchedVerses);
          console.log("Set activeVerses with count:", fetchedVerses.length);

          // Prepare displayedWords structure
          const correctWords: { [verseNumber: number]: any[] } = {};
          fetchedVerses.forEach((verse) => {
            const verseNumber = verse.verseNumber;
            const verseWords = verse.text.split(" ").map((word, wordIndex) => ({
              id: wordIndex,
              text: word,
              displayText: word,
              matched: false,
              almostMatched: false,
              highlighted: false,
              given: true,
              verseNumber,
              wordIndex,
            }));
            correctWords[verseNumber] = verseWords;
          });
          setDisplayedWords(correctWords);
        }
      } catch (err: any) {
        const errorMessage = err.message || "Unknown error";
        console.error(`Failed to load range: ${errorMessage}`);
        setActiveVerses([]);
        setSelectedRange(null);
        setDisplayedWords(null);
      } finally {
        setIsLoadingRange(false);
      }
    },
    [selectedSurah]
  );

  // --- NEW: Clear range selection handler ---
  const handleClearRangeSelection = useCallback(() => {
    setSelectedRange(null);
    setActiveVerses([]);
    setDisplayedWords(null);
  }, []);

  // --- ENHANCEMENT: Automatically enable "Draw" mode when a Surah is displayed ---
  const handleSurahSelect = async (surah: Surah) => {
    setIsRightPanelLoading(true);
    setSelectedSurah(surah);
    setSelectedComponent("SurahDisplay");
    setTotalAyahs(surah.numberOfAyahs);
    handleSetMode("draw"); // Automatically switch to draw mode

    // Reset range selection state
    setSelectedRange(null);
    setActiveVerses([]);
    setDisplayedWords(null);

    try {
      // Check if we need range selection
      if (surah.numberOfAyahs <= DYNAMIC_FETCH_THRESHOLD) {
        // Small surah - fetch all verses
        console.log(
          `Small surah (${surah.numberOfAyahs} ayahs), fetching all verses`
        );
        const response = await fetch(
          `/api/verses?surah=${surah.number}&start=1&end=${surah.numberOfAyahs}`
        );
        if (!response.ok) {
          throw new Error(
            `API error fetching verses for Surah ${surah.number}`
          );
        }
        const fetchedVerses: Verse[] = await response.json();

        const correctWords: { [verseNumber: number]: any[] } = {};
        fetchedVerses.forEach((verse) => {
          const verseNumber = verse.verseNumber;
          const verseWords = verse.text.split(" ").map((word, wordIndex) => ({
            id: wordIndex,
            text: word,
            displayText: word,
            matched: false,
            almostMatched: false,
            highlighted: false,
            given: true,
            verseNumber,
            wordIndex,
          }));
          correctWords[verseNumber] = verseWords;
        });

        setDisplayedWords(correctWords);
        setActiveVerses(fetchedVerses);
        setSelectedRange({ start: 1, end: surah.numberOfAyahs });
      } else {
        // Large surah - show range picker
        console.log(
          `Large surah (${surah.numberOfAyahs} ayahs), showing range picker`
        );
        setDisplayedWords({}); // Empty to trigger range picker
      }
    } catch (error) {
      console.error("Failed to prepare SurahDisplay:", error);
      setDisplayedWords(null);
    } finally {
      setIsRightPanelLoading(false);
    }
  };

  const toggleStudentCursorAllowed = () => {
    const newVal = !studentCursorAllowed;
    setStudentCursorAllowed(newVal);
    if (!newVal) {
      setStudentCursor(null);
    }
    socket.emit("student-cursor-allowed", { allowed: newVal });
  };

  const clearCanvas = () => {
    if (drawRef.current && drawRef.current.clearCanvas) {
      drawRef.current.clearCanvas();
    }
  };

  const handleTooltipEnter = (tooltipText: string) => {
    setShowTooltip(tooltipText);
  };
  const handleTooltipLeave = () => {
    setShowTooltip(null);
  };

  useEffect(() => {
    if (socket.connected) {
      const finalId =
        userId && userId.trim() !== "" ? userId.trim() : "student_demo_user";
      socket.emit("register-role", {
        userId: finalId,
        role: isTeacher ? "teacher" : "student",
      });
    } else {
      socket.on("connect", () => {
        const finalId =
          userId && userId.trim() !== "" ? userId.trim() : "student_demo_user";
        socket.emit("register-role", {
          userId: finalId,
          role: isTeacher ? "teacher" : "student",
        });
      });
    }

    const handleAnnotatorsUpdated = (data: any) => {
      if (Array.isArray(data.annotators) && data.annotators.includes(userId)) {
        setLocalCanAnnotate(true);
      }
    };

    const handleAnnotationUpdate = (data: {
      userId: string;
      stroke: { x: number; y: number }[];
    }) => {
      setLocalAnnotations((prev) => [
        ...prev,
        { userId: data.userId, positions: data.stroke },
      ]);
    };

    const handleClearAnnotations = (data: {
      all?: boolean;
      userId?: string;
    }) => {
      if (data.all) {
        setLocalAnnotations([]);
      } else if (data.userId) {
        setLocalAnnotations((prev) =>
          prev.filter((ann) => ann.userId !== data.userId)
        );
      }
    };

    const handleCursorUpdate = (data: {
      userId: string;
      xRatio: number;
      yRatio: number;
    }) => {
      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      const localX = rect.left + data.xRatio * rect.width;
      const localY = rect.top + data.yRatio * rect.height;
      if (data.userId !== userId) {
        if (isTeacher) {
          setStudentCursor({ x: localX, y: localY });
        } else {
          setTeacherCursor({ x: localX, y: localY });
        }
      }
    };

    const handleHighlightUpdate = (data: {
      userId: string;
      squareId: number;
    }) => {
      setHighlightedSquareId(data.squareId);
    };

    const handleSquareHovered = (data: {
      userId: string;
      squareId: number | null;
    }) => {
      if (data.userId !== userId) {
        setRemoteHoveredSquareId(data.squareId);
      }
    };

    const handleSquareSelected = (data: {
      userId: string;
      squareId: number | null;
    }) => {
      if (data.userId !== userId) {
        setRemoteSelectedSquareId(data.squareId);
      }
    };

    const handleChapterChanged = async (data: { newChapter: number }) => {
      setIsLoadingChapter(true);
      setHighlightedSquareId(null);
      setRemoteHoveredSquareId(null);
      setRemoteSelectedSquareId(null);
      const fetchedChapter = await fetchChapter(data.newChapter);
      if (fetchedChapter) {
        setCurrentChapterData(fetchedChapter);
      } else {
        console.warn("No data found for chapter:", data.newChapter);
      }
      setIsLoadingChapter(false);
    };

    const handleStudentCursorAllowed = (data: { allowed: boolean }) => {
      setStudentCursorAllowed(data.allowed);
    };

    const handleModeChanged = (data: { mode: "lesson" | "draw" }) => {
      setMode(data.mode);
    };

    socket.on("annotators-updated", handleAnnotatorsUpdated);
    socket.on("annotation-update", handleAnnotationUpdate);
    socket.on("clear-annotations", handleClearAnnotations);
    socket.on("cursor-update", handleCursorUpdate);
    socket.on("highlight-update", handleHighlightUpdate);
    socket.on("square-hovered", handleSquareHovered);
    socket.on("square-selected", handleSquareSelected);
    socket.on("chapter-changed", handleChapterChanged);
    socket.on("student-cursor-allowed", handleStudentCursorAllowed);
    socket.on("mode-changed", handleModeChanged);

    return () => {
      socket.off("connect");
      socket.off("annotators-updated", handleAnnotatorsUpdated);
      socket.off("annotation-update", handleAnnotationUpdate);
      socket.off("clear-annotations", handleClearAnnotations);
      socket.off("cursor-update", handleCursorUpdate);
      socket.off("highlight-update", handleHighlightUpdate);
      socket.off("square-hovered", handleSquareHovered);
      socket.off("square-selected", handleSquareSelected);
      socket.off("chapter-changed", handleChapterChanged);
      socket.off("student-cursor-allowed", handleStudentCursorAllowed);
      socket.off("mode-changed", handleModeChanged);
    };
  }, [
    socket,
    userId,
    isTeacher,
    router,
    fetchChapter,
    setRemoteHoveredSquareId,
    setRemoteSelectedSquareId,
  ]);

  useEffect(() => {
    if (!isTeacher) return;
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      const xInContainer = e.clientX - rect.left;
      const yInContainer = e.clientY - rect.top;
      const xRatio = xInContainer / rect.width;
      const yRatio = yInContainer / rect.height;
      socket.emit("cursor-update", { userId, xRatio, yRatio });
    };
    window.addEventListener("mousemove", handleMouseMove);
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, [isTeacher, userId, socket]);

  useEffect(() => {
    if (isTeacher) return;
    if (!studentCursorAllowed) return;
    const handleStudentMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      const xInContainer = e.clientX - rect.left;
      const yInContainer = e.clientY - rect.top;
      const xRatio = xInContainer / rect.width;
      const yRatio = yInContainer / rect.height;
      socket.emit("cursor-update", { userId, xRatio, yRatio });
    };
    window.addEventListener("mousemove", handleStudentMouseMove);
    return () => {
      window.removeEventListener("mousemove", handleStudentMouseMove);
    };
  }, [isTeacher, userId, studentCursorAllowed, socket]);

  const onChapterChanged = useCallback(
    async (newChapter: number) => {
      setIsLoadingChapter(true);
      setHighlightedSquareId(null);
      setRemoteHoveredSquareId(null);
      setRemoteSelectedSquareId(null);

      if (isTeacher) {
        socket.emit("chapter-changed", { newChapter });
      }

      const fetchedChapter = await fetchChapter(newChapter);
      if (fetchedChapter) {
        setCurrentChapterData(fetchedChapter);
      } else {
        console.warn("No data found for chapter:", newChapter);
      }
      setIsLoadingChapter(false);
    },
    [
      isTeacher,
      socket,
      fetchChapter,
      setRemoteHoveredSquareId,
      setRemoteSelectedSquareId,
    ]
  );

  const handleSquareHighlight = useCallback(
    (squareId: number) => {
      if (isTeacher) {
        socket.emit("highlight-update", { userId, squareId });
      }
    },
    [isTeacher, userId, socket]
  );

  const onSquareHover = useCallback(
    (squareId: number | null) => {
      if (isTeacher) {
        socket.emit("square-hovered", { userId, squareId });
      }
    },
    [isTeacher, userId, socket]
  );

  const onSquareSelected = useCallback(
    (squareId: number | null) => {
      if (isTeacher) {
        socket.emit("square-selected", { userId, squareId });
      }
    },
    [isTeacher, userId, socket]
  );

  // Determine effective mode (considers scrolling override) — used for UI display only
  const effectiveMode = isScrolling ? "lesson" : mode;

  const renderRightPanel = () => {
    if (isRightPanelLoading || isLoadingRange) {
      return (
        <div className="flex flex-col items-center justify-center flex-1 text-gray-500">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-sm text-center">
            {isLoadingRange ? "Loading Range..." : "Loading Quran..."}
          </p>
        </div>
      );
    }

    if (selectedComponent === "SurahGrid") {
      return <SurahGrid surahs={allSurahs} onSurahSelect={handleSurahSelect} />;
    }

    if (
      selectedComponent === "SurahDisplay" &&
      selectedSurah &&
      displayedWords
    ) {
      return (
        <SurahDisplay
          surah={selectedSurah}
          displayedWords={displayedWords}
          totalAyahs={selectedSurah.numberOfAyahs}
          onUnmatchedWordClick={() => {}}
          feedbackRefs={{}}
          difficulty="easy"
          selectedRange={selectedRange}
          availableRanges={availableRanges}
          onRangeSelectionRequest={handleRangeSelectionRequest}
          onClearRangeSelection={handleClearRangeSelection}
          enableDrawing={mode === "draw"} // keep overlay mounted in draw mode
          disableDrawingInteraction={isScrolling} // but let scroll pass through while scrolling
          userId={userId}
          isVirtualClassroom={true} // Step F: Set to true for virtual classroom context
        />
      );
    }

    if (selectedComponent === "TeachingMaterial") {
      return <TeachingMaterial onSelectCategory={handleCategorySelect} />;
    }

    if (selectedComponent === "ChapterPage") {
      if (effectiveMode === "lesson") {
        if (isLoadingChapter) {
          return (
            <div className="flex flex-col items-center justify-center flex-1 text-gray-500">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p className="text-sm text-center">Loading chapter...</p>
            </div>
          );
        }
        if (currentChapterData) {
          return (
            <ChapterPage
              key={`chapter-${currentChapterData.id}`}
              title={currentChapterData.title}
              squares={currentChapterData.squares}
              order={currentChapterData.order}
              onSquareHighlighted={handleSquareHighlight}
              highlightedSquareId={highlightedSquareId}
              isTeacher={isTeacher}
              hoveredSquareId={remoteHoveredSquareId}
              selectedSquareId={remoteSelectedSquareId}
              onSquareHover={onSquareHover}
              onSquareSelected={onSquareSelected}
              onChapterChange={onChapterChanged}
            />
          );
        }
        return (
          <div className="flex flex-col items-center justify-center flex-1 text-gray-500">
            <p className="text-sm text-center">No chapter data available</p>
          </div>
        );
      } else {
        return (
          <Draw
            ref={drawRef}
            canDraw={isTeacher ? true : studentCursorAllowed}
            userId={
              userId && userId.trim() !== ""
                ? userId.trim()
                : "student_demo_user"
            }
            externalColor={color}
            externalLineWidth={lineWidth}
            externalTool={tool}
          />
        );
      }
    }

    return (
      <div className="flex flex-col items-center justify-center flex-1 text-gray-500">
        <p className="text-lg text-center font-semibold">
          {selectedComponent} Material Coming Soon
        </p>
      </div>
    );
  };

  return (
    <div
      className={`${layoutContainer} h-screen w-screen flex flex-col text-sm`}
      style={{
        position: "relative",
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
      ref={containerRef}
    >
      <header className="bg-white border-b border-gray-200 h-16 flex-shrink-0">
        <div className="h-full px-6 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push("/home")}
              className="flex items-center justify-center w-9 h-9 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300"
              title="Return to home"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <div className="h-8 w-px bg-gray-200"></div>
          </div>

          {effectiveMode === "draw" && (
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3 relative">
                <button
                  onClick={() => {
                    setTool("pen");
                    setColor("#000000");
                    setLineWidth(3);
                  }}
                  className="relative w-10 h-10 overflow-hidden group"
                  onMouseEnter={() => handleTooltipEnter("Pencil")}
                  onMouseLeave={handleTooltipLeave}
                ></button>

                <button
                  onClick={() => {
                    setTool("pen");
                    setColor("#FFCC00");
                    setLineWidth(10);
                  }}
                  className="relative w-10 h-10 overflow-hidden group"
                  onMouseEnter={() => handleTooltipEnter("Yellow Highlighter")}
                  onMouseLeave={handleTooltipLeave}
                ></button>

                <button
                  onClick={() => {
                    setTool("pen");
                    setColor("#007AFF");
                    setLineWidth(10);
                  }}
                  className="relative w-10 h-10 overflow-hidden group"
                  onMouseEnter={() => handleTooltipEnter("Blue Highlighter")}
                  onMouseLeave={handleTooltipLeave}
                ></button>

                <button
                  onClick={() => {
                    setTool("eraser");
                  }}
                  className="relative w-10 h-10 overflow-hidden group"
                  onMouseEnter={() => handleTooltipEnter("Eraser")}
                  onMouseLeave={handleTooltipLeave}
                ></button>
              </div>
            </div>
          )}

          <div className="flex items-center gap-4">
            {isTeacher && (
              <div className="flex items-center gap-3 p-2 bg-white rounded-full shadow-sm border border-neutral-200">
                <div className="flex gap-1 bg-neutral-100 rounded-lg">
                  <button
                    onClick={() => handleSetMode("lesson")}
                    className={`px-3 py-1.5 rounded-full font-medium text-sm transition-all duration-200
                      ${
                        effectiveMode === "lesson"
                          ? "bg-neutral-900 text-white shadow-sm"
                          : "bg-transparent text-neutral-600 hover:bg-neutral-200"
                      }`}
                  >
                    Lesson
                  </button>
                  <button
                    onClick={() => handleSetMode("draw")}
                    className={`px-3 py-1.5 rounded-full font-medium text-sm transition-all duration-200
                      ${
                        effectiveMode === "draw"
                          ? "bg-neutral-900 text-white shadow-sm"
                          : "bg-transparent text-neutral-600 hover:bg-neutral-200"
                      }`}
                  >
                    Draw
                  </button>
                </div>

                {/* Visual indicator when auto-switching due to scrolling */}
                {isScrolling && mode === "draw" && (
                  <div className="text-xs text-gray-500 font-medium">
                    (Scrolling)
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <div className="relative flex items-center">
                    <input
                      id="studentCursorCheckbox"
                      type="checkbox"
                      checked={studentCursorAllowed}
                      onChange={toggleStudentCursorAllowed}
                      className="peer sr-only"
                    />
                    <div
                      onClick={toggleStudentCursorAllowed}
                      className={`w-9 h-5 rounded-full bg-gray-300 peer-checked:bg-neutral-900 flex items-center p-0.5 cursor-pointer transition-colors duration-200`}
                    >
                      <span
                        className={`h-4 w-4 rounded-full bg-white transform transition-transform duration-200 ${
                          studentCursorAllowed ? "translate-x-4" : ""
                        }`}
                      ></span>
                    </div>
                    <label
                      htmlFor="studentCursorCheckbox"
                      className="ml-2 text-sm font-medium text-neutral-700 select-none cursor-pointer"
                    >
                      Cursor Access
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>

      {showTooltip && (
        <div
          className="fixed px-2 py-1 text-xs text-white bg-gray-800 rounded shadow-lg
          pointer-events-none transform -translate-x-1/2 transition-opacity duration-200"
          style={{
            left: "50%",
            bottom: "20px",
            opacity: showTooltip ? 1 : 0,
          }}
        >
          {showTooltip}
        </div>
      )}

      <div
        className={`${mainContentWrapper} flex-1 p-4 gap-4 flex flex-col md:flex-row overflow-hidden`}
      >
        <div
          className={`${panelClass} w-full md:w-1/3 bg-white rounded-2xl shadow-sm border border-gray-200 p-4 flex flex-col`}
        >
          <div className="flex-1 min-h-0">
            <JitsiMeet
              roomName={roomName || "NoRoomName"}
              userName={userId}
              subject=" "
            />
          </div>
        </div>

        {/* Right Panel */}
        <div className="relative w-full md:w-2/3 bg-white rounded-2xl shadow-sm border border-gray-200 flex flex-col min-h-0">
          {/* Attach the scroll ref to the actual scroller so our scroll detection works reliably */}
          <div ref={rightPanelScrollRef} className="flex-1 overflow-y-auto p-4">
            {renderRightPanel()}
          </div>
        </div>
      </div>

      {isTeacher && studentCursor && (
        <div
          style={{
            position: "absolute",
            left: studentCursor.x,
            top: studentCursor.y,
            pointerEvents: "none",
            transform: "translate(-50%, -50%)",
            zIndex: 9999,
          }}
        >
          <div
            style={{
              width: 20,
              height: 20,
              borderRadius: "50%",
              backgroundColor: "blue",
              opacity: 0.7,
            }}
          />
        </div>
      )}

      {!isTeacher && teacherCursor && (
        <div
          style={{
            position: "absolute",
            left: teacherCursor.x,
            top: teacherCursor.y,
            pointerEvents: "none",
            transform: "translate(-50%, -50%)",
            zIndex: 9999,
          }}
        >
          <div
            style={{
              width: 20,
              height: 20,
              borderRadius: "50%",
              backgroundColor: "red",
              opacity: 0.7,
            }}
          />
        </div>
      )}
    </div>
  );
}

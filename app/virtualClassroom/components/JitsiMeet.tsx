"use client";

import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  Camera,
  Mic,
  MessageSquare,
  Settings,
  Users,
  Monitor,
  Phone,
  AlertCircle,
  CheckCircle,
} from "lucide-react";

const domainEnv = process.env.JITSI || "8x8.vc";

declare global {
  interface Window {
    JitsiMeetExternalAPI: any;
  }
}

interface JitsiMeetProps {
  roomName: string;
  userName?: string;
  onLeave?: () => void;
  subject?: string;
}

const JitsiMeet: React.FC<JitsiMeetProps> = ({
  roomName,
  userName = "User",
  onLeave,
  subject,
}) => {
  const jitsiContainerRef = useRef<HTMLDivElement | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [participantCount, setParticipantCount] = useState(1);
  const [meetingTime, setMeetingTime] = useState(0);
  const [isAudioMuted, setIsAudioMuted] = useState(false);
  const [isVideoMuted, setIsVideoMuted] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [inMeeting, setInMeeting] = useState(true);
  const apiRef = useRef<any>(null);
  const timerRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (inMeeting) {
      timerRef.current = setInterval(() => {
        setMeetingTime((prev) => prev + 1);
      }, 1000);
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [inMeeting]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const handleLeave = useCallback(() => {
    setInMeeting(false);
    if (onLeave) {
      onLeave();
    }
    apiRef.current?.executeCommand("hangup");
  }, [onLeave]);

  useEffect(() => {
    // Store the ref's current value in a variable inside the effect
    const jitsiContainer = jitsiContainerRef.current;

    if (
      typeof window !== "undefined" &&
      window.JitsiMeetExternalAPI &&
      inMeeting
    ) {
      try {
        const domain = domainEnv;
        const options = {
          roomName: roomName,
          parentNode: jitsiContainer, // Use the variable here
          width: "100%",
          height: "100%",
          configOverwrite: {
            displayName: userName,
            prejoinPageEnabled: false,
            startWithAudioMuted: false,
            startWithVideoMuted: false,
            disableDeepLinking: true,
          },
          interfaceConfigOverwrite: {
            TOOLBAR_BUTTONS: [
              "microphone",
              "camera",
              "closedcaptions",
              "desktop",
              "fullscreen",
              "fodeviceselection",
              "hangup",
              "profile",
              "recording",
              "livestreaming",
              "etherpad",
              "sharedvideo",
              "settings",
              "raisehand",
              "videoquality",
              "filmstrip",
              "invite",
              "feedback",
              "stats",
              "shortcuts",
              "tileview",
              "videobackgroundblur",
              "download",
              "help",
              "mute-everyone",
              "security",
            ],
            SHOW_BRAND_WATERMARK: false,
            SHOW_WATERMARK_FOR_GUESTS: false,
            SHOW_JITSI_WATERMARK: false,
            DEFAULT_LOGO_URL: "",
            DEFAULT_WELCOME_PAGE_LOGO_URL: "",
            SHOW_POWERED_BY: false,
            HIDE_DEEP_LINKING_LOGO: true,
            SHOW_ROOM_NAME: false,
          },
        };

        apiRef.current = new window.JitsiMeetExternalAPI(domain, options);

        apiRef.current.addEventListeners({
          participantJoined: () => setParticipantCount((prev) => prev + 1),
          participantLeft: () =>
            setParticipantCount((prev) => Math.max(1, prev - 1)),
          audioMuteStatusChanged: ({ muted }: { muted: boolean }) =>
            setIsAudioMuted(muted),
          videoMuteStatusChanged: ({ muted }: { muted: boolean }) =>
            setIsVideoMuted(muted),
          screenSharingStatusChanged: ({ on }: { on: boolean }) =>
            setIsScreenSharing(on),
          readyToClose: () => handleLeave(),
          videoConferenceJoined: () => {
            if (apiRef.current) {
              apiRef.current.executeCommand(
                "subject",
                subject ?? "Lesson Session"
              );
            }
          },
        });

        setTimeout(() => {
          if (apiRef.current) {
            apiRef.current.executeCommand(
              "subject",
              subject ?? "Lesson Session"
            );
          }
        }, 1000);

        setTimeout(() => {
          if (apiRef.current) {
            apiRef.current.executeCommand(
              "subject",
              subject ?? "Lesson Session"
            );
          }
        }, 3000);

        setIsLoading(false);
      } catch (err) {
        setError("Failed to initialize meeting");
        setIsLoading(false);
      }
    } else {
      if (inMeeting && !window.JitsiMeetExternalAPI) {
        setError(
          "Jitsi API not found. Ensure the script is included globally."
        );
      }
      setIsLoading(false);
    }

    return () => {
      if (apiRef.current) {
        apiRef.current.dispose();
      }
      // Use the variable in the cleanup function
      if (jitsiContainer) {
        jitsiContainer.innerHTML = "";
      }
    };
  }, [roomName, userName, inMeeting, subject, handleLeave]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
        <div className="text-center p-8">
          <div className="flex justify-center mb-4">
            <AlertCircle className="text-red-500 w-8 h-8" />
          </div>
          <p className="text-red-500 text-xl mb-4 font-semibold">{error}</p>
          <p className="text-gray-600 mb-6">
            There was a problem initializing your meeting.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!inMeeting) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
        <div className="text-center p-8">
          <div className="flex justify-center mb-4">
            <CheckCircle className="text-green-500 w-8 h-8" />
          </div>
          <h2 className="text-2xl font-bold mb-2">Call Ended</h2>
          <p className="text-gray-600 mb-6">
            You have successfully left the meeting.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
          >
            Rejoin
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 rounded-xl overflow-hidden h-full flex flex-col">
      {/* Meeting Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">{subject || "Meeting"}</h2>
            <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
              <Users className="w-4 h-4" />
              <span>
                {participantCount} participant
                {participantCount !== 1 ? "s" : ""}
              </span>
              <span>•</span>
              <span>{formatTime(meetingTime)}</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Settings className="w-5 h-5 text-gray-600" />
            </button>
            <button
              onClick={handleLeave}
              className="flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              <Phone className="w-4 h-4 mr-2" />
              Leave
            </button>
          </div>
        </div>
      </div>

      {/* Meeting Container fills remainder of parent */}
      <div className="relative flex-1">
        {isLoading && (
          <div className="absolute inset-0 bg-gray-50 flex items-center justify-center z-10">
            <div className="text-center">
              <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
              <p className="text-gray-600">Joining meeting...</p>
            </div>
          </div>
        )}
        <div
          ref={jitsiContainerRef}
          className="h-full w-full"
          style={{ overflow: "hidden" }}
        />
      </div>
    </div>
  );
};

export default JitsiMeet;

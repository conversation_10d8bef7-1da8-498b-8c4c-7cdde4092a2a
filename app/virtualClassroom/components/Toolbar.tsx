"use client";

import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useCallback,
} from "react";
import { ChromePicker } from "react-color";

// ----------------------------------------------------------------------
// (A) Interface for Toolbar props (Unchanged)
// ----------------------------------------------------------------------
interface ToolbarProps {
  selectedButton?: string | null;
  setSelectedButton?: (btn: string | null) => void;
  externalColor?: string;
  externalLineWidth?: number;
  externalTool?: "pen" | "eraser";
  onClearCanvas?: () => void;
  onSelectMarker?: (
    buttonId: string,
    newTool: "pen" | "eraser",
    newColor: string,
    newLineWidth: number,
    newOpacity?: number
  ) => void;
  onColorChange?: (newColor: string) => void;
  containerRef?: React.RefObject<HTMLDivElement>;
  size?: "medium" | "small";
}

// ----------------------------------------------------------------------
// Toolbar Component
// ----------------------------------------------------------------------
function ToolbarComponent(
  {
    selectedButton: propSelectedButton,
    setSelectedButton: propSetSelectedButton,
    externalColor,
    externalLineWidth,
    externalTool,
    containerRef,
    onSelectMarker,
    onColorChange,
    size = "medium",
  }: ToolbarProps,
  ref: React.Ref<any>
) {
  const [selectedButton, setSelectedButtonState] = useState<string | null>(
    propSelectedButton || null
  );
  const [color, setColor] = useState(externalColor || "#000000");
  const [lineWidth, setLineWidth] = useState(externalLineWidth || 3);
  const [tool, setTool] = useState<"pen" | "eraser">(externalTool || "pen");
  const [opacity, setOpacity] = useState(1.0);
  const [markerColors, setMarkerColors] = useState({
    marker1: "#0000FF",
    marker2: "#FF0000",
    marker3: "#FFFF00",
  });
  const [toolbarVisible, setToolbarVisible] = useState(true);
  const toolbarRef = useRef<HTMLDivElement | null>(null);
  const [scale, setScale] = useState(1);
  const [toolbarLayout, setToolbarLayout] = useState({
    buttonSize: "w-20",
    buttonHeight: "h-20",
    paletteButtonSize: "w-8 h-8",
    dividerHeight: "h-24",
    dividerMargin: "mx-6",
    orientation: "horizontal",
    containerClass: "flex-row",
  });

  // --- NEW: States for dragging functionality ---
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const dragStartRef = useRef({ x: 0, y: 0, initialX: 0, initialY: 0 });
  const [isInitialized, setIsInitialized] = useState(false);

  // --- NEW: live dragging & listener refs (prevents stale-closure issues) ---
  const isDraggingRef = useRef(false);
  const moveListenerRef = useRef<((e: MouseEvent | TouchEvent) => void) | null>(
    null
  );
  const endListenerRef = useRef<((e: MouseEvent | TouchEvent) => void) | null>(
    null
  );

  const selectButton = (
    buttonId: string,
    newTool: "pen" | "eraser",
    newColor: string,
    newLineWidth: number,
    newOpacity?: number
  ) => {
    if (selectedButton === buttonId) {
      setSelectedButtonState(null);
      propSetSelectedButton?.(null);
    } else {
      setTool(newTool);
      setColor(newColor);
      setLineWidth(newLineWidth);
      if (typeof newOpacity === "number") {
        setOpacity(newOpacity);
      }
      setSelectedButtonState(buttonId);
      propSetSelectedButton?.(buttonId);

      if (onSelectMarker) {
        onSelectMarker(buttonId, newTool, newColor, newLineWidth, newOpacity);
      }
    }
  };

  useEffect(() => {
    const toolbarEl = toolbarRef.current;
    if (!toolbarEl) return;

    const parentEl = toolbarEl.parentElement;
    if (!parentEl) return;

    const updateScale = () => {
      const parentWidth = parentEl.clientWidth;
      const toolbarWidth = toolbarEl.scrollWidth;
      if (toolbarWidth > parentWidth) {
        const newScale = parentWidth / toolbarWidth;
        setScale(newScale);
      } else {
        setScale(1);
      }
    };

    const ro = new ResizeObserver(() => {
      updateScale();
    });
    ro.observe(parentEl);

    updateScale();
    return () => {
      ro.disconnect();
    };
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const layouts = {
        small: {
          mobile: {
            buttonSize: "w-14",
            buttonHeight: "h-16",
            paletteButtonSize: "w-6 h-6",
            dividerHeight: "h-16",
            dividerMargin: "mx-3",
          },
          tablet: {
            buttonSize: "w-16",
            buttonHeight: "h-16",
            paletteButtonSize: "w-7 h-7",
            dividerHeight: "h-20",
            dividerMargin: "mx-4",
          },
          desktop: {
            buttonSize: "w-16",
            buttonHeight: "h-16",
            paletteButtonSize: "w-7 h-7",
            dividerHeight: "h-20",
            dividerMargin: "mx-4",
          },
        },
        medium: {
          mobile: {
            buttonSize: "w-16",
            buttonHeight: "h-20",
            paletteButtonSize: "w-7 h-7",
            dividerHeight: "h-20",
            dividerMargin: "mx-4",
          },
          tablet: {
            buttonSize: "w-18",
            buttonHeight: "h-20",
            paletteButtonSize: "w-8 h-8",
            dividerHeight: "h-24",
            dividerMargin: "mx-6",
          },
          desktop: {
            buttonSize: "w-20",
            buttonHeight: "h-20",
            paletteButtonSize: "w-8 h-8",
            dividerHeight: "h-24",
            dividerMargin: "mx-6",
          },
        },
      };
      const currentLayoutSet = layouts[size];
      if (width < 480) {
        setToolbarLayout({
          ...currentLayoutSet.mobile,
          orientation: "vertical",
          containerClass: "flex-col",
        });
      } else if (width < 768) {
        setToolbarLayout({
          ...currentLayoutSet.tablet,
          orientation: "horizontal",
          containerClass: "flex-row",
        });
      } else {
        setToolbarLayout({
          ...currentLayoutSet.desktop,
          orientation: "horizontal",
          containerClass: "flex-row",
        });
      }
    };
    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, [size]);

  useEffect(() => {
    setToolbarVisible(true);
  }, []);

  // --- MODIFIED: Position initialization logic ---
  useEffect(() => {
    if (containerRef?.current && toolbarRef.current && !isInitialized) {
      const container = containerRef.current;
      const toolbar = toolbarRef.current;
      const newX = (container.clientWidth - toolbar.clientWidth) / 2;
      const newY = container.clientHeight - toolbar.clientHeight - 20; // 20px from bottom
      setPosition({ x: newX, y: newY });
      setIsInitialized(true);
    }
  }, [containerRef, isInitialized]);

  // --- NEW: Event handlers for dragging (stable, document-level) ---
  const onDragStart = useCallback(
    (
      e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>
    ) => {
      e.preventDefault();
      setIsDragging(true);
      isDraggingRef.current = true;

      const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
      const clientY = "touches" in e ? e.touches[0].clientY : e.clientY;
      dragStartRef.current = {
        x: clientX,
        y: clientY,
        initialX: position.x,
        initialY: position.y,
      };

      // Define stable listeners that read refs (no stale state)
      const handleMove = (evt: MouseEvent | TouchEvent) => {
        if (!isDraggingRef.current) return;

        const isTouch = "touches" in (evt as TouchEvent);
        if (isTouch) {
          // prevent page/parent scroll while dragging
          (evt as TouchEvent).preventDefault();
        }

        const curX = isTouch
          ? (evt as TouchEvent).touches[0].clientX
          : (evt as MouseEvent).clientX;
        const curY = isTouch
          ? (evt as TouchEvent).touches[0].clientY
          : (evt as MouseEvent).clientY;

        const dx = curX - dragStartRef.current.x;
        const dy = curY - dragStartRef.current.y;

        let newX = dragStartRef.current.initialX + dx;
        let newY = dragStartRef.current.initialY + dy;

        // Boundary checks
        if (containerRef?.current && toolbarRef.current) {
          const parent = containerRef.current;
          const child = toolbarRef.current;
          newX = Math.max(
            0,
            Math.min(newX, parent.clientWidth - child.clientWidth)
          );
          newY = Math.max(
            0,
            Math.min(newY, parent.clientHeight - child.clientHeight)
          );
        }

        setPosition({ x: newX, y: newY });
      };

      const handleEnd = (_evt: MouseEvent | TouchEvent) => {
        isDraggingRef.current = false;
        setIsDragging(false);

        if (moveListenerRef.current) {
          document.removeEventListener("mousemove", moveListenerRef.current);
          document.removeEventListener(
            "touchmove",
            moveListenerRef.current as any
          );
        }
        if (endListenerRef.current) {
          document.removeEventListener("mouseup", endListenerRef.current);
          document.removeEventListener(
            "touchend",
            endListenerRef.current as any
          );
        }
        moveListenerRef.current = null;
        endListenerRef.current = null;
      };

      moveListenerRef.current = handleMove;
      endListenerRef.current = handleEnd;

      document.addEventListener("mousemove", handleMove);
      document.addEventListener("mouseup", handleEnd);
      // mark touchmove as non-passive so we can preventDefault above
      document.addEventListener("touchmove", handleMove, { passive: false });
      document.addEventListener("touchend", handleEnd);
    },
    [position, containerRef]
  );

  // NOTE: Keeping these for compatibility, but they are no longer used to attach listeners.
  const onDragMove = useCallback(
    (e: MouseEvent | TouchEvent) => {
      if (!isDraggingRef.current) return;
      const clientX =
        "touches" in e
          ? (e as TouchEvent).touches[0].clientX
          : (e as MouseEvent).clientX;
      const clientY =
        "touches" in e
          ? (e as TouchEvent).touches[0].clientY
          : (e as MouseEvent).clientY;
      const dx = clientX - dragStartRef.current.x;
      const dy = clientY - dragStartRef.current.y;

      let newX = dragStartRef.current.initialX + dx;
      let newY = dragStartRef.current.initialY + dy;

      if (containerRef?.current && toolbarRef.current) {
        const parent = containerRef.current;
        const child = toolbarRef.current;
        newX = Math.max(
          0,
          Math.min(newX, parent.clientWidth - child.clientWidth)
        );
        newY = Math.max(
          0,
          Math.min(newY, parent.clientHeight - child.clientHeight)
        );
      }
      setPosition({ x: newX, y: newY });
    },
    [containerRef]
  );

  const onDragEnd = useCallback(() => {
    isDraggingRef.current = false;
    setIsDragging(false);
  }, []);

  useEffect(() => {
    // Safety cleanup if the component unmounts mid-drag
    return () => {
      if (moveListenerRef.current) {
        document.removeEventListener("mousemove", moveListenerRef.current);
        document.removeEventListener(
          "touchmove",
          moveListenerRef.current as any
        );
      }
      if (endListenerRef.current) {
        document.removeEventListener("mouseup", endListenerRef.current);
        document.removeEventListener("touchend", endListenerRef.current as any);
      }
    };
  }, []);

  useImperativeHandle(ref, () => ({}));

  function handleColorClick(newColor: string) {
    if (
      selectedButton === "marker1" ||
      selectedButton === "marker2" ||
      selectedButton === "marker3"
    ) {
      setMarkerColors((prev) => ({
        ...prev,
        [selectedButton]: newColor,
      }));
      setColor(newColor);
      if (onColorChange) {
        onColorChange(newColor);
      }
    } else {
      setColor(newColor);
      if (onColorChange) {
        onColorChange(newColor);
      }
    }
  }

  const renderToolbar = () => (
    <div
      ref={toolbarRef}
      onMouseDown={onDragStart}
      onTouchStart={onDragStart}
      className={`absolute flex ${
        toolbarLayout.containerClass
      } gap-2 p-2 bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl border border-gray-200/50 transition-shadow duration-150 ease-in-out z-50 ${
        isDragging ? "cursor-grabbing shadow-2xl" : "cursor-grab"
      }`}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        touchAction: "none", // Prevents scrolling on mobile while dragging
      }}
    >
      <div className="relative flex gap-2">
        <button
          onMouseEnter={(e) => {
            if (selectedButton !== "marker1") {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(5.5) translateX(5px)";
            } else {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(5.5) translateX(15px)";
            }
          }}
          onMouseLeave={(e) => {
            if (selectedButton !== "marker1") {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(5) translateX(0)";
            } else {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(5.5) translateX(15px)";
            }
          }}
          onClick={() =>
            selectButton("marker1", "pen", markerColors.marker1, 3, 1.0)
          }
          className={`
            ${toolbarLayout.buttonSize} 
            ${toolbarLayout.buttonHeight}
            flex items-center justify-center focus:outline-none hover:bg-transparent rounded overflow-visible transition-all duration-150 ease-in-out
            ${selectedButton === "marker1" ? "scale-105" : "scale-100"}
            ${selectedButton === "marker1" ? "z-20" : "z-20"}
            hover:z-20
          `}
        >
          <div className="relative w-full h-full pointer-events-auto">
            <div className="pointer-events-none absolute w-full h-full overflow-visible top-0 left-0">
              <svg
                width="24"
                height="85"
                viewBox="0 0 81 23"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  position: "absolute",
                  left: "50%",
                  bottom: selectedButton === "marker1" ? "-40px" : 0,
                  transform: `translate(-50%, 0) rotate(-90deg) ${
                    selectedButton === "marker1"
                      ? "scale(5.5) translateX(15px)"
                      : "scale(5) translateX(0)"
                  }`,
                  transformOrigin: "center center",
                  transition:
                    "transform 0.2s ease-in-out, bottom 0.2s ease-in-out",
                  color: markerColors.marker1,
                }}
              >
                <g filter="url(#_2867099304__a)">
                  <path
                    d="M76.659 10.483a1.172 1.172 0 0 1 1.004 1.148 1.17 1.17 0 0 1-.992 1.148l-9.14 1.46c-1.608.256-3.068-.964-3.068-2.564 0-1.592 1.444-2.812 3.044-2.568l9.152 1.376Z"
                    fill="currentColor"
                  ></path>
                  <path
                    d="m54.575 19.922.007-.003.008-.003 15.347-5.643V8.828L54.47 3.343l-.006-.002-.154-.057c-.257-.095-.562-.207-.807-.373-.26-.175-.472-.422-.541-.78l-1.993.056-.03 19.038h2.022a1.13 1.13 0 0 1 .143-.378c.106-.176.254-.31.414-.42.231-.157.526-.28.802-.395a13.8 13.8 0 0 0 .256-.11Z"
                    fill="#fff"
                    stroke="url(#_2867099304__b)"
                    strokeWidth=".5"
                  ></path>
                  <path
                    d="M4.188 1.788V21.6h36.25V1.788H4.188Z"
                    fill="#fff"
                    stroke="url(#_2867099304__c)"
                    strokeWidth=".5"
                  ></path>
                  <path
                    d="M4.188 1.788V21.6h36.25V1.788H4.188Z"
                    stroke="url(#_2867099304__d)"
                    strokeWidth=".5"
                  ></path>
                  <path
                    d="m16.25 11.819 6.54-1.688-1.016 3.125 6.413-1.562"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  ></path>
                  <path
                    d="M52.953 1.875v19.6c0 .057-.062.15-.195.15H41.907l11.046-19.75Zm0 0c0-.057-.062-.15-.195-.15H41.907c-.133 0-.196.093-.196.15v19.6c0 .057.063.15.196.15l11.046-19.75Z"
                    fill="#fff"
                    stroke="url(#_2867099304__e)"
                    strokeWidth=".5"
                  ></path>
                </g>
                <path
                  d="M42.188 21.875h-2v-20.4h2v20.4Z"
                  fill="currentColor"
                ></path>
                <defs>
                  <linearGradient
                    id="_2867099304__b"
                    x1="61.188"
                    y1="1.875"
                    x2="61.188"
                    y2="21.475"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#EFEFEF"></stop>
                    <stop
                      offset=".5"
                      stopColor="#EFEFEF"
                      stopOpacity="0"
                    ></stop>
                  </linearGradient>
                  <linearGradient
                    id="_2867099304__c"
                    x1="22.313"
                    y1="1.538"
                    x2="22.313"
                    y2="21.85"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#EFEFEF"></stop>
                    <stop
                      offset=".5"
                      stopColor="#EFEFEF"
                      stopOpacity="0"
                    ></stop>
                  </linearGradient>
                  <linearGradient
                    id="_2867099304__d"
                    x1="22.313"
                    y1="1.538"
                    x2="22.313"
                    y2="21.85"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#EFEFEF"></stop>
                    <stop
                      offset=".5"
                      stopColor="#EFEFEF"
                      stopOpacity="0"
                    ></stop>
                  </linearGradient>
                  <linearGradient
                    id="_2867099304__e"
                    x1="46.887"
                    y1="1.475"
                    x2="46.887"
                    y2="21.875"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#EFEFEF"></stop>
                    <stop
                      offset=".5"
                      stopColor="#EFEFEF"
                      stopOpacity="0"
                    ></stop>
                  </linearGradient>
                  <filter
                    id="_2867099304__a"
                    x=".938"
                    y=".475"
                    width="79.725"
                    height="26.4"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                  >
                    <feFlood
                      floodOpacity="0"
                      result="BackgroundImageFix"
                    ></feFlood>
                    <feColorMatrix
                      in="SourceAlpha"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    ></feColorMatrix>
                    <feMorphology
                      radius="1"
                      in="SourceAlpha"
                      result="effect1_dropShadow_18280_80133"
                    ></feMorphology>
                    <feOffset dy="2"></feOffset>
                    <feGaussianBlur stdDeviation="2"></feGaussianBlur>
                    <feColorMatrix values="0 0 0 0 0.25098 0 0 0 0 0.341176 0 0 0 0 0.427451 0 0 0 0.3 0"></feColorMatrix>
                    <feBlend
                      in2="BackgroundImageFix"
                      result="effect1_dropShadow_18280_80133"
                    ></feBlend>
                    <feColorMatrix
                      in="SourceAlpha"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    ></feColorMatrix>
                    <feMorphology
                      radius="1"
                      operator="dilate"
                      in="SourceAlpha"
                      result="effect2_dropShadow_18280_80133"
                    ></feMorphology>
                    <feOffset></feOffset>
                    <feColorMatrix values="0 0 0 0 0.25098 0 0 0 0 0.341176 0 0 0 0 0.427451 0 0 0 0.04 0"></feColorMatrix>
                    <feBlend
                      in2="effect1_dropShadow_18280_80133"
                      result="effect2_dropShadow_18280_80133"
                    ></feBlend>
                    <feBlend
                      in="SourceGraphic"
                      in2="effect2_dropShadow_18280_80133"
                      result="shape"
                    ></feBlend>
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </button>
        <button
          onClick={() =>
            selectButton("marker2", "pen", markerColors.marker2, 12, 1.0)
          }
          className={`
            ${toolbarLayout.buttonSize}
            ${toolbarLayout.buttonHeight}
            flex items-center justify-center focus:outline-none hover:bg-transparent rounded overflow-visible transition-all duration-150 ease-in-out
            ${selectedButton === "marker2" ? "scale-105" : "scale-100"}
            ${selectedButton === "marker2" ? "z-20" : "z-20"}
            hover:z-20
          `}
        >
          <div className="relative w-full h-full pointer-events-auto">
            <div className="pointer-events-none absolute w-full h-full overflow-visible top-0 left-0">
              <svg
                width="24"
                height="85"
                viewBox="0 0 80 22"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  position: "absolute",
                  left: "50%",
                  bottom: selectedButton === "marker2" ? "-40px" : 0,
                  transform: `translate(-50%, 0) rotate(-90deg) ${
                    selectedButton === "marker2"
                      ? "scale(5.5) translateX(15px)"
                      : "scale(5) translateX(0)"
                  }`,
                  transformOrigin: "center center",
                  transition:
                    "transform 0.2s ease-in-out, bottom 0.2s ease-in-out",
                  color: markerColors.marker2,
                }}
              >
                <g filter="url(#_3396433190__a)">
                  <path
                    d="M68.275 6.675h-.2v8.4h.2c4.6 0 8.6-1.88 8.6-4.2 0-2.32-4-4.2-8.6-4.2Z"
                    fill="currentColor"
                  ></path>
                  <path
                    d="M51.507 1.525H3.25v18.7h48.257a.55.55 0 0 0 .565-.534V2.06a.55.55 0 0 0-.565-.534Z"
                    fill="#fff"
                    stroke="url(#_3396433190__b)"
                    strokeWidth=".5"
                  ></path>
                  <path
                    d="M41.309 20.475h-2v-19.2h2v19.2Z"
                    fill="currentColor"
                  ></path>
                  <mask id="_3396433190__d" fill="#fff">
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M63.984 5.775c-1.902-.619-11.996-3.9-12.148-3.9v18l12.062-3.68h4.556c.44 0 .8-.364.8-.817V6.591a.81.81 0 0 0-.8-.816h-4.47Z"
                    ></path>
                  </mask>
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M63.984 5.775c-1.902-.619-11.996-3.9-12.148-3.9v18l12.062-3.68h4.556c.44 0 .8-.364.8-.817V6.591a.81.81 0 0 0-.8-.816h-4.47Z"
                    fill="#fff"
                  ></path>
                  <path
                    d="M51.836 1.875h-.5v-.5h.5v.5Zm12.148 3.9v.5h-.08l-.075-.025.155-.475Zm-12.148 14.1.146.478-.646.197v-.675h.5Zm12.062-3.68-.146-.479.071-.021h.075v.5Zm4.556 0v-.5.5Zm.8-.817h.5-.5Zm0-8.787h-.5.5Zm-.8-.816v-.5.5Zm-16.618-4.4c.044 0 .08.006.087.007a.677.677 0 0 1 .083.019l.11.03.355.108c.296.092.712.223 1.211.382.999.317 2.335.747 3.725 1.196 2.78.898 5.78 1.873 6.731 2.182l-.309.951c-.95-.31-3.95-1.284-6.73-2.181-1.39-.45-2.724-.878-3.72-1.195a164.778 164.778 0 0 0-1.543-.482 3.5 3.5 0 0 0-.085-.024l-.008-.003.016.004c.003 0 .035.006.077.006v-1Zm-.5 18.5v-18h1v18h-1Zm12.708-3.202-12.062 3.68-.292-.957 12.062-3.68.292.957Zm-.146-.978h4.556v1h-4.556v-1Zm4.556 0c.156 0 .3-.132.3-.317h1c0 .721-.576 1.317-1.3 1.317v-1Zm.3-.317V6.591h1v8.787h-1Zm0-8.787a.311.311 0 0 0-.3-.316v-1c.725 0 1.3.6 1.3 1.316h-1Zm-.3-.316h-4.47v-1h4.47v1Z"
                    fill="url(#_3396433190__c)"
                    mask="url(#_3396433190__d)"
                  ></path>
                </g>
                <path
                  d="m15.781 11.05.544-.01a5 5 0 0 0 2.291-.602l2.398-1.297a.5.5 0 0 1 .682.669l-1.492 2.888a.5.5 0 0 0 .667.677l2.868-1.427a5 5 0 0 1 2.227-.523h.784"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="bevel"
                ></path>
                <defs>
                  <linearGradient
                    id="_3396433190__b"
                    x1="47.575"
                    y1="20.475"
                    x2="47.575"
                    y2="1.275"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop
                      offset=".49"
                      stopColor="#EFEFEF"
                      stopOpacity="0"
                    ></stop>
                    <stop offset="1" stopColor="#EFEFEF"></stop>
                  </linearGradient>
                  <linearGradient
                    id="_3396433190__c"
                    x1="66.335"
                    y1="16.088"
                    x2="66.335"
                    y2="5.881"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop
                      offset=".49"
                      stopColor="#EFEFEF"
                      stopOpacity="0"
                    ></stop>
                    <stop offset="1" stopColor="#EFEFEF"></stop>
                  </linearGradient>
                  <filter
                    id="_3396433190__a"
                    x="0"
                    y=".275"
                    width="79.875"
                    height="25.2"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                  >
                    <feFlood
                      floodOpacity="0"
                      result="BackgroundImageFix"
                    ></feFlood>
                    <feColorMatrix
                      in="SourceAlpha"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    ></feColorMatrix>
                    <feMorphology
                      radius="1"
                      in="SourceAlpha"
                      result="effect1_dropShadow_18280_80118"
                    ></feMorphology>
                    <feOffset dy="2"></feOffset>
                    <feGaussianBlur stdDeviation="2"></feGaussianBlur>
                    <feColorMatrix values="0 0 0 0 0.25098 0 0 0 0 0.341176 0 0 0 0 0.427451 0 0 0 0.3 0"></feColorMatrix>
                    <feBlend
                      in2="BackgroundImageFix"
                      result="effect1_dropShadow_18280_80118"
                    ></feBlend>
                    <feColorMatrix
                      in="SourceAlpha"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    ></feColorMatrix>
                    <feMorphology
                      radius="1"
                      operator="dilate"
                      in="SourceAlpha"
                      result="effect2_dropShadow_18280_80118"
                    ></feMorphology>
                    <feOffset></feOffset>
                    <feColorMatrix values="0 0 0 0 0.25098 0 0 0 0 0.341176 0 0 0 0 0.427451 0 0 0 0.04 0"></feColorMatrix>
                    <feBlend
                      in2="effect1_dropShadow_18280_80118"
                      result="effect2_dropShadow_18280_80118"
                    ></feBlend>
                    <feBlend
                      in="SourceGraphic"
                      in2="effect2_dropShadow_18280_80118"
                      result="shape"
                    ></feBlend>
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </button>
        <button
          onClick={() =>
            selectButton("marker3", "pen", markerColors.marker3, 16, 0.5)
          }
          className={`
            ${toolbarLayout.buttonSize}
            ${toolbarLayout.buttonHeight}
            flex items-center justify-center focus:outline-none hover:bg-transparent rounded overflow-visible transition-all duration-150 ease-in-out
            ${selectedButton === "marker3" ? "scale-105" : "scale-100"}
            ${selectedButton === "marker3" ? "z-20" : "z-20"}
            hover:z-20
          `}
        >
          <div className="relative w-full h-full pointer-events-auto">
            <div className="pointer-events-none absolute w-full h-full overflow-visible top-0 left-0">
              <svg
                width="24"
                height="85"
                viewBox="0 0 81 22"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  position: "absolute",
                  left: "50%",
                  bottom: selectedButton === "marker3" ? "-40px" : 0,
                  transform: `translate(-50%, 0) rotate(-90deg) ${
                    selectedButton === "marker3"
                      ? "scale(5.5) translateX(15px)"
                      : "scale(5) translateX(0)"
                  }`,
                  transformOrigin: "center center",
                  transition:
                    "transform 0.2s ease-in-out, bottom 0.2s ease-in-out",
                  color: markerColors.marker3,
                }}
              >
                <g
                  clipPath="url(#_4290814143__a)"
                  filter="url(#_4290814143__b)"
                >
                  <path
                    d="M74.469 14.714c.182 0 .34-.117.389-.286l2.202-7.445a.394.394 0 0 0-.39-.497h-7.574v8.228h5.373Z"
                    fill="currentColor"
                  ></path>
                  <path
                    d="m53.05 1.302.003.002 3.824 2.468c1.03.666 2.545 1.168 4.092 1.504 1.551.337 3.156.511 4.376.511h3.406c.341 0 .609.269.609.581v8.464c0 .312-.268.58-.609.58h-3.406c-1.218 0-2.823.175-4.374.512-1.547.336-3.064.838-4.094 1.504L53.05 1.302Zm0 0a.614.614 0 0 0-.337-.1H3.453v18.795h49.26a.624.624 0 0 0 .34-.1l3.824-2.47L53.05 1.303Z"
                    fill="#fff"
                    stroke="url(#_4290814143__c)"
                    strokeWidth=".406"
                  ></path>
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="m20.133 10.363-3.81 1.818-1.292-2.707 6.438-3.072 2.123 1.614-.51 2.896 3.729-1.68 1.232 2.736-6.312 2.843-2.094-1.627.496-2.82Z"
                    fill="currentColor"
                  ></path>
                  <path
                    d="M41.625 20.2h-2V1h2v19.2Z"
                    fill="currentColor"
                  ></path>
                </g>
                <defs>
                  <linearGradient
                    id="_4290814143__c"
                    x1="36.406"
                    y1="20.2"
                    x2="36.406"
                    y2="1"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop
                      offset=".49"
                      stopColor="#EFEFEF"
                      stopOpacity="0"
                    ></stop>
                    <stop offset="1" stopColor="#EFEFEF"></stop>
                  </linearGradient>
                  <clipPath id="_4290814143__a">
                    <path
                      fill="#fff"
                      transform="matrix(1 0 0 -1 3 21.275)"
                      d="M0 0h74.207v20.275H0z"
                    ></path>
                  </clipPath>
                  <filter
                    id="_4290814143__b"
                    x="0"
                    y="0"
                    width="80.207"
                    height="26.275"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                  >
                    <feFlood
                      floodOpacity="0"
                      result="BackgroundImageFix"
                    ></feFlood>
                    <feColorMatrix
                      in="SourceAlpha"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    ></feColorMatrix>
                    <feMorphology
                      radius="1"
                      in="SourceAlpha"
                      result="effect1_dropShadow_18280_80112"
                    ></feMorphology>
                    <feOffset dy="2"></feOffset>
                    <feGaussianBlur stdDeviation="2"></feGaussianBlur>
                    <feColorMatrix values="0 0 0 0 0.25098 0 0 0 0 0.341176 0 0 0 0 0.427451 0 0 0 0.3 0"></feColorMatrix>
                    <feBlend
                      in2="BackgroundImageFix"
                      result="effect1_dropShadow_18280_80112"
                    ></feBlend>
                    <feColorMatrix
                      in="SourceAlpha"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    ></feColorMatrix>
                    <feMorphology
                      radius="1"
                      operator="dilate"
                      in="SourceAlpha"
                      result="effect2_dropShadow_18280_80112"
                    ></feMorphology>
                    <feOffset></feOffset>
                    <feColorMatrix values="0 0 0 0 0.25098 0 0 0 0 0.341176 0 0 0 0 0.427451 0 0 0 0.04 0"></feColorMatrix>
                    <feBlend
                      in2="effect1_dropShadow_18280_80112"
                      result="effect2_dropShadow_18280_80112"
                    ></feBlend>
                    <feBlend
                      in="SourceGraphic"
                      in2="effect2_dropShadow_18280_80112"
                      result="shape"
                    ></feBlend>
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </button>
        <button
          onClick={() => selectButton("eraser", "eraser", "#ffffff", 10, 1.0)}
          className={`
            ${toolbarLayout.buttonSize}
            ${toolbarLayout.buttonHeight}
            flex items-center justify-center focus:outline-none hover:bg-transparent rounded overflow-visible transition-all duration-150 ease-in-out
            ${selectedButton === "eraser" ? "scale-105" : "scale-100"}
            ${selectedButton === "eraser" ? "z-20" : "z-20"}
            hover:z-20
          `}
        >
          <div className="relative w-full h-full pointer-events-auto">
            <div className="pointer-events-none absolute w-full h-full overflow-visible top-0 left-0">
              <svg
                width="24"
                height="85"
                viewBox="0 0 81 22"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  position: "absolute",
                  left: "50%",
                  bottom: selectedButton === "eraser" ? "-40px" : 0,
                  transform: `translate(-50%, 0) rotate(-90deg) ${
                    selectedButton === "eraser"
                      ? "scale(5.5) translateX(15px)"
                      : "scale(5) translateX(0)"
                  }`,
                  transformOrigin: "center center",
                  transition:
                    "transform 0.2s ease-in-out, bottom 0.2s ease-in-out",
                }}
              >
                <g filter="url(#_4206229758__a)">
                  <g filter="url(#_4206229758__b)">
                    <path
                      d="M75.324 19.654a2 2 0 0 0 1.864-1.995V4.123a2 2 0 0 0-1.864-1.995l-15.12-1.037H3v19.6h57.203l15.121-1.037Z"
                      fill="#FF9EAD"
                    ></path>
                    <path
                      d="M75.324 19.654a2 2 0 0 0 1.864-1.995V4.123a2 2 0 0 0-1.864-1.995l-15.12-1.037H3v19.6h57.203l15.121-1.037Z"
                      fill="#FC7C90"
                    ></path>
                  </g>
                  <g style={{ mixBlendMode: "multiply" }} opacity=".5">
                    <path
                      d="M75.324 19.654a2 2 0 0 0 1.864-1.995V4.123a2 2 0 0 0-1.864-1.995L60.188 1.091v19.6h15.136v-1.037Z"
                      fill="#8CC8FF"
                    ></path>
                    <path
                      d="M75.324 19.654a2 2 0 0 0 1.864-1.995V4.123a2 2 0 0 0-1.864-1.995L60.188 1.091v19.6h15.136v-1.037Z"
                      fill="#FC7C90"
                    ></path>
                  </g>
                  <path
                    d="M70.25 20.001V1.776l-2-.27v18.771h2v-.276Z"
                    fill="#fff"
                  ></path>
                </g>
                <defs>
                  <filter
                    id="_4206229758__a"
                    x="0"
                    y=".091"
                    width="80.188"
                    height="25.6"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                  >
                    <feFlood
                      floodOpacity="0"
                      result="BackgroundImageFix"
                    ></feFlood>
                    <feColorMatrix
                      in="SourceAlpha"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    ></feColorMatrix>
                    <feMorphology
                      radius="1"
                      in="SourceAlpha"
                      result="effect1_dropShadow_18280_80154"
                    ></feMorphology>
                    <feOffset dy="2"></feOffset>
                    <feGaussianBlur stdDeviation="2"></feGaussianBlur>
                    <feColorMatrix values="0 0 0 0 0.25098 0 0 0 0 0.341176 0 0 0 0 0.427451 0 0 0 0.3 0"></feColorMatrix>
                    <feBlend
                      in2="BackgroundImageFix"
                      result="effect1_dropShadow_18280_80154"
                    ></feBlend>
                    <feBlend
                      in="SourceGraphic"
                      in2="effect1_dropShadow_18280_80154"
                      result="shape"
                    ></feBlend>
                  </filter>
                  <filter
                    id="_4206229758__b"
                    x="3"
                    y="1.091"
                    width="74.188"
                    height="19.6"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                  >
                    <feFlood
                      floodOpacity="0"
                      result="BackgroundImageFix"
                    ></feFlood>
                    <feBlend
                      in="SourceGraphic"
                      in2="BackgroundImageFix"
                      result="shape"
                    ></feBlend>
                    <feColorMatrix
                      in="SourceAlpha"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    ></feColorMatrix>
                    <feOffset></feOffset>
                    <feGaussianBlur stdDeviation="1.8"></feGaussianBlur>
                    <feComposite
                      in2="hardAlpha"
                      operator="arithmetic"
                      k2="-1"
                      k3="1"
                    ></feComposite>
                    <feColorMatrix values="0 0 0 0 0.988235 0 0 0 0 0.486275 0 0 0 0 0.564706 0 0 0 1 0"></feColorMatrix>
                    <feBlend
                      in2="shape"
                      result="effect1_innerShadow_18280_80154"
                    ></feBlend>
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </button>
        <div
          className="
            absolute
            bottom-0
            left-0
            w-full
            h-3/5
            bg-white/95
            backdrop-blur-sm
            pointer-events-none
            z-30
          "
        ></div>
      </div>
      <div
        className={`border-l border-gray-200 ${toolbarLayout.dividerHeight} ${toolbarLayout.dividerMargin}`}
      ></div>
      <div className="grid grid-cols-3 gap-4">
        <button
          onClick={() => handleColorClick("#000000")}
          className={`${toolbarLayout.paletteButtonSize} rounded-full bg-black transform transition-transform duration-100 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400`}
          aria-label="Black color"
        />
        <button
          onClick={() => handleColorClick("#007AFF")}
          className={`${toolbarLayout.paletteButtonSize} rounded-full bg-blue-500 transform transition-transform duration-100 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400`}
          aria-label="Blue color"
        />
        <button
          onClick={() => handleColorClick("#34C759")}
          className={`${toolbarLayout.paletteButtonSize} rounded-full bg-green-500 transform transition-transform duration-100 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-400`}
          aria-label="Green color"
        />
        <button
          onClick={() => handleColorClick("#FF0000")}
          className={`${toolbarLayout.paletteButtonSize} rounded-full bg-red-500 transform transition-transform duration-100 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-400`}
          aria-label="Red color"
        />
        <button
          onClick={() => handleColorClick("#FFCC00")}
          className={`${toolbarLayout.paletteButtonSize} rounded-full bg-yellow-400 transform transition-transform duration-100 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-300`}
          aria-label="Yellow color"
        />
        <button
          onClick={() => handleColorClick("#A020F0")}
          className={`${toolbarLayout.paletteButtonSize} rounded-full bg-purple-400 transform transition-transform duration-100 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-400`}
          aria-label="Purple color"
        />
      </div>
    </div>
  );

  return (
    <>
      <style>
        {`
          @media (max-width: 480px) {
            .toolbar-vertical {
              flex-direction: column;
              right: 1rem;
              top: 50%;
              transform: translateY(-50%);
            }
          }

          .toolbar-button {
            transition: all 0.15s ease-in-out;
          }

          .toolbar-button:hover {
            transform: scale(1.1);
          }
        `}
      </style>

      {toolbarVisible && renderToolbar()}
    </>
  );
}

const Toolbar = forwardRef(ToolbarComponent);
export default Toolbar;

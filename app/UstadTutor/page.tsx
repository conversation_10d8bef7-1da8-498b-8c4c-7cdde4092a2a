// app/ustadTutor/page.tsx
import { auth } from "@clerk/nextjs/server";
// REMOVED: getOrCreateUser is no longer needed here.
import nextDynamic from "next/dynamic";
import {
  getChapterWithSquaresByOrder,
  getChapterWithSquares,
} from "@/db/queries";

export const dynamic = "force-dynamic";

const UstadTutor = nextDynamic(() => import("./UstadTutor"), {
  ssr: false,
});

export default async function UstadTutorPage({
  searchParams,
}: {
  searchParams: { chapter?: string };
}) {
  const { userId } = auth();
  // REMOVED the blocking getOrCreateUser() call:
  // if (userId) {
  //   await getOrCreateUser();
  // }

  const chapterOrder = searchParams.chapter
    ? parseInt(searchParams.chapter, 10)
    : 1;

  // Keep this line (not removing it as per instructions)
  let chapterData = await getChapterWithSquares(chapterOrder);
  // Now fetch by order-based approach
  const chapterDataByOrder = await getChapterWithSquaresByOrder(chapterOrder);

  // Overwrite chapterData with order-based result
  chapterData = chapterDataByOrder;

  const transformedChapterData = chapterData
    ? {
        ...chapterData,
        squares: chapterData.squares.map((sq) => ({
          ...sq,
          transliteration: sq.transliteration ?? "",
          group: "basic" as "basic",
          audioUrl: "",
          overlayContent: "",
        })),
        // Ensure we have 'order' field available
        order: chapterData.order || chapterOrder,
      }
    : null;

  return (
    <div
      style={{
        position: "relative",
        height: "100vh",
        width: "100vw",
        background: "#fff",
      }}
    >
      <UstadTutor chapterData={transformedChapterData} userId={userId || ""} />
    </div>
  );
}

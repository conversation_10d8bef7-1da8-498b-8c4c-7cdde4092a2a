"use client";

import React from "react";
// Import Lucide icons for a clean, modern look
import { Mic, Square, Loader2, CircleDot } from "lucide-react";

// Import the necessary types from the store
import {
  useVoiceStore,
  ConnectionStatus,
  ConversationStatus,
} from "@/store/useVoiceStore";

// The props interface is updated to accept the `status` prop from the parent.
interface StartConversationProps {
  status: ConnectionStatus;
  onStart: () => void;
}

// Define props for the Icon component, adding conversationActive for clearer logic
interface IconProps {
  status: ConversationStatus;
  isSpeaking: boolean;
  isListening: boolean;
  connectionStatus: ConnectionStatus;
  conversationActive: boolean; // Added for robust state handling
}

// A helper component to render the correct icon based on state using Lucide icons
const Icon = ({
  status,
  isSpeaking,
  isListening,
  connectionStatus,
  conversationActive, // Use this for state logic
}: IconProps) => {
  const iconSize = "w-6 h-6";

  // Highest priority: connecting state
  if (connectionStatus === "connecting") {
    return <Loader2 className={`animate-spin ${iconSize}`} />;
  }

  // AI is speaking - use a visual indicator
  if (isSpeaking) {
    return <CircleDot className={`animate-pulse ${iconSize}`} />;
  }

  // User is speaking or ready to speak
  if (isListening) {
    // A subtle pulse indicates the mic is "hot" or listening
    return <Mic className={`animate-pulse ${iconSize}`} />;
  }

  // Ready to end the conversation (idle but active)
  if (conversationActive) {
    return <Square className={`${iconSize}`} fill="currentColor" />;
  }

  // Default state: Ready to start the conversation
  return <Mic className={iconSize} />;
};

/**
 * An icon-button that expands on hover to show text, used for starting/stopping
 * the real-time voice conversation with the AI Tutor.
 */
export default function StartConversation({
  status,
  onStart,
}: StartConversationProps) {
  // This component now receives `status` as a prop but still uses the store for other UI states.
  const conversationActive = useVoiceStore((state) => state.conversationActive);
  const conversationStatus = useVoiceStore((state) => state.conversationStatus);
  const isListening = useVoiceStore((state) => state.isListening);
  const isSpeaking = useVoiceStore((state) => state.isSpeaking);

  // Generates the text to be displayed inside the button on hover
  const getButtonText = () => {
    if (status === "connecting") return "Connecting...";
    if (!conversationActive) return "Start Voice Chat";
    if (isListening) return "Listening...";
    if (isSpeaking) return "AI Speaking...";
    return "End Conversation";
  };

  const isButtonDisabled = status === "connecting";

  // Base classes for the button, now with group utility for hover effects on children
  const baseClasses = `
    group
    flex items-center justify-center
    h-14
    w-14 hover:w-48
    text-base font-semibold
    rounded-full
    transition-all duration-300 ease-in-out
    focus:outline-none focus:ring-4
    disabled:transform-none disabled:cursor-not-allowed
  `;

  // Dynamic styles based on state remain to provide strong visual feedback
  const getButtonStyles = () => {
    if (isButtonDisabled) {
      return "bg-neutral-200 text-neutral-400 dark:bg-neutral-800 dark:text-neutral-500";
    }
    // Active conversation states (listening or speaking) get the accent color
    if (isListening || isSpeaking) {
      return "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-300 dark:focus:ring-blue-800";
    }
    // Ready to end conversation
    if (conversationActive) {
      return "bg-red-600 text-white hover:bg-red-700 focus:ring-red-300 dark:focus:ring-red-900";
    }
    // Default "Start" state is now black
    // Removed hover:bg-neutral-800 to keep it black on hover
    return "bg-[#1a1a18] text-white focus:ring-neutral-400 dark:border dark:border-neutral-700";
  };

  // Classes for the text, making it appear on group-hover. Margin is now conditional.
  const textClasses = `
    whitespace-nowrap
    transition-all duration-300
    opacity-0 max-w-0
    group-hover:ml-2 group-hover:opacity-100 group-hover:max-w-full
  `;

  return (
    <button
      onClick={onStart}
      disabled={isButtonDisabled}
      className={`${baseClasses} ${getButtonStyles()}`}
      title={getButtonText()} // Tooltip is still useful for accessibility
    >
      <Icon
        status={conversationStatus}
        isSpeaking={isSpeaking}
        isListening={isListening}
        connectionStatus={status}
        conversationActive={conversationActive}
      />
      <span className={textClasses}>{getButtonText()}</span>
    </button>
  );
}

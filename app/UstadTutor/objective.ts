// objective.ts
// Centralized objectives (and tips) for lessons.
// Pure data module: safe to import server- or client-side.

// ---------- Types ----------
export type IconKey = "target" | "check" | "none";

export interface ObjectiveBullet {
  text: string;
  icon?: IconKey; // consumer decides how to render the icon
}

export interface LessonObjectives {
  lesson: number;
  heading: string; // e.g., "Student checklist:"
  soFar: ObjectiveBullet[]; // kept for compatibility; not used in UI
  endGoals: ObjectiveBullet[]; // "By the end of this level, I should be able to:"
  tips: string[]; // Tips shown under the objectives
}

/**
 * Optional hint map so your UI knows which icon component to use.
 * Example (React):
 *   import { IconTarget, IconCircleCheck } from "@tabler/icons-react";
 *   const Icons = { target: IconTarget, check: IconCircleCheck };
 */
export const IconHints: Record<IconKey, { library: string; name: string }> = {
  target: { library: "@tabler/icons-react", name: "IconTarget" },
  check: { library: "@tabler/icons-react", name: "IconCircleCheck" },
  none: { library: "", name: "" },
};

// ---------- Lesson 1 content (simple wording for a 10-year-old; no letter examples) ----------
const LESSON_1_OBJECTIVES: LessonObjectives = {
  lesson: 1,
  heading: "Student checklist:",
  soFar: [], // not used
  endGoals: [
    {
      text: "recognise and name every letter in this lesson when I see it.",
      icon: "target",
    },
    { text: "say each letter clearly, using one short beat.", icon: "target" },
    {
      text: "read small groups of letters smoothly from right to left.",
      icon: "target",
    },
    {
      text: "tell apart letters that look similar by checking the dots and shape.",
      icon: "target",
    },
  ],
  tips: [
    "Read in a steady voice—no singing or stretching.",
    "Use your finger to track from right to left.",
    "If you’re unsure, slow down, check the dots, then try again.",
  ],
};

// ---------- Registry & helpers ----------
export const OBJECTIVES_REGISTRY: Record<number, LessonObjectives> = {
  1: LESSON_1_OBJECTIVES,
};

/** Returns objectives for a given lesson or null if none exist. */
export function getObjectivesForLesson(
  lessonNumber: number
): LessonObjectives | null {
  return OBJECTIVES_REGISTRY[lessonNumber] ?? null;
}

/** Convenience helper: is there an objectives entry for this lesson? */
export function hasObjectives(lessonNumber: number): boolean {
  return lessonNumber in OBJECTIVES_REGISTRY;
}

/** Returns a human-friendly fallback if a lesson doesn’t have objectives yet. */
export function getObjectivesFallback(lessonNumber: number): LessonObjectives {
  return {
    lesson: lessonNumber,
    heading: "Student checklist:",
    soFar: [],
    endGoals: [],
    tips: ["Objectives for this lesson are coming soon."],
  };
}

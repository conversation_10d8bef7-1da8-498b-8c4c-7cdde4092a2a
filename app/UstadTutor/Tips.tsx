import React, { ReactNode } from "react";
import { Sparkles } from "lucide-react";

/**
 * A type definition for the tips object.
 * The outer key is the lesson number (e.g., 1 for lesson 1).
 * The inner key is the card's unique ID.
 * The value is the string tip to be displayed.
 */
type LessonTips = {
  [lessonNumber: number]: {
    [cardId: number]: string;
  };
};

/**
 * An object containing phonetic study tips for students.
 * The structure maps directly to the lesson and card being rendered.
 */
export const lessonTips: LessonTips = {
  // Tips for Lesson 1
  1: {
    1: "A silent letter that makes vowels long, like the 'a' in 'car'.",
    2: "Close your lips and then make a sound like the 'b' in 'bat'.",
    3: "Put your tongue behind your top teeth and make a 't' sound, like in 'tap'.",
    4: "Put your tongue between your teeth and blow, like the 'th' in 'think'.",
    5: "Make a 'j' sound from the middle of your tongue, like in 'judge'.",
    6: "Breathe out a sharp, airy sound from your throat, like a quiet 'sigh'.",
    7: "Make a scratchy sound from the back of your throat, like the 'ch' in 'loch'.",
    8: "Put your tongue behind your teeth and make a 'd' sound, like in 'dog'.",
    9: "Put your tongue on your teeth and make a soft 'th' sound, like in 'this'.",
    10: "Lightly tap your tongue to the roof of your mouth to make a rolling 'r' sound.",
    11: "Make a buzzing sound with your teeth close together, like the 'z' in 'zebra'.",
    12: "Make a hissing sound with your tongue low, like the 's' in 'snake'.",
    13: "Make a soft 'sh' sound with rounded lips, like in 'shoe'.",
    14: "Create a heavy, thick 's' sound by raising the back of your tongue.",
    15: "Use the side of your tongue against your molars to make a strong 'd' sound.",
    16: "Make a strong, heavy 't' sound by raising the back of your tongue.",
    17: "Create a heavy 'th' sound, like in 'this', but with the back of your tongue raised.",
    18: "Make a deep, constricted sound from low in your throat.",
    19: "Make a gargling sound from the back of your throat, like the French 'r'.",
    20: "Put your bottom lip on your top teeth and blow, like the 'f' in 'fun'.",
    21: "Make a strong 'k' sound from the very back of your throat.",
    22: "Make a light 'k' sound from the front of your mouth, like in 'cat'.",
    23: "Touch your tongue to the roof of your mouth and say 'l', like in 'lamp'.",
    24: "Close your lips and hum, just like the 'm' in 'moon'.",
    25: "Put your tongue behind your teeth and hum, like the 'n' in 'no'.",
    26: "Round your lips to make a 'w' sound, like in 'water'.",
    27: "Breathe out a soft, light sound from your chest, like the 'h' in 'hat'.",
    28: "Stop the sound sharply in your throat, like the pause in 'uh-oh'.",
    29: "Make a 'y' sound from the middle of your tongue, like in 'yes'.",
    30: "Make a 'y' sound from the middle of your tongue, like in 'yes'.",
  },
};

/**
 * A centralized formatter that takes a raw tip string and returns a fully-styled JSX node.
 * @param tipString The raw text of the tip.
 * @returns A ReactNode containing the styled tip, ready to be rendered.
 */
export const formatTip = (tipString: string): ReactNode => {
  return (
    <div className="bg-white/90 backdrop-blur-md border border-gray-200/50 text-gray-800 p-4 rounded-xl flex items-start gap-3 shadow-lg">
      <Sparkles className="w-5 h-5 text-black flex-shrink-0 mt-0.5" />
      <p className="text-sm font-medium">{tipString}</p>
    </div>
  );
};

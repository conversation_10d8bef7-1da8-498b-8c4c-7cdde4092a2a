/* ------------------------------------------------------------------ */
/*  FeedbackSection.tsx — enhanced to handle simplified missing words */
/* ------------------------------------------------------------------ */
import React, {
  useEffect,
  useState,
  ReactNode,
  RefObject,
  useCallback,
  useRef,
  useMemo,
} from "react";
import ThinkingIcon from "./thinkingIcon";
import {
  synthesizeSpeech,
  batchPrefetchTTS,
  isTextCached,
} from "./Audio/ttsUtils";
import {
  extractTextFromJSX,
  generateCitationFocusedSummary,
} from "./Audio/summaryFeedbackUtils";
import useClipPlayer from "./Audio/useClipPlayer";

/* ---------- Types ------------------------------------------------- */
type FeedbackMode = "overview" | "pronunciation";

interface Verse {
  surahNumber: number;
  verseNumber: number;
  text: string;
}

interface FeedbackSectionProps {
  // Core feedback data
  feedback: ReactNode[]; // matches 'combinedFeedback' from parent
  feedbackData?: any;
  citationMap?: Record<string, number>;

  // Additional data
  verses: Verse[];
  displayedWords: { [verseNumber: number]: any[] };
  feedbackRefs: { [key: string]: RefObject<HTMLDivElement> };

  // Configuration and state
  difficulty: "easy" | "hard";
  score: number;

  // Optional props
  highlightedWord?: string | null;
  loading?: boolean;
  selectedCitation?: number | null;
  activeFeedbackSection?: FeedbackMode;
  audioURL?: string | null;

  // Callback functions
  onCitationClick: (citationNumber: number) => void;
  onFeedbackSectionChange?: (section: FeedbackMode) => void;
  onPlayCitationTTS?:
    | ((citationNumber: number) => Promise<void>)
    | ((citationSummary: string) => Promise<void>);

  /** receive issue-count so parent can badge the nav-button */
  onFeedbackCountChange?: (count: number) => void;
}

interface FeedbackElementProps {
  "data-word"?: string;
  "data-citation-number"?: string;
  children?: React.ReactNode;
  className?: string;
  [k: string]: any;
}

/* ================================================================== */
/*   COMPONENT                                                        */
/* ================================================================== */
const FeedbackSection: React.FC<FeedbackSectionProps> = React.memo(
  ({
    feedback,
    feedbackData,
    citationMap,
    verses,
    displayedWords,
    highlightedWord = null,
    feedbackRefs,
    difficulty,
    score,
    loading = false,
    selectedCitation = null,
    activeFeedbackSection = "overview",
    audioURL = null,
    onCitationClick,
    onFeedbackSectionChange = () => {},
    onPlayCitationTTS,
    onFeedbackCountChange = () => {},
  }) => {
    /* ---------- local state --------------------------------------- */
    const [displayedText, setDisplayedText] = useState<ReactNode[]>([]);
    const [revealIdx, setRevealIdx] = useState(0);
    const [pendingScroll, setPendingScroll] = useState<string | null>(null);
    const [isReading, setIsReading] = useState(false);
    const [isCitationTTSLoading, setIsCitationTTSLoading] = useState(false);
    const isUpdatingRef = useRef(false);

    // Add useClipPlayer hook for playing user's recorded segments
    const { play: playSegment } = useClipPlayer(audioURL);

    /* ---------- process feedback elements -------------------------- */
    const childrenArr = useMemo(
      () => React.Children.toArray(feedback),
      [feedback]
    );

    const isCard = (el: any): el is React.ReactElement<FeedbackElementProps> =>
      React.isValidElement(el);

    /* ---------- 🆕 Enhanced categorization for simplified missing words ------------------- */
    const categorized = useMemo(() => {
      const summary: ReactNode[] = [];
      const missing: ReactNode[] = [];
      const pronunciation: ReactNode[] = [];
      const extra: ReactNode[] = [];

      childrenArr.forEach((el) => {
        if (!isCard(el)) return;

        const w = el.props["data-word"] ?? "";

        // Handle summary/congratulations messages
        if (
          w.includes("congratulations") ||
          w.includes("perfect-recitation") ||
          w.includes("all-memorized")
        ) {
          summary.push(el);
          return;
        }

        // 🆕 Enhanced: Handle the new simplified missing words feedback
        if (w.includes("missed-words-group")) {
          missing.push(el);
          return;
        }

        // Handle extra words group
        if (w.includes("extra-words-group")) {
          extra.push(el);
          return;
        }

        /* dig out the coloured title for detailed feedback cards */
        let title = "";
        const children = el.props.children;
        if (isCard(children)) {
          const nestedChildren = children.props.children;
          if (Array.isArray(nestedChildren) && nestedChildren.length > 0) {
            const firstChild = nestedChildren[0];
            if (isCard(firstChild)) {
              const furtherChildren = firstChild.props.children;
              if (
                Array.isArray(furtherChildren) &&
                furtherChildren.length > 2
              ) {
                const titleElement = furtherChildren[2];
                if (isCard(titleElement) && titleElement.props.children) {
                  title = extractTextFromJSX(titleElement.props.children) || "";
                }
              }
            }
          }
        }

        // 🆕 Enhanced: Check for both "Missing" and "Missed" titles
        if (title.includes("Missing") || title.includes("Missed")) {
          missing.push(el);
        } else if (title.includes("Extra")) {
          extra.push(el);
        } else {
          // Everything else goes to pronunciation (partial matches, letter corrections, etc.)
          pronunciation.push(el);
        }
      });
      return { summary, missing, pronunciation, extra };
    }, [childrenArr]);

    /* ---------- 🆕 Enhanced stats calculation ----------------------------- */
    const totals = useMemo(
      () => ({
        total:
          categorized.missing.length +
          categorized.pronunciation.length +
          categorized.extra.length,
        missing: categorized.missing.length,
        pronunciation: categorized.pronunciation.length,
        extra: categorized.extra.length,
        perfect:
          categorized.summary.length > 0 &&
          categorized.missing.length === 0 &&
          categorized.pronunciation.length === 0 &&
          categorized.extra.length === 0,
      }),
      [categorized]
    );

    /* push count up to parent for red badge */
    useEffect(() => {
      onFeedbackCountChange(totals.total);
    }, [totals, onFeedbackCountChange]);

    /* ---------- display all feedback (no tab filtering) ----------- */
    const currentArray = useMemo(() => {
      // Show filtered pronunciation feedback if selectedCitation exists
      if (selectedCitation) {
        return childrenArr.filter(
          (el) =>
            isCard(el) &&
            parseInt(el.props["data-citation-number"] ?? "-1") ===
              selectedCitation
        );
      }
      // Otherwise show all feedback
      return childrenArr;
    }, [childrenArr, selectedCitation]);

    /* ---------- reset when content changes ------------------------ */
    useEffect(() => {
      setDisplayedText([]);
      setRevealIdx(0);
    }, [currentArray]);

    /* ---------- reveals (typewriter-style) ------------------------ */
    useEffect(() => {
      if (loading) return;
      if (revealIdx >= currentArray.length) return;

      const t = setTimeout(() => {
        setDisplayedText((prev) => [...prev, currentArray[revealIdx]]);
        setRevealIdx((i) => i + 1);
      }, 120);

      return () => clearTimeout(t);
    }, [revealIdx, currentArray, loading]);

    /* ---------- highlight & scroll helpers ------------------------ */
    const scrollTo = useCallback(
      (el: HTMLDivElement) =>
        el.scrollIntoView({ block: "center", behavior: "smooth" }),
      []
    );

    const pulseHighlight = useCallback((el: HTMLDivElement | null) => {
      if (!el) return;
      el.classList.add("citation-highlight");
      setTimeout(() => el.classList.remove("citation-highlight"), 1800);
    }, []);

    /* highlighted word from SurahDisplay */
    useEffect(() => {
      if (!highlightedWord) return;

      /* 1) direct word-key reference */
      if (feedbackRefs[highlightedWord]?.current) {
        setPendingScroll(highlightedWord);
        return;
      }

      /* 2) maybe it's a citation number string */
      const maybeNum = Number(highlightedWord);
      if (!isNaN(maybeNum)) {
        let key: string | null = null;
        Object.keys(feedbackRefs).forEach((k) => {
          const ref = feedbackRefs[k]?.current;
          if (
            ref &&
            parseInt(ref.getAttribute("data-citation-number") ?? "-1") ===
              maybeNum
          )
            key = k;
        });
        if (key) {
          setPendingScroll(key);
        }
      }
    }, [highlightedWord, feedbackRefs]);

    /* after reveal finishes, honour pendingScroll */
    useEffect(() => {
      if (pendingScroll && revealIdx === currentArray.length) {
        const el = feedbackRefs[pendingScroll]?.current;
        if (el) {
          scrollTo(el);
          pulseHighlight(el);
        }
        setPendingScroll(null);
      }
    }, [
      pendingScroll,
      revealIdx,
      currentArray.length,
      feedbackRefs,
      scrollTo,
      pulseHighlight,
    ]);

    /* ---------- handle selected citation navigation --------------- */
    useEffect(() => {
      if (selectedCitation) {
        const feedbackElement = childrenArr.find((element) => {
          if (isCard(element)) {
            const citationNumber = element.props["data-citation-number"];
            return (
              citationNumber && parseInt(citationNumber) === selectedCitation
            );
          }
          return false;
        });
        if (feedbackElement && isCard(feedbackElement)) {
          const word = feedbackElement.props["data-word"];
          if (word && feedbackRefs[word]?.current) {
            setTimeout(() => {
              const element = feedbackRefs[word].current;
              if (element) {
                scrollTo(element);
                pulseHighlight(element);
              }
            }, 100);
          }
        }
      }
    }, [selectedCitation, feedbackRefs, childrenArr, scrollTo, pulseHighlight]);

    /* ---------- 🆕 Enhanced card border helper -------------------------------- */
    const borderTone = (w: string) => {
      // 🆕 Enhanced: Different border colors for different feedback types
      if (w.includes("missed-words-group")) {
        return "border-blue-200 hover:border-blue-300";
      }
      if (w.includes("extra-words-group")) {
        return "border-red-200 hover:border-red-300";
      }
      if (
        w.includes("congratulations") ||
        w.includes("perfect-recitation") ||
        w.includes("all-memorized")
      ) {
        return "border-green-200 hover:border-green-300";
      }
      // Default for pronunciation feedback and others
      return "border-gray-300 hover:border-gray-300";
    };

    /* ---------- preload common TTS once --------------------------- */
    useEffect(() => {
      if (!childrenArr.length) return;
      const t = setTimeout(() => {
        const phrases = [
          "This word needs correction.",
          "Perfect recitation!",
          "Missing Words",
          "Missed Words", // 🆕 Added for new missing words feedback
          "Words You Didn't Say", // 🆕 Added for new missing words feedback
          "Extra Words",
          "Different Letter Count",
          "You added extra letters that should be removed.",
          "You're missing letters that need to be added.",
          "Focus on correct pronunciation and try again.",
          "You said the wrong letter.",
          "You substituted the letter.",
          "This is a vowel error.",
          "This is a consonant error.",
          "near the start of the word",
          "in the middle of the word",
          "near the end of the word",
          "Feedback for word",
          "Pronunciation Details for Citation",
          "Good effort! Close to correct.",
          "You're making progress.",
          "You missed these words in your recitation.", // 🆕 Added for new missing words feedback
          "Try including these words in your next attempt.", // 🆕 Added for new missing words feedback
        ];
        const citationNumberPhrases = Array.from(
          { length: 10 },
          (_, i) => `Citation number ${i + 1}`
        );
        const allPhrases = [...phrases, ...citationNumberPhrases];
        batchPrefetchTTS(allPhrases, 3).catch(() => {});
      }, 600);
      return () => clearTimeout(t);
    }, [childrenArr]);

    /* ---------- render ------------------------------------------- */
    return (
      // --- MODIFIED: Removed bg-white to allow content area background to show through ---
      <div className="flex flex-col h-full min-h-0 rounded-xl shadow-sm overflow-hidden bg-transparent text-sm">
        {/* -------------------------------------------------------- */}
        {/*   CONTENT (with new background)                         */}
        {/* -------------------------------------------------------- */}
        <div
          className="flex-1 overflow-y-auto px-4 pb-6 pt-4 scrollbar-container"
          // --- ADDED: New background style applied here ---
          style={{
            backgroundColor: "#ffffff",
            backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
            backgroundRepeat: "repeat",
          }}
        >
          {loading ? (
            <div className="h-full flex items-center justify-center">
              <ThinkingIcon />
            </div>
          ) : !childrenArr.length ? (
            <div className="h-full flex items-center justify-center text-center text-gray-600">
              <div className="glowing-text-container relative">
                <p className="text-gray-700 text-lg">
                  Tap the microphone icon below to start recording...
                </p>
                <div className="glow-wave"></div>
              </div>
            </div>
          ) : currentArray.length === 0 ? (
            <div className="h-full flex flex-col items-center justify-center text-center space-y-2">
              <p className="text-gray-500 text-xl">
                {selectedCitation
                  ? "No feedback for this citation"
                  : "No feedback available"}
              </p>
              <p className="text-gray-400 text-base mb-4">
                {selectedCitation
                  ? "Switch to Overview to see general feedback"
                  : "Great job! No issues found"}
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {displayedText.map((node, i) => {
                if (!isCard(node)) return null;

                const w = node.props["data-word"] as string;
                const num = node.props["data-citation-number"];
                const selected =
                  num && selectedCitation && parseInt(num) === selectedCitation;

                const children = React.Children.toArray(node.props.children);
                const feedbackContent = children[0];
                if (!isCard(feedbackContent)) {
                  return null;
                }

                const content = React.cloneElement(feedbackContent, {
                  ...feedbackContent.props,
                  className: "feedback-content p-6 space-y-6",
                });

                return (
                  <div
                    key={`fb-${i}-${w}`}
                    ref={feedbackRefs[w]}
                    data-word={w}
                    data-citation-number={num}
                    // --- UNCHANGED: Cards remain white to contrast with new background ---
                    className={`bg-white rounded-3xl border shadow-sm
                                transition hover:translate-x-1 hover:shadow-md
                                ${borderTone(w)}
                                ${selected ? "ring-2 ring-indigo-400" : ""}
                                opacity-0 animate-slide-in`}
                    style={{ animationDelay: `${i * 90}ms` }}
                    // onClick handler removed as per previous request
                  >
                    {content}
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* 🆕 Enhanced hint footer with better messaging */}
        {!!totals.total && !loading && (
          <div className="border-t border-gray-100 py-2 text-center text-xs text-gray-500 bg-gray-50">
            {totals.missing > 0 && totals.pronunciation > 0 && totals.extra > 0
              ? `${totals.missing} missed, ${totals.pronunciation} pronunciation issues, ${totals.extra} extra words - tap for details`
              : totals.missing > 0 && totals.pronunciation > 0
              ? `${totals.missing} missed words, ${totals.pronunciation} pronunciation issues - tap for details`
              : totals.missing > 0 && totals.extra > 0
              ? `${totals.missing} missed words, ${totals.extra} extra words - tap for details`
              : totals.pronunciation > 0 && totals.extra > 0
              ? `${totals.pronunciation} pronunciation issues, ${totals.extra} extra words - tap for details`
              : totals.missing > 0
              ? `${totals.missing} missed word${
                  totals.missing > 1 ? "s" : ""
                } - tap for details`
              : totals.pronunciation > 0
              ? `${totals.pronunciation} pronunciation issue${
                  totals.pronunciation > 1 ? "s" : ""
                } - tap for details`
              : totals.extra > 0
              ? `${totals.extra} extra word${
                  totals.extra > 1 ? "s" : ""
                } - tap for details`
              : "Tip: click highlighted words or citation numbers for details"}
          </div>
        )}

        {/* ---------------- global styles ------------------------ */}
        <style jsx global>{`
          @keyframes slide-in {
            0% {
              opacity: 0;
              transform: translateY(10px);
            }
            100% {
              opacity: 1;
              transform: translateY(0);
            }
          }
          .animate-slide-in {
            animation: slide-in 0.5s ease-out forwards;
          }
          .scrollbar-container {
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
          }
          .scrollbar-container::-webkit-scrollbar {
            width: 8px;
          }
          .scrollbar-container::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.17);
            border-radius: 20px;
          }
          .citation-highlight {
            animation: highlight 2s ease-in-out;
          }
          @keyframes highlight {
            0% {
              box-shadow: 0 0 0 rgba(99, 102, 241, 0.6);
            }
            50% {
              box-shadow: 0 0 12px rgba(99, 102, 241, 0.8);
            }
            100% {
              box-shadow: 0 0 0 rgba(99, 102, 241, 0);
            }
          }
          .feedback-container .hover\\:translate-x-1,
          .feedback-container .hover\\:bg-white,
          .feedback-container .hover\\:scale-105,
          .feedback-container .hover\\:shadow-lg,
          .feedback-container .transform,
          .feedback-container .animate-slide-in,
          .feedback-container .opacity-0,
          .feedback-container .animate-delay-900 {
            animation: none !important;
            transition: none !important;
            transform: none !important;
            opacity: 1 !important;
          }
          .feedback-container > div {
            margin-bottom: 0 !important;
            box-shadow: none !important;
          }
          .glowing-text-container {
            position: relative;
            overflow: hidden;
            display: inline-block;
          }
          .glow-wave {
            position: absolute;
            top: 0;
            left: -40%;
            width: 40%;
            height: 100%;
            background: linear-gradient(
              90deg,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 0.8) 50%,
              rgba(255, 255, 255, 0) 100%
            );
            mix-blend-mode: overlay;
            filter: blur(3px);
            pointer-events: none;
            animation: moveGlow 6s linear infinite;
          }
          @keyframes moveGlow {
            0%,
            10% {
              left: -40%;
            }
            50% {
              left: 100%;
            }
            50.01%,
            100% {
              left: -40%;
            }
          }
          .highlighted {
            border-color: #e5e7eb !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
          }
        `}</style>
      </div>
    );
  }
);

FeedbackSection.displayName = "FeedbackSection";
export default FeedbackSection;

"use client";

import { useEffect, useState } from "react";
import { MobileSidebar } from "./mobile-sidebar";

export const MobileHeader = () => {
  const [render, setRender] = useState(false);

  useEffect(() => {
    // Client-side check to see if we are in an iframe or have the 'embedded' query param.
    const inIframe = window.self !== window.top;
    const hasEmbeddedQuery = new URLSearchParams(window.location.search).has(
      "embedded"
    );

    // Only render the header if not in an embedded context.
    if (!inIframe && !hasEmbeddedQuery) {
      setRender(true);
    }
  }, []);

  // Return null on the server and if embedded. This avoids hydration mismatches and flicker.
  if (!render) {
    return null;
  }

  // When used normally (direct navigation), it renders exactly as before.
  return (
    <nav className="lg:hidden px-6 h-[50px] flex items-center bg-[#1a1a18] border-b fixed top-0 w-full z-50">
      <MobileSidebar />
    </nav>
  );
};

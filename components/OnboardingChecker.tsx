// components/OnboardingChecker.tsx

"use client";

import { useEffect, useRef } from "react";
import { usePathname, useRouter } from "next/navigation";

/**
 * A client-side component that checks if the user has completed their
 * initial onboarding steps. If not, it redirects them to the join school page.
 * This component renders nothing and runs its logic after the initial page load
 * to avoid blocking server-side rendering.
 */
const OnboardingChecker = () => {
  const router = useRouter();
  const pathname = usePathname();

  // A ref is used to ensure the check only runs once, even with React's Strict Mode.
  const hasChecked = useRef(false);

  useEffect(() => {
    // Do not run the check if we are already on the page they need to go to.
    // This prevents a redirect loop.
    if (pathname === "/joinSchool") {
      return;
    }

    // Ensure the check only runs once per component mount.
    if (hasChecked.current) {
      return;
    }
    hasChecked.current = true;

    const checkStatus = async () => {
      try {
        console.log("[OnboardingChecker] Checking user onboarding status...");

        // This new API route encapsulates the logic that used to be in MainLayout.
        const response = await fetch("/api/users/onboarding-status");

        if (!response.ok) {
          // If the API call fails, log the error but don't redirect.
          console.error(
            "[OnboardingChecker] Failed to fetch onboarding status:",
            response.statusText
          );
          return;
        }

        const data = await response.json();

        // The core logic: if the API says the user is NOT onboarded, redirect.
        if (data.isOnboarded === false) {
          console.warn(
            "[OnboardingChecker] User is not onboarded. Redirecting to /joinSchool..."
          );
          router.push("/joinSchool");
        } else {
          // Otherwise, the user is fine. Do nothing.
          console.log(
            "[OnboardingChecker] User is fully onboarded. No action needed."
          );
        }
      } catch (error) {
        console.error(
          "[OnboardingChecker] An error occurred while checking status:",
          error
        );
      }
    };

    // Run the check.
    checkStatus();
  }, [pathname, router]);

  // This component does not render any UI.
  return null;
};

export default OnboardingChecker;

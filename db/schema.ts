import { relations } from "drizzle-orm";
import {
  boolean,
  integer,
  pgEnum,
  pgTable,
  primaryKey,
  serial,
  text,
  timestamp,
  // NEW: indexes for light data integrity on Speak Sequence options
  index,
  uniqueIndex,
} from "drizzle-orm/pg-core";

// --- New Curriculums Table ---
export const curriculums = pgTable("curriculums", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(), // Added unique constraint for ON CONFLICT usage
  description: text("description"),
});

// --- Tables Definitions ---

// Courses table (updated to include curriculum_id)
// Note: The curriculum_id column is added without calling .nullable(), making it nullable by default.
export const courses = pgTable("courses", {
  id: serial("id").primaryKey(),
  curriculumId: integer("curriculum_id").references(() => curriculums.id, {
    onDelete: "cascade",
  }),
  englishTitle: text("english_title").notNull(),
  description: text("description").notNull(),
  arabicTitle: text("arabic_title").notNull(),
});

// Add this to your schema file
export const users = pgTable("users", {
  userId: text("user_id").primaryKey(),
  email: text("email").notNull(),
  role: text("role").notNull().default("student"), // "student" or "teacher"
  displayName: text("display_name").default(""),
  avatarSrc: text("avatar_src").default(""), // <-- ADDED: avatar source column
  createdAt: timestamp("created_at").defaultNow(),

  // ========== ADDED: class credentials for teacher only ==========
  schoolUsername: text("school_username"), // e.g. "MATH101"
  schoolPassword: text("school_password"), // store hashed or plain if you prefer
});

// Surahs table (kept for compatibility, but no longer required for memorization)
export const surahs = pgTable("surahs", {
  id: serial("id").primaryKey(),
  number: integer("number").notNull(),
  name: text("name").notNull(),
  englishName: text("english_name").notNull(),
  revelationPlace: text("revelation_place").notNull(),
  numberOfAyahs: integer("number_of_ayahs").notNull(),
});

// Verses table with composite primary key (kept for compatibility, but no longer required for memorization)
export const verses = pgTable(
  "verses",
  {
    surahId: integer("surah_id")
      .references(() => surahs.id, { onDelete: "cascade" })
      .notNull(),
    verseNumber: integer("verse_number").notNull(),
    text: text("text").notNull(),
  },
  (table) => ({
    pk: primaryKey(table.surahId, table.verseNumber),
  })
);

// Units table
export const units = pgTable("units", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  courseId: integer("course_id")
    .references(() => courses.id, { onDelete: "cascade" })
    .notNull(),
  order: integer("order").notNull(),
});

// Lessons table
export const lessons = pgTable("lessons", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  unitId: integer("unit_id")
    .references(() => units.id, { onDelete: "cascade" })
    .notNull(),
  order: integer("order").notNull(),
});

// Challenges enumeration
export const challengesEnum = pgEnum("type", [
  "SELECT",
  "ASSIST",
  "MATCHING",
  "TAP_WHAT_YOU_HEAR",
  "IMAGE_AUDIO_SELECT",
  "DRAG_AND_DROP",
  "FILL_IN_THE_BLANK",
  "SPEAK_THIS",
  // NEWLY ADDED:
  "SPEAK_THIS_ADVANCED",
  // === NEWLY ADDED CHALLENGE TYPE (non-graded explainer/demo) ===
  "EXPLAINER",
  // === NEW: continuous speaking drill with auto-advance ===
  "SPEAK_SEQUENCE",
  // === NEW: SPEED ROUND modes ===
  "SPEED_SPOT_MISTAKE",
  "SPEED_MATCH_AUDIO",
]);

// Challenges table
export const challenges = pgTable("challenges", {
  id: serial("id").primaryKey(),
  lessonId: integer("lesson_id")
    .references(() => lessons.id, { onDelete: "cascade" })
    .notNull(),
  type: challengesEnum("type").notNull(),
  question: text("question").notNull(), // For EXPLAINER, this is the statement/heading
  order: integer("order").notNull(),
  audioSrc: text("audio_src"),
  mediaType: text("media_type"), // For EXPLAINER, set to 'image' (typically)
  mediaUrl: text("media_url"), // For EXPLAINER, the diagram/image URL
  topCardText: text("top_card_text"), // Optional body text for EXPLAINER
  topCardAudio: text("top_card_audio"), // Optional narration for EXPLAINER
  sentence: text("sentence"),

  // === NEW COLUMN to enable stacked layout ===
  optionsStacked: boolean("options_stacked").default(false),

  // === NEW OPTIONAL FLAGS for pedagogy/flow control (do not interfere with existing logic) ===
  // EXPLAINER screens are skippable and non-scoring; these help the app
  // switch button labels and ignore hearts/progress for these items.
  // (They default to false for all existing challenge types.)
  // You can rely on type==='EXPLAINER' if you prefer; these give you explicit toggles.
  isSkippable: boolean("is_skippable").default(false),
  isNonGraded: boolean("is_non_graded").default(false),

  // ====== NEW: SPEAK_SEQUENCE config (app-level enforcement) ======
  // Keep these generic so they don't break other types.
  // UI/BE should enforce itemCount exactly for SPEAK_SEQUENCE.
  speakItemCount: integer("speak_item_count").default(10), // Rule: 10 items
  speakTimeLimitSeconds: integer("speak_time_limit_seconds").default(60), // Later use
  speakContinuousCapture: boolean("speak_continuous_capture").default(true),
  speakAutoAdvanceOnPass: boolean("speak_auto_advance_on_pass").default(true),

  // ====== NEW: SPEED ROUND generic config ======
  speedItemCount: integer("speed_item_count").default(20),
  speedTimeLimitSeconds: integer("speed_time_limit_seconds").default(60),
});

// === NEW: Speed Item kind enum for SPEED ROUND challenges ===
export const speedItemKindEnum = pgEnum("speed_item_kind", [
  "spot_mistake",
  "match_audio",
]);

// === NEW: Speed Items table (one row per item in a speed round) ===
export const speedItems = pgTable(
  "speed_items",
  {
    id: serial("id").primaryKey(),
    challengeId: integer("challenge_id")
      .references(() => challenges.id, { onDelete: "cascade" })
      .notNull(),
    kind: speedItemKindEnum("kind").notNull(), // 'spot_mistake' | 'match_audio'
    audioSrc: text("audio_src").notNull(), // audio prompt
    audioText: text("audio_text"), // normalized text the audio says (optional but recommended)
    // Fields used by 'spot_mistake':
    displayText: text("display_text"), // what is shown to user
    isMatch: boolean("is_match"), // whether display matches audioText
    // Authoring/order controls:
    sequence: integer("sequence"),
    active: boolean("active").default(true),
  },
  (table) => ({
    ixChallengeKind: index("ix_speed_items_challenge_kind").on(
      table.challengeId,
      table.kind
    ),
    ixChallengeSeq: index("ix_speed_items_challenge_seq").on(
      table.challengeId,
      table.sequence
    ),
  })
);

// Challenge options table
export const challengeOptions = pgTable(
  "challenge_options",
  {
    id: serial("id").primaryKey(),
    challengeId: integer("challenge_id")
      .references(() => challenges.id, { onDelete: "cascade" })
      .notNull(),
    text: text("text").notNull(),
    correct: boolean("correct").notNull(),
    imageSrc: text("imagesrc"),
    audioSrc: text("audiosrc"),
    matchPairId: integer("match_pair_id"),
    side: text("side").default(""),
    sequence: integer("sequence"),

    // NEW: tie 4-choice options to a speed item (SPEED_MATCH_AUDIO only)
    speedItemId: integer("speed_item_id").references(() => speedItems.id, {
      onDelete: "cascade",
    }),
  },
  // Light integrity for sequencing & duplicates within a challenge
  (table) => ({
    // One sequence position per challenge (if provided)
    uqChallengeSequence: uniqueIndex("uq_challenge_options_challenge_seq")
      .on(table.challengeId, table.sequence)
      .where(sql`"sequence" IS NOT NULL`),
    // Avoid duplicate target text within a single challenge
    uqChallengeText: uniqueIndex("uq_challenge_options_challenge_text").on(
      table.challengeId,
      table.text
    ),
    // Fast lookup by challenge
    ixChallengeId: index("ix_challenge_options_challenge_id").on(
      table.challengeId
    ),
    // NEW: fast lookup by speed item (for 4-choice grouping)
    ixSpeedItemId: index("ix_challenge_options_speed_item_id").on(
      table.speedItemId
    ),
    // NEW: ensure exactly one correct option per speed item (when linked)
    uqSpeedItemCorrectTrue: uniqueIndex(
      "uq_challenge_options_speeditem_correct_true"
    )
      .on(table.speedItemId)
      .where(sql`"correct" = true AND "speed_item_id" IS NOT NULL`),
  })
);

// Needed for the WHERE in uniqueIndex above
import { sql } from "drizzle-orm";

// Challenge progress table
export const challengeProgress = pgTable("challenge_progress", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull(),
  challengeId: integer("challenge_id")
    .references(() => challenges.id, { onDelete: "cascade" })
    .notNull(),
  completed: boolean("completed").notNull().default(false),
});

// === NEW: Challenge sessions (per playthrough; used for SPEED rounds analytics) ===
export const challengeSessions = pgTable("challenge_sessions", {
  id: serial("id").primaryKey(),
  challengeId: integer("challenge_id")
    .references(() => challenges.id, { onDelete: "cascade" })
    .notNull(),
  userId: text("user_id")
    .references(() => users.userId, { onDelete: "cascade" })
    .notNull(),
  startedAt: timestamp("started_at").defaultNow().notNull(),
  endedAt: timestamp("ended_at"),
  durationMs: integer("duration_ms"),
  score: integer("score").default(0),
  itemsSeen: integer("items_seen").default(0),
  itemsCorrect: integer("items_correct").default(0),
  streakMax: integer("streak_max").default(0),
  deviceType: text("device_type"),
});

// === NEW: Attempts per item inside a session ===
export const challengeAttempts = pgTable(
  "challenge_attempts",
  {
    id: serial("id").primaryKey(),
    sessionId: integer("session_id")
      .references(() => challengeSessions.id, { onDelete: "cascade" })
      .notNull(),
    speedItemId: integer("speed_item_id")
      .references(() => speedItems.id, { onDelete: "cascade" })
      .notNull(),
    chosen: text("chosen").notNull(), // 'match' | 'mismatch' | 'option_a'|'option_b'|'option_c'|'option_d'
    isCorrect: boolean("is_correct").notNull(),
    latencyMs: integer("latency_ms"),
    startedAt: timestamp("started_at").defaultNow(),
    answeredAt: timestamp("answered_at"),
  },
  (table) => ({
    ixSession: index("ix_challenge_attempts_session").on(table.sessionId),
    ixSpeedItem: index("ix_challenge_attempts_speed_item").on(
      table.speedItemId
    ),
  })
);

// User progress table
export const userProgress = pgTable("user_progress", {
  userId: text("user_id").primaryKey(),
  userName: text("user_name").notNull().default("User"),
  userImageSrc: text("user_image_src").notNull().default("/mascot.svg"),
  activeCourseId: integer("active_course_id").references(() => courses.id, {
    onDelete: "cascade",
  }),
  hearts: integer("hearts").notNull().default(5),
  points: integer("points").notNull().default(0),
});

// User subscription table
export const userSubscription = pgTable("user_subscription", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull().unique(),
  stripeCustomerId: text("stripe_customer_id").notNull().unique(),
  stripeSubscriptionId: text("stripe_subscription_id").notNull().unique(),
  stripePriceId: text("stripe_price_id").notNull(),
  stripeCurrentPeriodEnd: timestamp("stripe_current_period_end").notNull(),
});

// Events table
export const events = pgTable("events", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description"),
  startTime: timestamp("start_time").notNull(),
  endTime: timestamp("end_time").notNull(),
  googleEventId: text("google_event_id").notNull(),
  calendarLink: text("calendar_link"),
  recurrenceRule: text("recurrence_rule"),
  // ADDED: We'll store a Jitsi room name or link and a flag if it's a meeting
  jitsiRoomName: text("jitsi_room_name"),
  isMeeting: boolean("is_meeting").default(false),
});

/*
  ================================
  OLD memorization tables referencing surahs/verses directly
  ================================
  We'll refactor them to use surahNumber & verseNumber
*/

// Memorization Progress Table (refactored to surahNumber, no foreign key to surahs)
export const memorizationProgress = pgTable("memorization_progress", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull(),
  surahNumber: integer("surah_number").notNull(), // replaced surahId
  completedVerses: integer("completed_verses").notNull().default(0),
  recitationAttempts: integer("recitation_attempts").notNull().default(0),
  score: integer("score").notNull().default(0),
  difficulty: text("difficulty").notNull(),
});

// memorizedVerses table (refactored to surahNumber, no foreign key to verses)
export const memorizedVerses = pgTable(
  "memorized_verses",
  {
    userId: text("user_id").notNull(),
    surahNumber: integer("surah_number").notNull(), // replaced surahId
    verseNumber: integer("verse_number").notNull(),
    dateMemorized: timestamp("date_memorized").notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey(table.userId, table.surahNumber, table.verseNumber),
  })
);

// Memorization Sessions Table (refactored to surahNumber)
export const memorizationSessions = pgTable("memorization_sessions", {
  sessionId: serial("session_id").primaryKey(),
  userId: text("user_id").notNull(),
  surahNumber: integer("surah_number").notNull(), // replaced surahId
  versesRange: text("verses_range").notNull(),
  startTime: timestamp("start_time").notNull(),
  endTime: timestamp("end_time").notNull(),
  result: text("result").notNull(),
  difficulty: text("difficulty").notNull(),
});

// Memorization Feedback Table (refactored to surahNumber, verseNumber)
export const memorizationFeedback = pgTable("memorization_feedback", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull(),
  sessionId: integer("session_id")
    .references(() => memorizationSessions.sessionId, {
      onDelete: "cascade",
    })
    .notNull(),
  surahNumber: integer("surah_number").notNull(), // replaced surahId
  verseNumber: integer("verse_number").notNull(),
  feedbackType: text("feedback_type").notNull(),
  feedbackDetails: text("feedback_details").notNull(),
});

// Memorized Surahs Table (refactored to surahNumber)
export const memorizedSurahs = pgTable("memorized_surahs", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull(),
  surahNumber: integer("surah_number").notNull(), // replaced surahId
  dateMemorized: timestamp("date_memorized").notNull().defaultNow(),
});

// Chapters table
export const chapters = pgTable("chapters", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  order: integer("order").notNull().default(1),
});

// Squares table
export const squares = pgTable("squares", {
  id: serial("id").primaryKey(),
  chapterId: integer("chapter_id")
    .references(() => chapters.id, { onDelete: "cascade" })
    .notNull(),
  squareNumber: integer("square_number").notNull(),
  content: text("content").notNull(),
  transliteration: text("transliteration").default(""), // New Column
  // ...other columns like audioUrl or group if needed...
});

// ========== ADDED: Teacher-Student Pivot Table ==========
export const teacherStudents = pgTable(
  "teacher_students",
  {
    teacherId: text("teacher_id")
      .references(() => users.userId, { onDelete: "cascade" })
      .notNull(),
    studentId: text("student_id")
      .references(() => users.userId, { onDelete: "cascade" })
      .notNull(),
  },
  (table) => ({
    pk: primaryKey(table.teacherId, table.studentId),
  })
);

/* 
  ===========================================================
  ADDED: messages - store chat messages for 30 days
  You can run a scheduled job to delete messages older than 30 days
  ===========================================================
*/
export const messages = pgTable("messages", {
  id: serial("id").primaryKey(),
  senderId: text("sender_id")
    .references(() => users.userId, { onDelete: "cascade" })
    .notNull(),
  recipientId: text("recipient_id")
    .references(() => users.userId, { onDelete: "cascade" })
    .notNull(),
  content: text("content").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  // You might add a 'read' column, attachments, etc.

  // NEWLY ADDED: Track when the message was read (null = unread).
  readAt: timestamp("read_at"),
});

/* 
  ===========================================================
  ADDED: eventParticipants - link multiple participants to an event
  Teacher can invite multiple students
  ===========================================================
*/
export const eventParticipants = pgTable(
  "event_participants",
  {
    eventId: integer("event_id")
      .references(() => events.id, { onDelete: "cascade" })
      .notNull(),
    userId: text("user_id")
      .references(() => users.userId, { onDelete: "cascade" })
      .notNull(),
  },
  (table) => ({
    pk: primaryKey(table.eventId, table.userId),
  })
);

/* 
  ===========================================================
  ADDED: homeworks - track assigned tasks from teacher to student
  We'll keep it simple for now
  ===========================================================
*/
export const homeworks = pgTable("homeworks", {
  id: serial("id").primaryKey(),
  teacherId: text("teacher_id")
    .references(() => users.userId, { onDelete: "cascade" })
    .notNull(),
  studentId: text("student_id")
    .references(() => users.userId, { onDelete: "cascade" })
    .notNull(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  assignedAt: timestamp("assigned_at").defaultNow().notNull(),
  dueDate: timestamp("due_date"),
  completed: boolean("completed").default(false),
});

// --- Relations Definitions ---

// Keep old surahsRelations, versesRelations, etc. for reference. They are no longer used in memorization logic but do not break the schema if you wish to keep them.
export const surahsRelations = relations(surahs, ({ many }) => ({
  verses: many(verses),
}));

export const versesRelations = relations(verses, ({ one }) => ({
  surah: one(surahs, {
    fields: [verses.surahId],
    references: [surahs.id],
  }),
}));

// ENHANCED: Updated courses relations to include curriculum
export const coursesRelations = relations(courses, ({ one, many }) => ({
  // NEW: Add curriculum relation
  curriculum: one(curriculums, {
    fields: [courses.curriculumId],
    references: [curriculums.id],
  }),
  // Existing relations
  userProgress: many(userProgress),
  units: many(units),
}));

// NEW: Add curriculums relations
export const curriculumsRelations = relations(curriculums, ({ many }) => ({
  courses: many(courses),
}));

export const unitsRelations = relations(units, ({ many, one }) => ({
  course: one(courses, {
    fields: [units.courseId],
    references: [courses.id],
  }),
  lessons: many(lessons),
}));

export const lessonsRelations = relations(lessons, ({ one, many }) => ({
  unit: one(units, {
    fields: [lessons.unitId],
    references: [units.id],
  }),
  challenges: many(challenges),
}));

export const challengesRelations = relations(challenges, ({ one, many }) => ({
  lesson: one(lessons, {
    fields: [challenges.lessonId],
    references: [lessons.id],
  }),
  challengeOptions: many(challengeOptions),
  challengeProgress: many(challengeProgress),
  // NEW:
  speedItems: many(speedItems),
  challengeSessions: many(challengeSessions),
}));

export const speedItemsRelations = relations(speedItems, ({ one, many }) => ({
  challenge: one(challenges, {
    fields: [speedItems.challengeId],
    references: [challenges.id],
  }),
  options: many(challengeOptions),
  attempts: many(challengeAttempts),
}));

export const challengeOptionsRelations = relations(
  challengeOptions,
  ({ one }) => ({
    challenge: one(challenges, {
      fields: [challengeOptions.challengeId],
      references: [challenges.id],
    }),
    // NEW: optional link to speed item (for SPEED_MATCH_AUDIO)
    speedItem: one(speedItems, {
      fields: [challengeOptions.speedItemId],
      references: [speedItems.id],
    }),
  })
);

export const challengeProgressRelations = relations(
  challengeProgress,
  ({ one }) => ({
    challenge: one(challenges, {
      fields: [challengeProgress.challengeId],
      references: [challenges.id],
    }),
  })
);

export const challengeSessionsRelations = relations(
  challengeSessions,
  ({ one, many }) => ({
    challenge: one(challenges, {
      fields: [challengeSessions.challengeId],
      references: [challenges.id],
    }),
    user: one(users, {
      fields: [challengeSessions.userId],
      references: [users.userId],
    }),
    attempts: many(challengeAttempts),
  })
);

export const challengeAttemptsRelations = relations(
  challengeAttempts,
  ({ one }) => ({
    session: one(challengeSessions, {
      fields: [challengeAttempts.sessionId],
      references: [challengeSessions.id],
    }),
    speedItem: one(speedItems, {
      fields: [challengeAttempts.speedItemId],
      references: [speedItems.id],
    }),
  })
);

export const userProgressRelations = relations(userProgress, ({ one }) => ({
  activeCourse: one(courses, {
    fields: [userProgress.activeCourseId],
    references: [courses.id],
  }),
}));

// ENHANCED: Chapter relations
export const chaptersRelations = relations(chapters, ({ many }) => ({
  squares: many(squares),
}));

export const squaresRelations = relations(squares, ({ one }) => ({
  chapter: one(chapters, {
    fields: [squares.chapterId],
    references: [chapters.id],
  }),
}));

// FIXED: Teacher-Student pivot relations with proper relationName matching
export const teacherStudentsRelations = relations(
  teacherStudents,
  ({ one }) => ({
    teacher: one(users, {
      fields: [teacherStudents.teacherId],
      references: [users.userId],
      relationName: "teacherStudentTeacher",
    }),
    student: one(users, {
      fields: [teacherStudents.studentId],
      references: [users.userId],
      relationName: "teacherStudentStudent",
    }),
  })
);

// FIXED: Users relations with proper relationName matching
export const usersRelations = relations(users, ({ many }) => ({
  // Teacher side relations
  studentsAsTeacher: many(teacherStudents, {
    relationName: "teacherStudentTeacher",
  }),
  // Student side relations
  teachersAsStudent: many(teacherStudents, {
    relationName: "teacherStudentStudent",
  }),
  // Message relations
  sentMessages: many(messages, {
    relationName: "messageSender",
  }),
  receivedMessages: many(messages, {
    relationName: "messageRecipient",
  }),
  // Event participation
  eventParticipations: many(eventParticipants),
  // Homework relations
  assignedHomeworks: many(homeworks, {
    relationName: "homeworkTeacher",
  }),
  receivedHomeworks: many(homeworks, {
    relationName: "homeworkStudent",
  }),
}));

// FIXED: Messages relations with proper relationName matching
export const messagesRelations = relations(messages, ({ one }) => ({
  sender: one(users, {
    fields: [messages.senderId],
    references: [users.userId],
    relationName: "messageSender",
  }),
  recipient: one(users, {
    fields: [messages.recipientId],
    references: [users.userId],
    relationName: "messageRecipient",
  }),
}));

// ENHANCED: Event and event participants relations
export const eventsRelations = relations(events, ({ many }) => ({
  participants: many(eventParticipants),
}));

export const eventParticipantsRelations = relations(
  eventParticipants,
  ({ one }) => ({
    event: one(events, {
      fields: [eventParticipants.eventId],
      references: [events.id],
    }),
    user: one(users, {
      fields: [eventParticipants.userId],
      references: [users.userId],
    }),
  })
);

// FIXED: Homework relations with proper relationName matching
export const homeworksRelations = relations(homeworks, ({ one }) => ({
  teacher: one(users, {
    fields: [homeworks.teacherId],
    references: [users.userId],
    relationName: "homeworkTeacher",
  }),
  student: one(users, {
    fields: [homeworks.studentId],
    references: [users.userId],
    relationName: "homeworkStudent",
  }),
}));

// ENHANCED: Memorization-related relations (though they now use surahNumber instead of foreign keys)
export const memorizationProgressRelations = relations(
  memorizationProgress,
  ({ one }) => ({
    // Note: No direct relation to surahs table since we use surahNumber
    // But we can still relate to user if needed
  })
);

export const memorizedVersesRelations = relations(
  memorizedVerses,
  ({ one }) => ({
    // Note: No direct relations since we use surahNumber and verseNumber
    // But could add user relation if needed
  })
);

export const memorizationSessionsRelations = relations(
  memorizationSessions,
  ({ one, many }) => ({
    // Note: No direct relation to surahs since we use surahNumber
    feedbacks: many(memorizationFeedback),
  })
);

export const memorizationFeedbackRelations = relations(
  memorizationFeedback,
  ({ one }) => ({
    session: one(memorizationSessions, {
      fields: [memorizationFeedback.sessionId],
      references: [memorizationSessions.sessionId],
    }),
    // Note: No direct relation to verses since we use surahNumber and verseNumber
  })
);

export const memorizedSurahsRelations = relations(
  memorizedSurahs,
  ({ one }) => ({
    // Note: No direct relation to surahs since we use surahNumber
  })
);

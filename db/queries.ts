/* eslint-disable */

import { cache } from "react";
import { eq, and, gte, lte, desc, inArray } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import {
  challengeProgress,
  courses, // Keep this
  lessons,
  units, // Keep this (from units table)
  userProgress,
  userSubscription,
  challengeOptions,
  challenges,
  events,
  surahs,
  verses,
  memorizationProgress,
  memorizationSessions,
  memorizationFeedback,
  chapters,
  squares,
  memorizedVerses,
  teacherStudents,
  eventParticipants,
  curriculums,

  // ▼ NEW: speed-round tables (align with your schema exports)
  speedItems,
  challengeSessions,
  challengeAttempts,
} from "@/db/schema";

// Event functions
export const createEvent = async (eventData: any) => {
  console.log("[queries.ts] createEvent called with:", eventData);
  const { invitedStudentIds, ...otherFields } = eventData;
  console.log(
    "[queries.ts] About to insert event with isMeeting:",
    otherFields.isMeeting,
    " and jitsiRoomName:",
    otherFields.jitsiRoomName
  );
  const newEvent = await db
    .insert(events)
    .values({ ...otherFields })
    .returning();
  const createdEvent = newEvent[0];
  console.log("[queries.ts] Inserted new event ID:", createdEvent.id);
  if (Array.isArray(invitedStudentIds) && invitedStudentIds.length > 0) {
    console.log(
      "[queries.ts] Will insert eventParticipants for these students:",
      invitedStudentIds
    );
    const participantsData = invitedStudentIds.map((studentId: string) => ({
      eventId: createdEvent.id,
      userId: studentId,
    }));
    const insertedParticipants = await db
      .insert(eventParticipants)
      .values(participantsData)
      .returning();
    console.log(
      "[queries.ts] Inserted eventParticipants rows count:",
      insertedParticipants.length
    );
  }
  return createdEvent;
};

export const getEvents = async () => {
  return await db.query.events.findMany({
    orderBy: (events, { asc }) => [asc(events.startTime)],
  });
};

export const getEventById = async (eventId: number) => {
  return await db.query.events.findFirst({
    where: eq(events.id, eventId),
  });
};

export const getEventsByDateRange = async (
  startDate: string,
  endDate: string
) => {
  return await db.query.events.findMany({
    where: (events) =>
      gte(events.startTime, new Date(startDate)) &&
      lte(events.endTime, new Date(endDate)),
  });
};

export const updateEventById = async (eventId: number, eventData: any) => {
  console.log("[queries.ts] updateEventById called with:", eventId, eventData);
  const updatedEvent = await db
    .update(events)
    .set({ ...eventData })
    .where(eq(events.id, eventId))
    .returning();
  return updatedEvent[0];
};

export const deleteEventById = async (eventId: number) => {
  const deletedEvent = await db
    .delete(events)
    .where(eq(events.id, eventId))
    .returning();
  return deletedEvent[0];
};

export const getUserProgress = cache(async () => {
  const { userId } = await auth();
  if (!userId) {
    console.log(
      "[queries.ts -> getUserProgress] No userId found, returning null."
    );
    return null;
  }
  let data = await db.query.userProgress.findFirst({
    where: eq(userProgress.userId, userId),
    with: { activeCourse: true },
  });
  if (!data) {
    console.log(
      "[queries.ts -> getUserProgress] No user_progress row found for userId:",
      userId
    );
    console.log(
      "[queries.ts -> getUserProgress] Auto-creating a new user_progress row..."
    );
    try {
      await db.insert(userProgress).values({
        userId,
        userName: "NewUser",
        userImageSrc: "/mascot.svg",
        hearts: 5,
        points: 0,
      });
      data = await db.query.userProgress.findFirst({
        where: eq(userProgress.userId, userId),
        with: { activeCourse: true },
      });
      console.log(
        "[queries.ts -> getUserProgress] Created user_progress row:",
        data
      );
    } catch (error) {
      console.error(
        "[queries.ts -> getUserProgress] Error creating user_progress row:",
        error
      );
      return null; // Return null if creation fails
    }
  }
  return data;
});

export const getSurahs = async () => {
  return await db.query.surahs.findMany({
    orderBy: (surahs, { asc }) => [asc(surahs.number)],
    columns: {
      id: true,
      number: true,
      name: true,
      englishName: true,
      revelationPlace: true,
      numberOfAyahs: true,
    },
  });
};

export const getSurahById = async (surahId: number) => {
  const surah = await db.query.surahs.findFirst({
    where: eq(surahs.id, surahId),
    with: {
      verses: { orderBy: (verses, { asc }) => [asc(verses.verseNumber)] },
    },
  });
  return surah;
};

export const getVerse = async (surahId: number, verseNumber: number) => {
  return await db.query.verses.findFirst({
    where: and(
      eq(verses.surahId, surahId),
      eq(verses.verseNumber, verseNumber)
    ),
  });
};

export const checkMemorizedVerse = async (
  userId: string,
  surahNumber: number,
  verseNumber: number
) => {
  return await db.query.memorizedVerses.findFirst({
    where: and(
      eq(memorizedVerses.userId, userId),
      eq(memorizedVerses.surahNumber, surahNumber),
      eq(memorizedVerses.verseNumber, verseNumber)
    ),
  });
};

export const getMemorizedVersesBySurah = async (
  userId: string,
  surahNumber: number
) => {
  return await db.query.memorizedVerses.findMany({
    where: and(
      eq(memorizedVerses.userId, userId),
      eq(memorizedVerses.surahNumber, surahNumber)
    ),
    orderBy: (mv, { asc }) => [asc(mv.verseNumber)],
  });
};

export const upsertMemorizationProgress = async (progressData: {
  userId: string;
  surahNumber: number;
  completedVerses?: number;
  recitationAttempts?: number;
  score?: number;
  difficulty?: string;
}) => {
  const existingProgress = await db.query.memorizationProgress.findFirst({
    where: and(
      eq(memorizationProgress.userId, progressData.userId),
      eq(memorizationProgress.surahNumber, progressData.surahNumber)
    ),
  });
  if (existingProgress) {
    const updatedProgress = await db
      .update(memorizationProgress)
      .set({
        completedVerses:
          progressData.completedVerses ?? existingProgress.completedVerses,
        recitationAttempts:
          progressData.recitationAttempts ??
          existingProgress.recitationAttempts,
        score: progressData.score ?? existingProgress.score,
        difficulty: progressData.difficulty ?? existingProgress.difficulty,
      })
      .where(
        and(
          eq(memorizationProgress.userId, progressData.userId),
          eq(memorizationProgress.surahNumber, progressData.surahNumber)
        )
      )
      .returning();
    return updatedProgress[0];
  } else {
    const newProgress = await db
      .insert(memorizationProgress)
      .values({
        userId: progressData.userId,
        surahNumber: progressData.surahNumber,
        completedVerses: progressData.completedVerses ?? 0,
        recitationAttempts: progressData.recitationAttempts ?? 0,
        score: progressData.score ?? 0,
        difficulty: progressData.difficulty ?? "easy",
      })
      .returning();
    return newProgress[0];
  }
};

export const getMemorizationProgress = cache(
  async (userId: string, surahNumber: number) => {
    const progress = await db.query.memorizationProgress.findFirst({
      where: and(
        eq(memorizationProgress.userId, userId),
        eq(memorizationProgress.surahNumber, surahNumber)
      ),
    });
    return progress;
  }
);

export const createMemorizationSession = async (sessionData: {
  userId: string;
  surahNumber: number;
  versesRange: string;
  startTime: Date;
  endTime: Date;
  result: string;
  difficulty?: string;
}) => {
  const newSession = await db
    .insert(memorizationSessions)
    .values({ ...sessionData, difficulty: sessionData.difficulty ?? "easy" })
    .returning();
  return newSession[0];
};

export const getMemorizationSessionsBySurah = async (
  userId: string,
  surahNumber: number
) => {
  return await db.query.memorizationSessions.findMany({
    where: and(
      eq(memorizationSessions.userId, userId),
      eq(memorizationSessions.surahNumber, surahNumber)
    ),
    orderBy: (ms, { desc }) => [desc(ms.startTime)],
  });
};

export const recordMemorizationFeedback = async (feedbackData: {
  sessionId: number;
  surahNumber: number;
  verseNumber: number;
  feedbackType: string;
  feedbackDetails: string;
}) => {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");
  const newFeedback = await db
    .insert(memorizationFeedback)
    .values({ ...feedbackData, userId })
    .returning();
  return newFeedback[0];
};

export const getMemorizationFeedback = cache(async (sessionId: number) => {
  return await db.query.memorizationFeedback.findMany({
    where: eq(memorizationFeedback.sessionId, sessionId),
    with: { session: true },
  });
});

export const getCourses = cache(async () => {
  console.log("[getCourses] Fetching all courses from 'courses' table."); // Added log
  const data = await db.query.courses.findMany({
    columns: {
      id: true,
      englishTitle: true,
      arabicTitle: true,
      description: true,
      curriculumId: true,
    },
  });
  console.log(`[getCourses] Found ${data.length} courses.`); // Added log
  return data;
});

// ENHANCED getCourseById with logging
export const getCourseById = cache(async (courseId: number) => {
  console.log(
    `[getCourseById] Attempting to fetch course with ID: ${courseId} from 'courses' table.`
  );
  const data = await db.query.courses.findFirst({
    where: eq(courses.id, courseId),
    columns: {
      id: true,
      englishTitle: true,
      arabicTitle: true,
      description: true,
      curriculumId: true,
    },
    with: {
      units: {
        orderBy: (units, { asc }) => [asc(units.order)],
        with: {
          lessons: {
            orderBy: (lessons, { asc }) => [asc(lessons.order)],
          },
        },
      },
    },
  });
  if (data) {
    console.log(
      `[getCourseById] Found course for ID ${courseId}: ${data.englishTitle}`
    );
  } else {
    console.warn(`[getCourseById] Course NOT FOUND for ID ${courseId}.`);
  }
  return data;
});

export const getCoursesByCurriculum = cache(async (curriculumId: number) => {
  console.log(
    "[getCoursesByCurriculum] Looking for curriculumId:",
    curriculumId
  );
  const coursesData = await db.query.courses.findMany({
    where: eq(courses.curriculumId, curriculumId),
    columns: {
      id: true,
      englishTitle: true,
      arabicTitle: true,
      description: true,
      curriculumId: true,
    },
  });
  console.log("[getCoursesByCurriculum] Found courses:", coursesData.length);
  return coursesData;
});

export const getCurriculumById = cache(async (curriculumId: number) => {
  console.log("[getCurriculumById] Looking for curriculumId:", curriculumId);
  try {
    const curriculum = await db.query.curriculums.findFirst({
      where: eq(curriculums.id, curriculumId),
      columns: { id: true, name: true, description: true },
    });
    console.log("[getCurriculumById] Found curriculum:", curriculum);
    if (!curriculum) {
      console.log(
        "[getCurriculumById] No curriculum found with id:",
        curriculumId
      );
      return null;
    }
    const result = {
      id: curriculum.id,
      englishTitle: curriculum.name,
      arabicTitle: curriculum.name,
      description: curriculum.description || "Interactive learning content",
      curriculumId: curriculum.id,
      curriculum: {
        id: curriculum.id,
        name: curriculum.name,
        description: curriculum.description,
      },
    };
    console.log("[getCurriculumById] Returning result:", {
      id: result.id,
      title: result.englishTitle,
      curriculumName: result.curriculum.name,
    });
    return result;
  } catch (error) {
    console.error("[getCurriculumById] Error:", error);
    return null;
  }
});

export const getCourseByCurriculum = cache(async (curriculumId: number) => {
  console.log(
    "[getCourseByCurriculum] DEPRECATED - redirecting to getCurriculumById"
  );
  return await getCurriculumById(curriculumId);
});

export const getCurriculums = cache(async () => {
  console.log("[getCurriculums] Fetching all curriculums...");
  try {
    const curriculumsData = await db.query.curriculums.findMany({
      columns: { id: true, name: true, description: true },
      orderBy: (curriculums, { asc }) => [asc(curriculums.id)],
    });
    console.log("[getCurriculums] Found curriculums:", {
      count: curriculumsData.length,
      ids: curriculumsData.map((c) => c.id),
      names: curriculumsData.map((c) => c.name),
    });
    return curriculumsData;
  } catch (error) {
    console.error("[getCurriculums] Error fetching curriculums:", error);
    return [];
  }
});

const DAY_IN_MS = 86_400_000;

export const getCourseProgress = cache(async () => {
  const { userId } = await auth();
  const userProgressData = await getUserProgress();
  if (!userId || !userProgressData?.activeCourseId) {
    console.log(
      "[getCourseProgress] No userId or no activeCourseId in userProgress, returning null."
    );
    return null;
  }
  console.log(
    `[getCourseProgress] Fetching units for activeCourseId: ${userProgressData.activeCourseId}`
  );
  const unitsInActiveCourse = await db.query.units.findMany({
    orderBy: (units, { asc }) => [asc(units.order)],
    where: eq(units.courseId, userProgressData.activeCourseId),
    with: {
      lessons: {
        orderBy: (lessons, { asc }) => [asc(lessons.order)],
        with: {
          unit: true,
          challenges: {
            with: {
              challengeProgress: {
                where: eq(challengeProgress.userId, userId),
              },
              // ORDER options by sequence (then id) for all types.
              challengeOptions: {
                orderBy: (co, { asc }) => [asc(co.sequence), asc(co.id)],
              },
            },
          },
        },
      },
    },
  });
  console.log(
    `[getCourseProgress] Found ${unitsInActiveCourse.length} units in active course.`
  );

  // 🔧 Respect EXPLAINER / non-graded items when deciding the next active lesson
  const firstUncompletedLesson = unitsInActiveCourse
    .flatMap((unit) => unit.lessons)
    .map((lesson: any) => {
      // Soft enforcement for SPEAK_SEQUENCE: trim to item count if extra
      const fixedChallenges =
        Array.isArray(lesson.challenges) && lesson.challenges.length > 0
          ? lesson.challenges.map((ch: any) => {
              if (ch.type === "SPEAK_SEQUENCE") {
                const targetCount =
                  (ch as any).speakItemCount ??
                  (ch as any).speak_item_count ??
                  10;
                const opts = Array.isArray(ch.challengeOptions)
                  ? ch.challengeOptions
                  : [];
                if (opts.length > targetCount) {
                  console.warn(
                    `[getCourseProgress] SPEAK_SEQUENCE challenge ${ch.id} has ${opts.length} options; trimming to ${targetCount}.`
                  );
                } else if (opts.length < targetCount) {
                  console.warn(
                    `[getCourseProgress] SPEAK_SEQUENCE challenge ${ch.id} has only ${opts.length}/${targetCount} options.`
                  );
                }
                return {
                  ...ch,
                  challengeOptions: opts.slice(0, targetCount),
                };
              }
              return ch;
            })
          : lesson.challenges;
      return { ...lesson, challenges: fixedChallenges };
    })
    .find((lesson) => {
      const gradedChallenges = lesson.challenges.filter(
        (ch: any) => !(ch as any).isNonGraded
      );
      if (gradedChallenges.length === 0) {
        // If a lesson only has non-graded explainers, treat it as completed for progression.
        return false;
      }
      return gradedChallenges.some((challenge: any) => {
        return (
          !challenge.challengeProgress ||
          challenge.challengeProgress.length === 0 ||
          challenge.challengeProgress.some(
            (progress: any) => progress.completed === false
          )
        );
      });
    });

  console.log(
    `[getCourseProgress] First uncompleted lesson ID: ${firstUncompletedLesson?.id}`
  );
  return {
    activeLesson: firstUncompletedLesson,
    activeLessonId: firstUncompletedLesson?.id,
  };
});

export const getUserSubscription = cache(async () => {
  const { userId } = await auth();
  if (!userId) return null;
  const data = await db.query.userSubscription.findFirst({
    where: eq(userSubscription.userId, userId),
  });
  if (!data) return null;
  const isActive =
    data.stripePriceId &&
    data.stripeCurrentPeriodEnd?.getTime()! + DAY_IN_MS > Date.now();
  return { ...data, isActive: !!isActive };
});

export const getTopTenUsers = cache(async () => {
  const { userId } = await auth();
  if (!userId) return [];
  const data = await db.query.userProgress.findMany({
    orderBy: (userProgress, { desc }) => [desc(userProgress.points)],
    limit: 10,
    columns: { userId: true, userName: true, userImageSrc: true, points: true },
  });
  return data;
});

export const getMatchingPairs = cache(async (challengeId: number) => {
  const pairs = await db.query.challengeOptions.findMany({
    where: eq(challengeOptions.challengeId, challengeId),
    orderBy: (challengeOptions, { asc }) => [asc(challengeOptions.matchPairId)],
  });
  return pairs;
});

export const getDragAndDropOptions = cache(async (challengeId: number) => {
  const options = await db.query.challengeOptions.findMany({
    where: eq(challengeOptions.challengeId, challengeId),
    orderBy: (challengeOptions, { asc }) => [asc(challengeOptions.sequence)],
  });
  return options;
});

// NEW: Speak Sequence helpers
export const getSpeakSequenceOptions = cache(async (challengeId: number) => {
  // Always returns options ordered by sequence then id.
  const opts = await db.query.challengeOptions.findMany({
    where: eq(challengeOptions.challengeId, challengeId),
    orderBy: (co, { asc }) => [asc(co.sequence), asc(co.id)],
    columns: {
      id: true,
      challengeId: true,
      text: true,
      audioSrc: true,
      sequence: true,
      correct: true,
    },
  });
  return opts;
});

export const getSpeakSequenceMeta = cache(async (challengeId: number) => {
  const row = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
    columns: {
      id: true,
      type: true,
      // expose new speak_* fields
      speakItemCount: true as any,
      speakTimeLimitSeconds: true as any,
      speakContinuousCapture: true as any,
      speakAutoAdvanceOnPass: true as any,
      // also existing fields you already use
      lessonId: true,
      question: true,
      audioSrc: true,
      sentence: true,
      isSkippable: true,
      isNonGraded: true,
      optionsStacked: true,
      order: true,
      mediaType: true,
      mediaUrl: true,
      topCardText: true,
      topCardAudio: true,
      // ▼ NEW: include SPEED config as well (harmless for other types)
      speedItemCount: true as any,
      speedTimeLimitSeconds: true as any,
    },
  });
  if (!row) return null;
  const options = await getSpeakSequenceOptions(challengeId);
  const expected =
    (row as any).speakItemCount ?? (row as any).speak_item_count ?? 10;
  return {
    ...row,
    optionCount: options.length,
    expectedCount: expected,
    ok: options.length === expected,
  };
});

// Fetch units - Enhanced with more logging and speak_* columns
export const getUnits = cache(async (filterByCourseId?: number) => {
  const { userId } = await auth();
  const userProgressData = await getUserProgress();

  if (!userId) {
    console.log("[getUnits] No userId, returning empty array.");
    return [];
  }

  const courseIdToFetch =
    filterByCourseId !== undefined && filterByCourseId !== null
      ? filterByCourseId
      : userProgressData?.activeCourseId;

  if (!courseIdToFetch) {
    console.log(
      `[getUnits] No courseId available to fetch units for. filterByCourseId: ${filterByCourseId}, userProgressData?.activeCourseId: ${userProgressData?.activeCourseId}. Returning empty array.`
    );
    return [];
  }

  console.log(
    `[getUnits] Attempting to fetch units for effective courseId: ${courseIdToFetch}`
  );

  const data = await db.query.units.findMany({
    orderBy: (units, { asc }) => [asc(units.order)],
    where: eq(units.courseId, courseIdToFetch),
    with: {
      lessons: {
        orderBy: (lessons, { asc }) => [asc(lessons.order)],
        with: {
          challenges: {
            orderBy: (challenges, { asc }) => [asc(challenges.order)],
            with: {
              challengeProgress: {
                where: eq(challengeProgress.userId, userId),
              },
              // Ensure ordered options everywhere (sequence, then id)
              challengeOptions: {
                orderBy: (co, { asc }) => [asc(co.sequence), asc(co.id)],
              },
            },
            columns: {
              id: true,
              lessonId: true,
              type: true,
              question: true,
              order: true,
              audioSrc: true,
              mediaType: true,
              mediaUrl: true,
              topCardText: true,
              topCardAudio: true,
              sentence: true,
              optionsStacked: true,
              // ⭐ include new flags
              isSkippable: true,
              isNonGraded: true,
              // ⭐ include new SPEAK_SEQUENCE configs
              speakItemCount: true as any,
              speakTimeLimitSeconds: true as any,
              speakContinuousCapture: true as any,
              speakAutoAdvanceOnPass: true as any,
              // ▼ include SPEED config (safe for other types)
              speedItemCount: true as any,
              speedTimeLimitSeconds: true as any,
            },
          },
        },
      },
    },
  });

  console.log(
    `[getUnits] Raw data fetched from DB for courseId ${courseIdToFetch}. Units found: ${data.length}`
  );
  if (data.length > 0) {
    data.forEach((unit) => {
      console.log(
        `[getUnits] Raw Unit ID: ${unit.id}, Title: ${unit.title}, Lessons count: ${unit.lessons.length}`
      );
      if (unit.lessons.length > 0) {
        unit.lessons.forEach((lesson) => {
          console.log(
            `  [getUnits]   Raw Lesson ID: ${lesson.id}, Title: ${lesson.title}, Challenges count: ${lesson.challenges.length}`
          );
        });
      } else {
        console.log(
          `  [getUnits]   Raw Unit ID: ${unit.id} has no lessons in raw data.`
        );
      }
    });
  }

  // Soft normalization for Speak Sequence (trim extras; warn if fewer)
  const normalizedData = data.map((unit) => {
    const lessonsWithCompletedStatus = unit.lessons.map((lesson: any) => {
      const fixedChallenges =
        Array.isArray(lesson.challenges) && lesson.challenges.length > 0
          ? lesson.challenges.map((ch: any) => {
              if (ch.type === "SPEAK_SEQUENCE") {
                const targetCount =
                  (ch as any).speakItemCount ??
                  (ch as any).speak_item_count ??
                  10;
                const opts = Array.isArray(ch.challengeOptions)
                  ? ch.challengeOptions
                  : [];
                if (opts.length > targetCount) {
                  console.warn(
                    `[getUnits] SPEAK_SEQUENCE challenge ${ch.id} has ${opts.length} options; trimming to ${targetCount}.`
                  );
                } else if (opts.length < targetCount) {
                  console.warn(
                    `[getUnits] SPEAK_SEQUENCE challenge ${ch.id} has only ${opts.length}/${targetCount} options.`
                  );
                }
                return {
                  ...ch,
                  challengeOptions: opts.slice(0, targetCount),
                };
              }
              return ch;
            })
          : lesson.challenges;

      if (!Array.isArray(fixedChallenges) || fixedChallenges.length === 0) {
        return { ...lesson, challenges: [], completed: false };
      }

      // ✅ Treat non-graded (explainers) as auto-complete for lesson-level completion
      const allCompletedChallenges = fixedChallenges.every((challenge: any) => {
        const isNonGraded = (challenge as any).isNonGraded === true;
        if (isNonGraded) return true;
        return (
          Array.isArray(challenge.challengeProgress) &&
          challenge.challengeProgress.length > 0 &&
          challenge.challengeProgress.every(
            (progress: any) => progress.completed
          )
        );
      });

      return {
        ...lesson,
        challenges: fixedChallenges,
        completed: allCompletedChallenges,
      };
    });
    return { ...unit, lessons: lessonsWithCompletedStatus };
  });

  console.log(
    `[getUnits] Returning ${normalizedData.length} normalized units for courseId: ${courseIdToFetch}.`
  );
  if (normalizedData.length > 0) {
    normalizedData.forEach((unit) => {
      console.log(
        `[getUnits] Normalized Unit ID: ${unit.id}, Title: ${unit.title}, Normalized Lessons count: ${unit.lessons.length}`
      );
      if (unit.lessons.length > 0) {
        unit.lessons.forEach((lesson: any) => {
          console.log(
            `  [getUnits]   Normalized Lesson ID: ${lesson.id}, Title: ${lesson.title}, Completed: ${lesson.completed}`
          );
        });
      } else {
        console.log(
          `  [getUnits]   Normalized Unit ID: ${unit.id} has no lessons after normalization (or originally).`
        );
      }
    });
  }
  return normalizedData;
});

export const getLesson = cache(async (id?: number) => {
  const { userId } = await auth();
  if (!userId) {
    console.log("[getLesson] No userId, returning null.");
    return null;
  }
  const courseProgress = await getCourseProgress();
  const lessonId = id || courseProgress?.activeLessonId;
  if (!lessonId) {
    console.log(
      `[getLesson] No lessonId to fetch (id param: ${id}, activeLessonId: ${courseProgress?.activeLessonId}). Returning null.`
    );
    return null;
  }
  console.log(`[getLesson] Attempting to fetch lesson with ID: ${lessonId}`);
  const data = await db.query.lessons.findFirst({
    where: eq(lessons.id, lessonId),
    with: {
      challenges: {
        orderBy: (challenges, { asc }) => [asc(challenges.order)],
        with: {
          // Ordered options for deterministic Speak Sequence flow
          challengeOptions: {
            orderBy: (co, { asc }) => [asc(co.sequence), asc(co.id)],
          },
          challengeProgress: { where: eq(challengeProgress.userId, userId) },
        },
        columns: {
          id: true,
          lessonId: true,
          type: true,
          question: true,
          order: true,
          audioSrc: true,
          mediaType: true,
          mediaUrl: true,
          topCardText: true,
          topCardAudio: true,
          sentence: true,
          optionsStacked: true,
          // ⭐ include new flags
          isSkippable: true,
          isNonGraded: true,
          // ⭐ include new SPEAK_SEQUENCE configs
          speakItemCount: true as any,
          speakTimeLimitSeconds: true as any,
          speakContinuousCapture: true as any,
          speakAutoAdvanceOnPass: true as any,
          // ▼ include SPEED config (safe for other types)
          speedItemCount: true as any,
          speedTimeLimitSeconds: true as any,
        },
      },
    },
  });
  if (!data) {
    console.warn(`[getLesson] Lesson NOT FOUND for ID: ${lessonId}.`);
    return null;
  }
  if (!data.challenges) {
    console.warn(
      `[getLesson] Lesson ID ${lessonId} found, but it has no challenges array.`
    );
    return { ...data, challenges: [] }; // Return lesson with empty challenges array
  }
  console.log(
    `[getLesson] Lesson ID ${lessonId} found with ${data.challenges.length} challenges.`
  );

  // Soft enforcement for SPEAK_SEQUENCE (trim extras; warn if fewer)
  const fixedForSpeakSequence = (data.challenges as any[]).map((ch: any) => {
    if (ch.type === "SPEAK_SEQUENCE") {
      const targetCount =
        (ch as any).speakItemCount ?? (ch as any).speak_item_count ?? 10;
      const opts = Array.isArray(ch.challengeOptions)
        ? ch.challengeOptions
        : [];
      if (opts.length > targetCount) {
        console.warn(
          `[getLesson] SPEAK_SEQUENCE challenge ${ch.id} has ${opts.length} options; trimming to ${targetCount}.`
        );
      } else if (opts.length < targetCount) {
        console.warn(
          `[getLesson] SPEAK_SEQUENCE challenge ${ch.id} has only ${opts.length}/${targetCount} options.`
        );
      }
      return {
        ...ch,
        challengeOptions: opts.slice(0, targetCount),
      };
    }
    return ch;
  });

  const normalizedChallenges = fixedForSpeakSequence.map((challenge: any) => {
    const isNonGraded = (challenge as any).isNonGraded === true;
    const hasProgress =
      challenge.challengeProgress &&
      challenge.challengeProgress.length > 0 &&
      challenge.challengeProgress.every((progress: any) => progress.completed);

    // ✅ Mark non-graded explainers as completed by definition
    const completed = isNonGraded ? true : !!hasProgress;

    return { ...challenge, completed };
  });
  return { ...data, challenges: normalizedChallenges };
});

export const getLessonPercentage = cache(async () => {
  const courseProgress = await getCourseProgress();
  if (!courseProgress?.activeLessonId) {
    console.log("[getLessonPercentage] No active lesson ID, returning 0%.");
    return 0;
  }
  const lesson = await getLesson(courseProgress.activeLessonId);
  if (!lesson || !lesson.challenges || lesson.challenges.length === 0) {
    console.log(
      `[getLessonPercentage] No lesson data or no challenges for activeLessonId ${courseProgress.activeLessonId}, returning 0%.`
    );
    return 0;
  }

  // Keep existing approach: explainers are already marked completed in getLesson()
  const completedChallenges = lesson.challenges.filter(
    (challenge: any) => challenge.completed
  );
  const percentage = Math.round(
    (completedChallenges.length / lesson.challenges.length) * 100
  );
  console.log(
    `[getLessonPercentage] Calculated for lesson ${lesson.id}: ${completedChallenges.length}/${lesson.challenges.length} challenges completed = ${percentage}%.`
  );
  return percentage;
});

export const getChapterWithSquares = async (chapterId: number) => {
  const chapter = await db.query.chapters.findFirst({
    where: eq(chapters.id, chapterId),
  });
  if (!chapter) return null;
  const squaresData = await db.query.squares.findMany({
    where: eq(squares.chapterId, chapterId),
    orderBy: (squares, { asc }) => [asc(squares.squareNumber)],
    columns: {
      id: true,
      squareNumber: true,
      content: true,
      transliteration: true,
    },
  });
  return { ...chapter, squares: squaresData };
};

export const getChapterWithSquaresByOrder = async (chapterOrder: number) => {
  const chapter = await db.query.chapters.findFirst({
    where: eq(chapters.order, chapterOrder),
  });
  if (!chapter) return null;
  const squaresData = await db.query.squares.findMany({
    where: eq(squares.chapterId, chapter.id),
    orderBy: (squares, { asc }) => [asc(squares.squareNumber)],
    columns: {
      id: true,
      squareNumber: true,
      content: true,
      transliteration: true,
    },
  });
  return { ...chapter, squares: squaresData };
};

export const addStudentToTeacher = async (
  teacherId: string,
  studentId: string
) => {
  console.log("[queries.ts] addStudentToTeacher called with:", {
    teacherId,
    studentId,
  });
  try {
    const inserted = await db
      .insert(teacherStudents)
      .values({ teacherId, studentId })
      .returning();
    console.log("[queries.ts] addStudentToTeacher insertion result:", inserted);
    return inserted[0];
  } catch (error) {
    console.error("[queries.ts] addStudentToTeacher error:", error);
    throw error;
  }
};

export const getStudentsForTeacher = async (teacherId: string) => {
  console.log("[queries.ts] getStudentsForTeacher called with:", teacherId);
  try {
    const list = await db.query.teacherStudents.findMany({
      where: eq(teacherStudents.teacherId, teacherId),
    });
    console.log(
      "[queries.ts] getStudentsForTeacher result length:",
      list.length
    );
    return list;
  } catch (error) {
    console.error("[queries.ts] getStudentsForTeacher error:", error);
    return [];
  }
};

/* ----------------------------------------------------------------
   NEW: SPEED ROUND HELPERS (SPEED_SPOT_MISTAKE and SPEED_MATCH_AUDIO)
------------------------------------------------------------------*/

type SpeedKind = "spot_mistake" | "match_audio";
const typeToSpeedKind = (type: string): SpeedKind =>
  type === "SPEED_SPOT_MISTAKE" ? "spot_mistake" : "match_audio";

/** Minimal challenge config fetcher for speed rounds */
export const getSpeedRoundConfig = cache(async (challengeId: number) => {
  const ch = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
    columns: {
      id: true,
      type: true,
      speedItemCount: true as any,
      speedTimeLimitSeconds: true as any,
      question: true,
      lessonId: true,
    },
  });
  if (!ch) return null;

  // Count items for this challenge so UI can warn if fewer/more than expected.
  const allItems = await db.query.speedItems.findMany({
    where: eq(speedItems.challengeId, challengeId),
    orderBy: (si, { asc }) => [asc(si.sequence), asc(si.id)],
    columns: {
      id: true,
      kind: true as any,
      audioSrc: true,
      audioText: true,
      displayText: true,
      isMatch: true,
      sequence: true,
      active: true,
    },
  });

  const expectedKind = typeToSpeedKind(ch.type as unknown as string);
  const items = allItems.filter(
    (i: any) => i.kind === expectedKind && i.active !== false
  );

  return {
    ...ch,
    itemsAvailable: items.length,
    expectedCount:
      (ch as any).speedItemCount ?? (ch as any).speed_item_count ?? 20,
    timeLimit:
      (ch as any).speedTimeLimitSeconds ??
      (ch as any).speed_time_limit_seconds ??
      60,
  };
});

/** Deck for SPEED_SPOT_MISTAKE: each card has audio + display text + is_match */
export const getSpeedSpotMistakeDeck = cache(async (challengeId: number) => {
  const rows = await db.query.speedItems.findMany({
    where: eq(speedItems.challengeId, challengeId),
    orderBy: (si, { asc }) => [asc(si.sequence), asc(si.id)],
    columns: {
      id: true,
      kind: true as any,
      audioSrc: true,
      audioText: true,
      displayText: true,
      isMatch: true,
      sequence: true,
      active: true,
    },
  });

  // Only this kind + active items
  const deck = rows.filter(
    (r: any) => r.kind === "spot_mistake" && r.active !== false
  );
  return deck;
});

/** Deck for SPEED_MATCH_AUDIO: each card has audio + 4 options (exactly one correct) */
export const getSpeedMatchAudioDeck = cache(async (challengeId: number) => {
  const items = await db.query.speedItems.findMany({
    where: eq(speedItems.challengeId, challengeId),
    orderBy: (si, { asc }) => [asc(si.sequence), asc(si.id)],
    columns: {
      id: true,
      kind: true as any,
      audioSrc: true,
      audioText: true,
      sequence: true,
      active: true,
    },
  });

  const base = items.filter(
    (r: any) => r.kind === "match_audio" && r.active !== false
  );
  if (base.length === 0) return [];

  const ids = base.map((b) => b.id);
  const opts = await db.query.challengeOptions.findMany({
    where: inArray(challengeOptions.speedItemId as any, ids as any),
    orderBy: (co, { asc }) => [asc(co.id)],
    columns: {
      id: true,
      speedItemId: true as any,
      text: true,
      correct: true,
      imageSrc: true,
      audioSrc: true,
    },
  });

  const byItem = new Map<number, any[]>();
  for (const o of opts) {
    const k = Number((o as any).speedItemId);
    if (!byItem.has(k)) byItem.set(k, []);
    byItem.get(k)!.push(o);
  }

  const deck = base.map((row) => {
    const options = byItem.get(row.id) ?? [];
    // Ensure UI has stable shape (even if not exactly 4 yet)
    return {
      id: row.id,
      audioSrc: row.audioSrc,
      audioText: row.audioText,
      sequence: row.sequence,
      options: options.map((o) => ({
        id: o.id,
        text: o.text,
        correct: o.correct,
        imageSrc: o.imageSrc,
        audioSrc: o.audioSrc,
      })),
    };
  });

  return deck;
});

/** Start a speed-round play session (one 60s run). */
export const startSpeedSession = async (
  challengeId: number,
  deviceType?: string
) => {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");
  const inserted = await db
    .insert(challengeSessions)
    .values({
      challengeId,
      userId,
      deviceType: deviceType ?? null,
      startedAt: new Date(),
    } as any)
    .returning();
  return inserted[0];
};

/** Log an attempt for a given item within a session. */
export const recordSpeedAttempt = async (params: {
  sessionId: number;
  speedItemId: number;
  chosen: string; // 'match' | 'mismatch' | 'option_a'|'option_b'|'option_c'|'option_d'
  isCorrect: boolean;
  latencyMs?: number | null;
}) => {
  const row = await db
    .insert(challengeAttempts)
    .values({
      sessionId: params.sessionId,
      speedItemId: params.speedItemId,
      chosen: params.chosen,
      isCorrect: params.isCorrect,
      latencyMs: params.latencyMs ?? null,
      startedAt: new Date(),
      answeredAt: new Date(),
    } as any)
    .returning();
  return row[0];
};

/** End the session and backfill summary stats (score, items, streak) */
export const endSpeedSession = async (sessionId: number) => {
  // pull attempts
  const attempts = await db.query.challengeAttempts.findMany({
    where: eq(challengeAttempts.sessionId, sessionId),
    orderBy: (ca, { asc }) => [asc(ca.id)],
    columns: {
      id: true,
      isCorrect: true,
      latencyMs: true,
      startedAt: true,
      answeredAt: true,
    },
  });

  const itemsSeen = attempts.length;
  const itemsCorrect = attempts.filter((a) => a.isCorrect).length;
  const score = itemsCorrect; // simple: 1 per correct (adjust if you want)

  // compute max streak
  let streak = 0;
  let maxStreak = 0;
  for (const a of attempts) {
    streak = a.isCorrect ? streak + 1 : 0;
    if (streak > maxStreak) maxStreak = streak;
  }

  // duration
  const firstTimestamp = attempts[0]?.startedAt;
  const lastTimestamp = attempts[attempts.length - 1]?.answeredAt;

  const first = firstTimestamp ? new Date(firstTimestamp) : new Date();
  const last = lastTimestamp ? new Date(lastTimestamp) : new Date();
  const durationMs = Math.max(0, last.getTime() - first.getTime());

  const updated = await db
    .update(challengeSessions)
    .set({
      endedAt: new Date(),
      durationMs,
      score,
      itemsSeen,
      itemsCorrect,
      streakMax: maxStreak,
    } as any)
    .where(eq(challengeSessions.id, sessionId))
    .returning();

  return updated[0];
};

/** Read back a session with attempts (for analytics/review) */
export const getSpeedSessionWithAttempts = async (sessionId: number) => {
  const session = await db.query.challengeSessions.findFirst({
    where: eq(challengeSessions.id, sessionId),
  });
  if (!session) return null;

  const attempts = await db.query.challengeAttempts.findMany({
    where: eq(challengeAttempts.sessionId, sessionId),
    orderBy: (ca, { asc }) => [asc(ca.id)],
  });

  return { session, attempts };
};

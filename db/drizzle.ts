// db/drizzle.ts

import postgres from "postgres";
import { drizzle, PostgresJsDatabase } from "drizzle-orm/postgres-js";
import * as schema from "./schema";

// This is the declaration for the global variable.
// We are telling TypeScript that we might have a `drizzle` property on the global object.
declare global {
  var drizzle: PostgresJsDatabase<typeof schema> | undefined;
}

// Ensure the DATABASE_URL is present.
if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is not set.");
}

let db: PostgresJsDatabase<typeof schema>;

// The "globalThis" object is not affected by hot-reloading in development.
// In production, we'll always create a new client.
// In development, we'll check if a client already exists on the global object.
// If it does, we reuse it. If not, we create it and store it for next time.
if (process.env.NODE_ENV === "production") {
  const sql = postgres(process.env.DATABASE_URL);
  db = drizzle(sql, { schema });
} else {
  if (!global.drizzle) {
    const sql = postgres(process.env.DATABASE_URL);
    global.drizzle = drizzle(sql, { schema });
  }
  db = global.drizzle;
}

export default db;

// lib/users.ts

import { auth, clerkClient } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { users } from "@/db/schema";
import { eq } from "drizzle-orm";

type UserRole = "teacher" | "student";

interface UserRecord {
  userId: string;
  email: string;
  role: UserRole;
  displayName?: string;
  createdAt?: Date;
  schoolUsername?: string | null;
  schoolPassword?: string | null;
}

/**
 * Gets an existing user from the DB or creates them as a "student" by default if they don't exist.
 * This is a foundational function for ensuring a user record exists locally.
 */
export async function getOrCreateUser(): Promise<UserRecord | null> {
  console.log("[getOrCreateUser] Function called.");

  const { userId } = auth();

  if (!userId) {
    console.error(
      "[getOrCreateUser] Critical: No userId found. User is not authenticated."
    );
    return null;
  }

  try {
    // First, attempt to find the user in the local database.
    const existing = await db
      .select()
      .from(users)
      .where(eq(users.userId, userId));

    // If the user already exists, return their record immediately.
    if (existing.length > 0) {
      const row = existing[0];
      return {
        userId: row.userId,
        email: row.email,
        role: row.role as UserRole,
        displayName: row.displayName ?? undefined,
        createdAt: row.createdAt ?? undefined,
        schoolUsername: row.schoolUsername ?? null,
        schoolPassword: row.schoolPassword ?? null,
      };
    }

    // If the user does not exist locally, fetch their details from Clerk.
    const clerkUser = await clerkClient.users.getUser(userId);

    const email =
      clerkUser?.emailAddresses?.[0]?.emailAddress || "<EMAIL>";
    const displayName = clerkUser?.fullName || "";

    // Insert the new user into the database with default role 'student'.
    const inserted = await db
      .insert(users)
      .values({ userId, email, role: "student", displayName })
      .returning();

    const [created] = inserted;
    return {
      userId: created.userId,
      email: created.email,
      role: created.role as UserRole,
      displayName: created.displayName ?? undefined,
      createdAt: created.createdAt ?? undefined,
      schoolUsername: created.schoolUsername ?? null,
      schoolPassword: created.schoolPassword ?? null,
    };
  } catch (error) {
    console.error("[getOrCreateUser] An unexpected error occurred:", error);
    // Re-throwing the error allows upstream callers (e.g., Server Components) to handle it.
    throw error;
  }
}

/**
 * Retrieves the current user's role from the DB. Returns null if not found.
 */
export async function getUserRole(): Promise<UserRole | null> {
  console.log("[getUserRole] Function called.");

  const { userId } = auth();

  if (!userId) {
    return null;
  }

  try {
    const rows = await db
      .select({ role: users.role })
      .from(users)
      .where(eq(users.userId, userId));

    if (rows.length === 0) {
      return null;
    }

    return rows[0].role as UserRole;
  } catch (error) {
    console.error("[getUserRole] An unexpected error occurred:", error);
    throw error;
  }
}

/**
 * A simple utility to check if the current authenticated user is a teacher.
 */
export async function isTeacher(): Promise<boolean> {
  try {
    const role = await getUserRole();
    return role === "teacher";
  } catch {
    // If getUserRole throws an error, safely assume the user is not a teacher.
    return false;
  }
}

/**
 * Ensures a user by a known userId exists in the DB.
 * Useful for server-side actions or webhooks where you have a userId but need to confirm their local record.
 */
export async function createUserIfMissing(
  userId: string,
  defaultRole: UserRole = "student"
): Promise<UserRecord | null> {
  console.log("[createUserIfMissing] Called with userId:", userId);

  if (!userId) return null;

  try {
    const existing = await db
      .select()
      .from(users)
      .where(eq(users.userId, userId));

    if (existing.length > 0) {
      const row = existing[0];
      return {
        userId: row.userId,
        email: row.email,
        role: row.role as UserRole,
        displayName: row.displayName ?? undefined,
        createdAt: row.createdAt ?? undefined,
        schoolUsername: row.schoolUsername ?? null,
        schoolPassword: row.schoolPassword ?? null,
      };
    }

    const clerkUser = await clerkClient.users.getUser(userId);
    const email =
      clerkUser?.emailAddresses?.[0]?.emailAddress || "<EMAIL>";
    const displayName = clerkUser?.fullName || "";

    const inserted = await db
      .insert(users)
      .values({ userId, email, role: defaultRole, displayName })
      .returning();

    const [created] = inserted;
    return {
      userId: created.userId,
      email: created.email,
      role: created.role as UserRole,
      displayName: created.displayName ?? undefined,
      createdAt: created.createdAt ?? undefined,
      schoolUsername: created.schoolUsername ?? null,
      schoolPassword: created.schoolPassword ?? null,
    };
  } catch (error) {
    console.error("[createUserIfMissing] An unexpected error occurred:", error);
    // Return null to indicate failure in this context, rather than throwing.
    return null;
  }
}
